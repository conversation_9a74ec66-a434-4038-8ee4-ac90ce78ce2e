# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

version: 2

build:
  os: ubuntu-22.04
  tools:
    python: "3.8"

sphinx:
   configuration: docs/source/conf.py

# If using Sphinx, optionally build your docs in additional formats such as PDF
formats:
   - pdf

# Optionally declare the Python requirements required to build your docs
python:
   install:
   - requirements: docs/requirements-docs.txt
