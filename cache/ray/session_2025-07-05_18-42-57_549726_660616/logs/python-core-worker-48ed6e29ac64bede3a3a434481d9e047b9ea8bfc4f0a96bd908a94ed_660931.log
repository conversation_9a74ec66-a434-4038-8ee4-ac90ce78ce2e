[2025-07-05 18:42:59,478 I 660931 660931] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660931
[2025-07-05 18:42:59,480 I 660931 660931] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,482 I 660931 660931] grpc_server.cc:141: worker server started, listening on port 38687.
[2025-07-05 18:42:59,484 I 660931 660931] core_worker.cc:542: Initializing worker at address: ***********:38687 worker_id=48ed6e29ac64bede3a3a434481d9e047b9ea8bfc4f0a96bd908a94ed node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,485 I 660931 660931] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,486 I 660931 660931] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,486 I 660931 660931] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,486 I 660931 660931] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,486 I 660931 661524] core_worker.cc:902: Event stats:


Global stats: 15 total (10 active)
Queueing time: mean = 17.569 us, max = 168.938 us, min = 9.998 us, total = 263.538 us
Execution time:  mean = 58.294 us, total = 874.404 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 2.897 us, total = 20.281 us, Queueing time: mean = 34.467 us, max = 168.938 us, min = 72.330 us, total = 241.268 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.563 us, total = 20.563 us, Queueing time: mean = 9.998 us, max = 9.998 us, min = 9.998 us, total = 9.998 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 704.300 us, total = 704.300 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 129.260 us, total = 129.260 us, Queueing time: mean = 12.272 us, max = 12.272 us, min = 12.272 us, total = 12.272 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3 total (2 active)
Queueing time: mean = 7.133 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Execution time:  mean = 47.633 us, total = 142.900 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 142.900 us, total = 142.900 us, Queueing time: mean = 21.399 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Other Stats:
	grpc_in_progress:1
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,486 I 660931 660931] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,487 I 660931 661524] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,487 I 660931 661524] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,491 W 660931 661509] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,486 I 660931 661524] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 46.706 us, max = 547.579 us, min = 4.525 us, total = 40.868 ms
Execution time:  mean = 74.946 us, total = 65.577 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.470 us, total = 5.082 ms, Queueing time: mean = 50.978 us, max = 126.200 us, min = 4.525 us, total = 30.587 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.623 us, total = 647.981 us, Queueing time: mean = 48.438 us, max = 82.705 us, min = 25.438 us, total = 2.955 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 732.767 us, total = 43.966 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 169.131 us, total = 10.148 ms, Queueing time: mean = 48.285 us, max = 204.910 us, min = 10.807 us, total = 2.897 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.369 us, total = 1.642 ms, Queueing time: mean = 30.955 us, max = 100.341 us, min = 10.385 us, total = 1.857 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 30.415 us, total = 364.978 us, Queueing time: mean = 25.827 us, max = 66.530 us, min = 16.024 us, total = 309.925 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 70.322 us, total = 492.252 us, Queueing time: mean = 282.719 us, max = 547.579 us, min = 72.330 us, total = 1.979 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.338 us, total = 32.026 us, Queueing time: mean = 41.716 us, max = 67.691 us, min = 35.051 us, total = 250.296 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.049 ms, total = 1.049 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.344 us, total = 60.344 us, Queueing time: mean = 10.707 us, max = 10.707 us, min = 10.707 us, total = 10.707 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 129.260 us, total = 129.260 us, Queueing time: mean = 12.272 us, max = 12.272 us, min = 12.272 us, total = 12.272 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.563 us, total = 20.563 us, Queueing time: mean = 9.998 us, max = 9.998 us, min = 9.998 us, total = 9.998 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 704.300 us, total = 704.300 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.239 ms, total = 1.239 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 55.024 us, max = 2.305 ms, min = 2.886 us, total = 328.107 ms
Execution time:  mean = 15.099 us, total = 90.036 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 15.100 us, total = 90.026 ms, Queueing time: mean = 55.033 us, max = 2.305 ms, min = 9.308 us, total = 328.104 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.899 us, total = 9.899 us, Queueing time: mean = 2.886 us, max = 2.886 us, min = 2.886 us, total = 2.886 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 37.570 us, max = 1.504 ms, min = 12.071 us, total = 6.800 ms
Execution time:  mean = 321.266 us, total = 58.149 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 206.590 us, total = 12.395 ms, Queueing time: mean = 51.761 us, max = 92.297 us, min = 21.870 us, total = 3.106 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 725.276 us, total = 43.517 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 34.903 us, total = 2.094 ms, Queueing time: mean = 61.220 us, max = 1.504 ms, min = 12.071 us, total = 3.673 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 142.900 us, total = 142.900 us, Queueing time: mean = 21.399 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,487 I 660931 661524] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 47.926 us, max = 547.579 us, min = 4.525 us, total = 83.056 ms
Execution time:  mean = 73.264 us, total = 126.967 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.450 us, total = 10.140 ms, Queueing time: mean = 52.686 us, max = 138.355 us, min = 4.525 us, total = 63.223 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 714.338 us, total = 85.721 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 175.876 us, total = 21.105 ms, Queueing time: mean = 50.994 us, max = 204.910 us, min = 10.807 us, total = 6.119 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.107 us, total = 3.493 ms, Queueing time: mean = 36.107 us, max = 100.341 us, min = 10.385 us, total = 4.333 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.705 us, total = 1.285 ms, Queueing time: mean = 50.852 us, max = 123.495 us, min = 15.807 us, total = 6.102 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 33.313 us, total = 799.515 us, Queueing time: mean = 28.660 us, max = 70.633 us, min = 11.760 us, total = 687.834 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.042 us, total = 60.508 us, Queueing time: mean = 46.499 us, max = 156.113 us, min = 13.665 us, total = 557.990 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 70.322 us, total = 492.252 us, Queueing time: mean = 282.719 us, max = 547.579 us, min = 72.330 us, total = 1.979 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 334.911 us, total = 669.821 us, Queueing time: mean = 10.316 us, max = 20.633 us, min = 20.633 us, total = 20.633 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.049 ms, total = 1.049 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.344 us, total = 60.344 us, Queueing time: mean = 10.707 us, max = 10.707 us, min = 10.707 us, total = 10.707 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 129.260 us, total = 129.260 us, Queueing time: mean = 12.272 us, max = 12.272 us, min = 12.272 us, total = 12.272 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.563 us, total = 20.563 us, Queueing time: mean = 9.998 us, max = 9.998 us, min = 9.998 us, total = 9.998 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 704.300 us, total = 704.300 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.239 ms, total = 1.239 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11921 total (1 active)
Queueing time: mean = 57.295 us, max = 2.305 ms, min = 2.886 us, total = 683.016 ms
Execution time:  mean = 15.072 us, total = 179.669 ms
Event stats:
	CoreWorker.CheckSignal - 11920 total (1 active), Execution time: mean = 15.072 us, total = 179.660 ms, Queueing time: mean = 57.300 us, max = 2.305 ms, min = 9.308 us, total = 683.013 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.899 us, total = 9.899 us, Queueing time: mean = 2.886 us, max = 2.886 us, min = 2.886 us, total = 2.886 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 33.918 us, max = 1.504 ms, min = 12.071 us, total = 12.245 ms
Execution time:  mean = 306.213 us, total = 110.543 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 205.918 us, total = 24.710 ms, Queueing time: mean = 50.405 us, max = 92.297 us, min = 21.870 us, total = 6.049 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 678.886 us, total = 81.466 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 35.198 us, total = 4.224 ms, Queueing time: mean = 51.454 us, max = 1.504 ms, min = 12.071 us, total = 6.175 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 142.900 us, total = 142.900 us, Queueing time: mean = 21.399 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,488 I 660931 661524] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 48.586 us, max = 626.814 us, min = 4.525 us, total = 125.886 ms
Execution time:  mean = 72.536 us, total = 187.942 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.430 us, total = 15.166 ms, Queueing time: mean = 53.433 us, max = 138.355 us, min = 4.525 us, total = 96.126 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 706.835 us, total = 127.230 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 176.603 us, total = 31.789 ms, Queueing time: mean = 51.349 us, max = 204.910 us, min = 10.807 us, total = 9.243 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.485 us, total = 5.307 ms, Queueing time: mean = 35.587 us, max = 100.341 us, min = 10.385 us, total = 6.406 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.945 us, total = 1.970 ms, Queueing time: mean = 52.068 us, max = 129.607 us, min = 15.807 us, total = 9.372 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 34.603 us, total = 1.246 ms, Queueing time: mean = 33.630 us, max = 75.262 us, min = 11.760 us, total = 1.211 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.213 us, total = 93.834 us, Queueing time: mean = 81.656 us, max = 626.814 us, min = 13.665 us, total = 1.470 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 70.322 us, total = 492.252 us, Queueing time: mean = 282.719 us, max = 547.579 us, min = 72.330 us, total = 1.979 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 481.659 us, total = 1.445 ms, Queueing time: mean = 15.595 us, max = 26.152 us, min = 20.633 us, total = 46.785 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.049 ms, total = 1.049 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.344 us, total = 60.344 us, Queueing time: mean = 10.707 us, max = 10.707 us, min = 10.707 us, total = 10.707 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 129.260 us, total = 129.260 us, Queueing time: mean = 12.272 us, max = 12.272 us, min = 12.272 us, total = 12.272 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.563 us, total = 20.563 us, Queueing time: mean = 9.998 us, max = 9.998 us, min = 9.998 us, total = 9.998 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 704.300 us, total = 704.300 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.239 ms, total = 1.239 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17883 total (1 active)
Queueing time: mean = 56.008 us, max = 2.305 ms, min = -0.000 s, total = 1.002 s
Execution time:  mean = 15.117 us, total = 270.344 ms
Event stats:
	CoreWorker.CheckSignal - 17882 total (1 active), Execution time: mean = 15.118 us, total = 270.334 ms, Queueing time: mean = 56.011 us, max = 2.305 ms, min = -0.000 s, total = 1.002 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.899 us, total = 9.899 us, Queueing time: mean = 2.886 us, max = 2.886 us, min = 2.886 us, total = 2.886 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 34.384 us, max = 1.504 ms, min = 12.071 us, total = 18.602 ms
Execution time:  mean = 311.641 us, total = 168.598 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 207.198 us, total = 37.296 ms, Queueing time: mean = 54.628 us, max = 109.735 us, min = 21.870 us, total = 9.833 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 693.343 us, total = 124.802 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 35.319 us, total = 6.357 ms, Queueing time: mean = 48.597 us, max = 1.504 ms, min = 12.071 us, total = 8.747 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 142.900 us, total = 142.900 us, Queueing time: mean = 21.399 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,489 I 660931 661524] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 49.595 us, max = 626.814 us, min = 4.525 us, total = 171.102 ms
Execution time:  mean = 72.596 us, total = 250.456 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.401 us, total = 20.154 ms, Queueing time: mean = 54.660 us, max = 138.355 us, min = 4.525 us, total = 131.130 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 708.781 us, total = 170.107 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 178.422 us, total = 42.821 ms, Queueing time: mean = 50.825 us, max = 204.910 us, min = 10.807 us, total = 12.198 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.696 us, total = 7.127 ms, Queueing time: mean = 35.899 us, max = 100.341 us, min = 10.385 us, total = 8.616 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.985 us, total = 2.636 ms, Queueing time: mean = 53.742 us, max = 129.607 us, min = 15.807 us, total = 12.898 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.251 us, total = 1.692 ms, Queueing time: mean = 40.527 us, max = 101.189 us, min = 11.760 us, total = 1.945 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.172 us, total = 124.130 us, Queueing time: mean = 91.126 us, max = 626.814 us, min = 13.665 us, total = 2.187 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 70.322 us, total = 492.252 us, Queueing time: mean = 282.719 us, max = 547.579 us, min = 72.330 us, total = 1.979 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 525.003 us, total = 2.100 ms, Queueing time: mean = 28.784 us, max = 68.352 us, min = 20.633 us, total = 115.137 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.049 ms, total = 1.049 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.344 us, total = 60.344 us, Queueing time: mean = 10.707 us, max = 10.707 us, min = 10.707 us, total = 10.707 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 129.260 us, total = 129.260 us, Queueing time: mean = 12.272 us, max = 12.272 us, min = 12.272 us, total = 12.272 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.563 us, total = 20.563 us, Queueing time: mean = 9.998 us, max = 9.998 us, min = 9.998 us, total = 9.998 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 704.300 us, total = 704.300 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.239 ms, total = 1.239 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23844 total (1 active)
Queueing time: mean = 56.229 us, max = 2.305 ms, min = -0.000 s, total = 1.341 s
Execution time:  mean = 15.066 us, total = 359.233 ms
Event stats:
	CoreWorker.CheckSignal - 23843 total (1 active), Execution time: mean = 15.066 us, total = 359.223 ms, Queueing time: mean = 56.231 us, max = 2.305 ms, min = -0.000 s, total = 1.341 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.899 us, total = 9.899 us, Queueing time: mean = 2.886 us, max = 2.886 us, min = 2.886 us, total = 2.886 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 33.338 us, max = 1.504 ms, min = 12.071 us, total = 24.037 ms
Execution time:  mean = 315.281 us, total = 227.318 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 206.765 us, total = 49.624 ms, Queueing time: mean = 53.695 us, max = 109.735 us, min = 21.870 us, total = 12.887 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 703.784 us, total = 168.908 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.013 us, total = 8.643 ms, Queueing time: mean = 46.368 us, max = 1.504 ms, min = 12.071 us, total = 11.128 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 142.900 us, total = 142.900 us, Queueing time: mean = 21.399 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,489 I 660931 661524] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 50.141 us, max = 626.814 us, min = 4.525 us, total = 216.107 ms
Execution time:  mean = 73.361 us, total = 316.187 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.382 us, total = 25.138 ms, Queueing time: mean = 55.250 us, max = 138.355 us, min = 4.525 us, total = 165.693 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 719.868 us, total = 215.960 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 180.330 us, total = 54.099 ms, Queueing time: mean = 52.366 us, max = 204.910 us, min = 10.807 us, total = 15.710 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.841 us, total = 8.952 ms, Queueing time: mean = 34.857 us, max = 100.341 us, min = 10.385 us, total = 10.457 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.026 us, total = 3.308 ms, Queueing time: mean = 55.031 us, max = 129.607 us, min = 15.807 us, total = 16.509 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.320 us, total = 2.119 ms, Queueing time: mean = 42.771 us, max = 101.189 us, min = 11.760 us, total = 2.566 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.132 us, total = 153.951 us, Queueing time: mean = 96.604 us, max = 626.814 us, min = 13.665 us, total = 2.898 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 70.322 us, total = 492.252 us, Queueing time: mean = 282.719 us, max = 547.579 us, min = 72.330 us, total = 1.979 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 551.348 us, total = 2.757 ms, Queueing time: mean = 38.587 us, max = 77.796 us, min = 20.633 us, total = 192.933 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.985 us, total = 5.970 us, Queueing time: mean = 34.235 us, max = 68.470 us, min = 68.470 us, total = 68.470 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.049 ms, total = 1.049 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.344 us, total = 60.344 us, Queueing time: mean = 10.707 us, max = 10.707 us, min = 10.707 us, total = 10.707 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 129.260 us, total = 129.260 us, Queueing time: mean = 12.272 us, max = 12.272 us, min = 12.272 us, total = 12.272 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.563 us, total = 20.563 us, Queueing time: mean = 9.998 us, max = 9.998 us, min = 9.998 us, total = 9.998 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 704.300 us, total = 704.300 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.239 ms, total = 1.239 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29805 total (1 active)
Queueing time: mean = 55.836 us, max = 2.305 ms, min = -0.000 s, total = 1.664 s
Execution time:  mean = 15.106 us, total = 450.238 ms
Event stats:
	CoreWorker.CheckSignal - 29804 total (1 active), Execution time: mean = 15.106 us, total = 450.228 ms, Queueing time: mean = 55.838 us, max = 2.305 ms, min = -0.000 s, total = 1.664 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.899 us, total = 9.899 us, Queueing time: mean = 2.886 us, max = 2.886 us, min = 2.886 us, total = 2.886 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 32.962 us, max = 1.504 ms, min = 12.071 us, total = 29.698 ms
Execution time:  mean = 315.960 us, total = 284.680 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 205.720 us, total = 61.716 ms, Queueing time: mean = 54.837 us, max = 109.735 us, min = 21.870 us, total = 16.451 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 706.767 us, total = 212.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 35.969 us, total = 10.791 ms, Queueing time: mean = 44.086 us, max = 1.504 ms, min = 12.071 us, total = 13.226 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 142.900 us, total = 142.900 us, Queueing time: mean = 21.399 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,490 I 660931 661524] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.720 us, max = 626.814 us, min = 4.525 us, total = 262.123 ms
Execution time:  mean = 74.422 us, total = 384.611 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.397 us, total = 30.211 ms, Queueing time: mean = 55.879 us, max = 138.355 us, min = 4.525 us, total = 201.051 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 734.661 us, total = 264.478 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 181.341 us, total = 65.283 ms, Queueing time: mean = 54.255 us, max = 204.910 us, min = 10.807 us, total = 19.532 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.774 us, total = 10.719 ms, Queueing time: mean = 33.974 us, max = 100.341 us, min = 10.385 us, total = 12.230 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.012 us, total = 3.964 ms, Queueing time: mean = 55.963 us, max = 129.607 us, min = 15.807 us, total = 20.147 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 36.896 us, total = 2.657 ms, Queueing time: mean = 44.261 us, max = 101.189 us, min = 11.760 us, total = 3.187 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.143 us, total = 185.149 us, Queueing time: mean = 100.786 us, max = 626.814 us, min = 13.665 us, total = 3.628 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 70.322 us, total = 492.252 us, Queueing time: mean = 282.719 us, max = 547.579 us, min = 72.330 us, total = 1.979 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 568.902 us, total = 3.413 ms, Queueing time: mean = 44.489 us, max = 77.796 us, min = 20.633 us, total = 266.932 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.985 us, total = 5.970 us, Queueing time: mean = 34.235 us, max = 68.470 us, min = 68.470 us, total = 68.470 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.049 ms, total = 1.049 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.344 us, total = 60.344 us, Queueing time: mean = 10.707 us, max = 10.707 us, min = 10.707 us, total = 10.707 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 129.260 us, total = 129.260 us, Queueing time: mean = 12.272 us, max = 12.272 us, min = 12.272 us, total = 12.272 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.563 us, total = 20.563 us, Queueing time: mean = 9.998 us, max = 9.998 us, min = 9.998 us, total = 9.998 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 704.300 us, total = 704.300 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.239 ms, total = 1.239 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35765 total (1 active)
Queueing time: mean = 56.085 us, max = 11.218 ms, min = -0.000 s, total = 2.006 s
Execution time:  mean = 15.110 us, total = 540.396 ms
Event stats:
	CoreWorker.CheckSignal - 35764 total (1 active), Execution time: mean = 15.110 us, total = 540.386 ms, Queueing time: mean = 56.086 us, max = 11.218 ms, min = -0.000 s, total = 2.006 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.899 us, total = 9.899 us, Queueing time: mean = 2.886 us, max = 2.886 us, min = 2.886 us, total = 2.886 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 31.999 us, max = 1.504 ms, min = 12.071 us, total = 34.591 ms
Execution time:  mean = 315.868 us, total = 341.453 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 205.677 us, total = 74.044 ms, Queueing time: mean = 53.204 us, max = 109.735 us, min = 21.870 us, total = 19.153 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 706.116 us, total = 254.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.290 us, total = 13.064 ms, Queueing time: mean = 42.824 us, max = 1.504 ms, min = 12.071 us, total = 15.417 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 142.900 us, total = 142.900 us, Queueing time: mean = 21.399 us, max = 21.399 us, min = 21.399 us, total = 21.399 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660931 661524] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660931 661524] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660931 661524] core_worker.cc:5107: Number of alive nodes:0
