[2025-07-05 18:42:59,719 I 660948 660948] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660948
[2025-07-05 18:42:59,720 I 660948 660948] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,722 I 660948 660948] grpc_server.cc:141: worker server started, listening on port 41729.
[2025-07-05 18:42:59,724 I 660948 660948] core_worker.cc:542: Initializing worker at address: ***********:41729 worker_id=0eb5e3f78a4a1fd0e55bbdd1364b3bb34205ecac0402054b43d320bd node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,724 I 660948 660948] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,725 I 660948 660948] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,725 I 660948 660948] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,725 I 660948 660948] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,725 I 660948 660948] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,725 I 660948 662251] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 9.018 us, max = 90.815 us, min = 5.676 us, total = 135.263 us
Execution time:  mean = 67.486 us, total = 1.012 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 3.022 us, total = 21.151 us, Queueing time: mean = 13.784 us, max = 90.815 us, min = 5.676 us, total = 96.491 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 186.832 us, total = 186.832 us, Queueing time: mean = 6.391 us, max = 6.391 us, min = 6.391 us, total = 6.391 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 419.875 us, total = 419.875 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 358.475 us, total = 358.475 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.958 us, total = 25.958 us, Queueing time: mean = 32.381 us, max = 32.381 us, min = 32.381 us, total = 32.381 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 4.259 us, max = 10.628 us, min = 6.408 us, total = 17.036 us
Execution time:  mean = 140.586 us, total = 562.344 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 453.614 us, total = 453.614 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.344 us, total = 98.344 us, Queueing time: mean = 10.628 us, max = 10.628 us, min = 10.628 us, total = 10.628 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 10.386 us, total = 10.386 us, Queueing time: mean = 6.408 us, max = 6.408 us, min = 6.408 us, total = 6.408 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,726 I 660948 662251] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,726 I 660948 662251] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,731 W 660948 662246] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,726 I 660948 662251] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 48.662 us, max = 763.605 us, min = 5.676 us, total = 42.580 ms
Execution time:  mean = 74.000 us, total = 64.750 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.589 us, total = 5.154 ms, Queueing time: mean = 50.563 us, max = 130.380 us, min = 12.312 us, total = 30.338 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.455 us, total = 637.769 us, Queueing time: mean = 50.219 us, max = 123.602 us, min = 16.273 us, total = 3.063 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.726 us, total = 1.664 ms, Queueing time: mean = 37.319 us, max = 116.214 us, min = 10.109 us, total = 2.239 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 743.384 us, total = 44.603 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 171.656 us, total = 10.299 ms, Queueing time: mean = 51.907 us, max = 92.139 us, min = 14.412 us, total = 3.114 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 30.962 us, total = 371.550 us, Queueing time: mean = 39.419 us, max = 75.918 us, min = 7.539 us, total = 473.032 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 93.546 us, total = 654.821 us, Queueing time: mean = 353.247 us, max = 763.605 us, min = 5.676 us, total = 2.473 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.509 us, total = 27.052 us, Queueing time: mean = 50.507 us, max = 76.712 us, min = 40.067 us, total = 303.044 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 280.192 us, total = 280.192 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 186.832 us, total = 186.832 us, Queueing time: mean = 6.391 us, max = 6.391 us, min = 6.391 us, total = 6.391 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 358.475 us, total = 358.475 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 419.875 us, total = 419.875 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.958 us, total = 25.958 us, Queueing time: mean = 32.381 us, max = 32.381 us, min = 32.381 us, total = 32.381 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 67.689 us, total = 67.689 us, Queueing time: mean = 537.134 us, max = 537.134 us, min = 537.134 us, total = 537.134 us

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.691 us, max = 2.330 ms, min = -0.000 s, total = 326.121 ms
Execution time:  mean = 15.099 us, total = 90.037 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 15.101 us, total = 90.029 ms, Queueing time: mean = 54.700 us, max = 2.330 ms, min = -0.000 s, total = 326.119 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.597 us, total = 7.597 us, Queueing time: mean = 2.672 us, max = 2.672 us, min = 2.672 us, total = 2.672 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 31.077 us, max = 118.813 us, min = 6.408 us, total = 5.625 ms
Execution time:  mean = 346.256 us, total = 62.672 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 775.643 us, total = 46.539 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 214.212 us, total = 12.853 ms, Queueing time: mean = 49.402 us, max = 118.813 us, min = 18.862 us, total = 2.964 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 53.045 us, total = 3.183 ms, Queueing time: mean = 44.170 us, max = 85.333 us, min = 6.408 us, total = 2.650 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.344 us, total = 98.344 us, Queueing time: mean = 10.628 us, max = 10.628 us, min = 10.628 us, total = 10.628 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,727 I 660948 662251] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 47.536 us, max = 763.605 us, min = 5.676 us, total = 82.379 ms
Execution time:  mean = 76.338 us, total = 132.294 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.629 us, total = 10.355 ms, Queueing time: mean = 50.857 us, max = 130.380 us, min = 12.312 us, total = 61.029 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.690 us, total = 1.283 ms, Queueing time: mean = 49.762 us, max = 123.602 us, min = 12.585 us, total = 5.971 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 30.520 us, total = 3.662 ms, Queueing time: mean = 38.595 us, max = 116.214 us, min = 10.109 us, total = 4.631 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 763.878 us, total = 91.665 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 181.398 us, total = 21.768 ms, Queueing time: mean = 52.129 us, max = 98.602 us, min = 14.412 us, total = 6.255 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 34.125 us, total = 818.999 us, Queueing time: mean = 35.120 us, max = 75.918 us, min = 7.539 us, total = 842.871 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.664 us, total = 55.963 us, Queueing time: mean = 44.173 us, max = 76.712 us, min = 14.875 us, total = 530.081 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 93.546 us, total = 654.821 us, Queueing time: mean = 353.247 us, max = 763.605 us, min = 5.676 us, total = 2.473 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 346.212 us, total = 692.425 us, Queueing time: mean = 35.273 us, max = 70.546 us, min = 70.546 us, total = 70.546 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 280.192 us, total = 280.192 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 186.832 us, total = 186.832 us, Queueing time: mean = 6.391 us, max = 6.391 us, min = 6.391 us, total = 6.391 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 358.475 us, total = 358.475 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 419.875 us, total = 419.875 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.958 us, total = 25.958 us, Queueing time: mean = 32.381 us, max = 32.381 us, min = 32.381 us, total = 32.381 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 67.689 us, total = 67.689 us, Queueing time: mean = 537.134 us, max = 537.134 us, min = 537.134 us, total = 537.134 us

-----------------
Task execution event stats:

Global stats: 11921 total (1 active)
Queueing time: mean = 57.534 us, max = 2.330 ms, min = -0.000 s, total = 685.857 ms
Execution time:  mean = 15.205 us, total = 181.261 ms
Event stats:
	CoreWorker.CheckSignal - 11920 total (1 active), Execution time: mean = 15.206 us, total = 181.253 ms, Queueing time: mean = 57.538 us, max = 2.330 ms, min = -0.000 s, total = 685.854 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.597 us, total = 7.597 us, Queueing time: mean = 2.672 us, max = 2.672 us, min = 2.672 us, total = 2.672 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 31.426 us, max = 118.813 us, min = 6.408 us, total = 11.345 ms
Execution time:  mean = 345.819 us, total = 124.841 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 782.789 us, total = 93.935 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 211.629 us, total = 25.395 ms, Queueing time: mean = 53.523 us, max = 118.813 us, min = 18.862 us, total = 6.423 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 45.101 us, total = 5.412 ms, Queueing time: mean = 40.929 us, max = 85.333 us, min = 6.408 us, total = 4.911 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.344 us, total = 98.344 us, Queueing time: mean = 10.628 us, max = 10.628 us, min = 10.628 us, total = 10.628 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,727 I 660948 662251] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 48.893 us, max = 763.605 us, min = 5.676 us, total = 126.683 ms
Execution time:  mean = 76.359 us, total = 197.847 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.562 us, total = 15.402 ms, Queueing time: mean = 52.530 us, max = 130.380 us, min = 12.312 us, total = 94.502 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.736 us, total = 1.932 ms, Queueing time: mean = 51.800 us, max = 123.602 us, min = 12.585 us, total = 9.324 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.657 us, total = 5.338 ms, Queueing time: mean = 40.234 us, max = 116.214 us, min = 10.109 us, total = 7.242 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 767.159 us, total = 138.089 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 179.322 us, total = 32.278 ms, Queueing time: mean = 53.125 us, max = 98.602 us, min = 14.412 us, total = 9.563 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.380 us, total = 1.274 ms, Queueing time: mean = 44.562 us, max = 80.817 us, min = 7.539 us, total = 1.604 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.914 us, total = 88.454 us, Queueing time: mean = 72.259 us, max = 571.033 us, min = 14.875 us, total = 1.301 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 93.546 us, total = 654.821 us, Queueing time: mean = 353.247 us, max = 763.605 us, min = 5.676 us, total = 2.473 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 483.765 us, total = 1.451 ms, Queueing time: mean = 33.031 us, max = 70.546 us, min = 28.546 us, total = 99.092 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 280.192 us, total = 280.192 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 186.832 us, total = 186.832 us, Queueing time: mean = 6.391 us, max = 6.391 us, min = 6.391 us, total = 6.391 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 358.475 us, total = 358.475 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 419.875 us, total = 419.875 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.958 us, total = 25.958 us, Queueing time: mean = 32.381 us, max = 32.381 us, min = 32.381 us, total = 32.381 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 67.689 us, total = 67.689 us, Queueing time: mean = 537.134 us, max = 537.134 us, min = 537.134 us, total = 537.134 us

-----------------
Task execution event stats:

Global stats: 17884 total (1 active)
Queueing time: mean = 55.259 us, max = 2.330 ms, min = -0.000 s, total = 988.252 ms
Execution time:  mean = 15.281 us, total = 273.290 ms
Event stats:
	CoreWorker.CheckSignal - 17883 total (1 active), Execution time: mean = 15.282 us, total = 273.283 ms, Queueing time: mean = 55.262 us, max = 2.330 ms, min = -0.000 s, total = 988.250 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.597 us, total = 7.597 us, Queueing time: mean = 2.672 us, max = 2.672 us, min = 2.672 us, total = 2.672 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 32.377 us, max = 118.813 us, min = 6.408 us, total = 17.516 ms
Execution time:  mean = 348.666 us, total = 188.628 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 790.850 us, total = 142.353 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 212.737 us, total = 38.293 ms, Queueing time: mean = 53.970 us, max = 118.813 us, min = 18.862 us, total = 9.715 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 43.801 us, total = 7.884 ms, Queueing time: mean = 43.282 us, max = 85.333 us, min = 6.408 us, total = 7.791 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.344 us, total = 98.344 us, Queueing time: mean = 10.628 us, max = 10.628 us, min = 10.628 us, total = 10.628 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,728 I 660948 662251] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 48.999 us, max = 763.605 us, min = 5.676 us, total = 169.047 ms
Execution time:  mean = 76.610 us, total = 264.304 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.478 us, total = 20.339 ms, Queueing time: mean = 52.590 us, max = 675.373 us, min = 12.312 us, total = 126.164 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.710 us, total = 2.570 ms, Queueing time: mean = 52.719 us, max = 123.602 us, min = 12.585 us, total = 12.653 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.783 us, total = 7.148 ms, Queueing time: mean = 41.479 us, max = 116.214 us, min = 10.109 us, total = 9.955 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 770.884 us, total = 185.012 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 180.503 us, total = 43.321 ms, Queueing time: mean = 53.840 us, max = 99.868 us, min = 14.412 us, total = 12.922 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.449 us, total = 1.702 ms, Queueing time: mean = 44.725 us, max = 80.817 us, min = 7.539 us, total = 2.147 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 4.880 us, total = 117.113 us, Queueing time: mean = 84.564 us, max = 571.033 us, min = 14.875 us, total = 2.030 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 93.546 us, total = 654.821 us, Queueing time: mean = 353.247 us, max = 763.605 us, min = 5.676 us, total = 2.473 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 525.403 us, total = 2.102 ms, Queueing time: mean = 32.107 us, max = 70.546 us, min = 28.546 us, total = 128.427 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 280.192 us, total = 280.192 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 186.832 us, total = 186.832 us, Queueing time: mean = 6.391 us, max = 6.391 us, min = 6.391 us, total = 6.391 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 358.475 us, total = 358.475 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 419.875 us, total = 419.875 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.958 us, total = 25.958 us, Queueing time: mean = 32.381 us, max = 32.381 us, min = 32.381 us, total = 32.381 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 67.689 us, total = 67.689 us, Queueing time: mean = 537.134 us, max = 537.134 us, min = 537.134 us, total = 537.134 us

-----------------
Task execution event stats:

Global stats: 23848 total (1 active)
Queueing time: mean = 54.342 us, max = 2.330 ms, min = -0.000 s, total = 1.296 s
Execution time:  mean = 15.242 us, total = 363.486 ms
Event stats:
	CoreWorker.CheckSignal - 23847 total (1 active), Execution time: mean = 15.242 us, total = 363.479 ms, Queueing time: mean = 54.344 us, max = 2.330 ms, min = -0.000 s, total = 1.296 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.597 us, total = 7.597 us, Queueing time: mean = 2.672 us, max = 2.672 us, min = 2.672 us, total = 2.672 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 33.191 us, max = 118.813 us, min = 6.408 us, total = 23.931 ms
Execution time:  mean = 345.448 us, total = 249.068 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 782.958 us, total = 187.910 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 211.791 us, total = 50.830 ms, Queueing time: mean = 55.411 us, max = 118.813 us, min = 18.862 us, total = 13.299 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 42.625 us, total = 10.230 ms, Queueing time: mean = 44.257 us, max = 85.333 us, min = 6.408 us, total = 10.622 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.344 us, total = 98.344 us, Queueing time: mean = 10.628 us, max = 10.628 us, min = 10.628 us, total = 10.628 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,729 I 660948 662251] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 49.129 us, max = 763.605 us, min = 5.676 us, total = 211.744 ms
Execution time:  mean = 77.194 us, total = 332.707 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.486 us, total = 25.448 ms, Queueing time: mean = 52.645 us, max = 675.373 us, min = 12.312 us, total = 157.881 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.603 us, total = 9.181 ms, Queueing time: mean = 42.678 us, max = 116.214 us, min = 10.109 us, total = 12.803 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.793 us, total = 3.238 ms, Queueing time: mean = 52.941 us, max = 123.602 us, min = 12.585 us, total = 15.882 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 776.704 us, total = 233.011 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 182.560 us, total = 54.768 ms, Queueing time: mean = 54.420 us, max = 99.868 us, min = 14.412 us, total = 16.326 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.951 us, total = 2.157 ms, Queueing time: mean = 47.625 us, max = 80.817 us, min = 7.539 us, total = 2.858 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 4.982 us, total = 149.455 us, Queueing time: mean = 89.274 us, max = 571.033 us, min = 14.875 us, total = 2.678 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 93.546 us, total = 654.821 us, Queueing time: mean = 353.247 us, max = 763.605 us, min = 5.676 us, total = 2.473 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 550.946 us, total = 2.755 ms, Queueing time: mean = 34.204 us, max = 70.546 us, min = 28.546 us, total = 171.018 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.753 us, total = 5.507 us, Queueing time: mean = 47.877 us, max = 95.753 us, min = 95.753 us, total = 95.753 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 280.192 us, total = 280.192 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 186.832 us, total = 186.832 us, Queueing time: mean = 6.391 us, max = 6.391 us, min = 6.391 us, total = 6.391 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 358.475 us, total = 358.475 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 419.875 us, total = 419.875 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.958 us, total = 25.958 us, Queueing time: mean = 32.381 us, max = 32.381 us, min = 32.381 us, total = 32.381 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 67.689 us, total = 67.689 us, Queueing time: mean = 537.134 us, max = 537.134 us, min = 537.134 us, total = 537.134 us

-----------------
Task execution event stats:

Global stats: 29811 total (1 active)
Queueing time: mean = 53.847 us, max = 2.330 ms, min = -0.000 s, total = 1.605 s
Execution time:  mean = 15.221 us, total = 453.752 ms
Event stats:
	CoreWorker.CheckSignal - 29810 total (1 active), Execution time: mean = 15.221 us, total = 453.744 ms, Queueing time: mean = 53.849 us, max = 2.330 ms, min = -0.000 s, total = 1.605 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.597 us, total = 7.597 us, Queueing time: mean = 2.672 us, max = 2.672 us, min = 2.672 us, total = 2.672 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 33.649 us, max = 118.813 us, min = 6.408 us, total = 30.318 ms
Execution time:  mean = 342.218 us, total = 308.339 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 776.029 us, total = 232.809 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 209.992 us, total = 62.997 ms, Queueing time: mean = 56.690 us, max = 118.813 us, min = 18.862 us, total = 17.007 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 41.448 us, total = 12.434 ms, Queueing time: mean = 44.333 us, max = 85.333 us, min = 6.408 us, total = 13.300 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.344 us, total = 98.344 us, Queueing time: mean = 10.628 us, max = 10.628 us, min = 10.628 us, total = 10.628 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,729 I 660948 662251] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 49.712 us, max = 763.605 us, min = 5.676 us, total = 256.913 ms
Execution time:  mean = 77.243 us, total = 399.190 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.520 us, total = 30.654 ms, Queueing time: mean = 53.239 us, max = 675.373 us, min = 12.312 us, total = 191.554 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.607 us, total = 11.019 ms, Queueing time: mean = 42.961 us, max = 116.214 us, min = 10.109 us, total = 15.466 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.031 us, total = 3.971 ms, Queueing time: mean = 53.904 us, max = 123.602 us, min = 12.585 us, total = 19.405 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 777.711 us, total = 279.976 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 181.663 us, total = 65.399 ms, Queueing time: mean = 56.118 us, max = 123.231 us, min = 14.412 us, total = 20.203 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.790 us, total = 2.577 ms, Queueing time: mean = 47.616 us, max = 80.817 us, min = 7.539 us, total = 3.428 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.055 us, total = 181.964 us, Queueing time: mean = 96.301 us, max = 571.033 us, min = 14.875 us, total = 3.467 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 93.546 us, total = 654.821 us, Queueing time: mean = 353.247 us, max = 763.605 us, min = 5.676 us, total = 2.473 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 568.779 us, total = 3.413 ms, Queueing time: mean = 40.921 us, max = 74.508 us, min = 28.546 us, total = 245.526 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.753 us, total = 5.507 us, Queueing time: mean = 47.877 us, max = 95.753 us, min = 95.753 us, total = 95.753 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 280.192 us, total = 280.192 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 186.832 us, total = 186.832 us, Queueing time: mean = 6.391 us, max = 6.391 us, min = 6.391 us, total = 6.391 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 358.475 us, total = 358.475 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 419.875 us, total = 419.875 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.958 us, total = 25.958 us, Queueing time: mean = 32.381 us, max = 32.381 us, min = 32.381 us, total = 32.381 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 67.689 us, total = 67.689 us, Queueing time: mean = 537.134 us, max = 537.134 us, min = 537.134 us, total = 537.134 us

-----------------
Task execution event stats:

Global stats: 35772 total (1 active)
Queueing time: mean = 53.853 us, max = 2.397 ms, min = -0.000 s, total = 1.926 s
Execution time:  mean = 15.234 us, total = 544.967 ms
Event stats:
	CoreWorker.CheckSignal - 35771 total (1 active), Execution time: mean = 15.235 us, total = 544.959 ms, Queueing time: mean = 53.855 us, max = 2.397 ms, min = -0.000 s, total = 1.926 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.597 us, total = 7.597 us, Queueing time: mean = 2.672 us, max = 2.672 us, min = 2.672 us, total = 2.672 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 33.794 us, max = 153.916 us, min = 6.408 us, total = 36.531 ms
Execution time:  mean = 338.413 us, total = 365.824 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 767.678 us, total = 276.364 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 207.735 us, total = 74.784 ms, Queueing time: mean = 56.979 us, max = 153.916 us, min = 18.862 us, total = 20.512 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 40.493 us, total = 14.577 ms, Queueing time: mean = 44.467 us, max = 85.333 us, min = 6.408 us, total = 16.008 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.344 us, total = 98.344 us, Queueing time: mean = 10.628 us, max = 10.628 us, min = 10.628 us, total = 10.628 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660948 662251] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660948 662251] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660948 662251] core_worker.cc:5107: Number of alive nodes:0
[2025-07-05 18:49:08,879 I 660948 662251] raylet_client.cc:281: Error reporting task backlog information: RpcError: RPC Error message: Cancelling all calls; RPC Error details:  rpc_code: 14
