[2025-07-05 18:42:59,489 I 660938 660938] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660938
[2025-07-05 18:42:59,491 I 660938 660938] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,493 I 660938 660938] grpc_server.cc:141: worker server started, listening on port 38199.
[2025-07-05 18:42:59,494 I 660938 660938] core_worker.cc:542: Initializing worker at address: ***********:38199 worker_id=b5d8182a0988f080eb277c5e2d0ecb8fef3e7403ad2d4e05759ceb94 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,495 I 660938 660938] task_event_buffer.cc:287: Reporting task events to <PERSON><PERSON> every 1000ms.
[2025-07-05 18:42:59,497 I 660938 661720] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,497 I 660938 660938] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,497 I 660938 661720] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,497 I 660938 661720] core_worker.cc:902: Event stats:


Global stats: 16 total (8 active)
Queueing time: mean = 43.697 us, max = 581.177 us, min = 8.529 us, total = 699.147 us
Execution time:  mean = 124.929 us, total = 1.999 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 3.919 us, total = 27.431 us, Queueing time: mean = 13.705 us, max = 78.625 us, min = 17.312 us, total = 95.937 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 23.013 us, total = 23.013 us, Queueing time: mean = 13.504 us, max = 13.504 us, min = 13.504 us, total = 13.504 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 74.081 us, total = 74.081 us, Queueing time: mean = 581.177 us, max = 581.177 us, min = 581.177 us, total = 581.177 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 84.198 us, total = 84.198 us, Queueing time: mean = 8.529 us, max = 8.529 us, min = 8.529 us, total = 8.529 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 411.020 us, total = 411.020 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 798.889 us, total = 798.889 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 580.231 us, total = 580.231 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 67.069 us, max = 258.607 us, min = 9.669 us, total = 268.276 us
Execution time:  mean = 157.366 us, total = 629.463 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 258.804 us, total = 258.804 us, Queueing time: mean = 258.607 us, max = 258.607 us, min = 258.607 us, total = 258.607 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.245 us, total = 13.245 us, Queueing time: mean = 9.669 us, max = 9.669 us, min = 9.669 us, total = 9.669 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 357.414 us, total = 357.414 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,497 I 660938 660938] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,497 I 660938 660938] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,497 I 660938 660938] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,501 W 660938 661694] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,498 I 660938 661720] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 54.124 us, max = 652.171 us, min = 8.529 us, total = 47.358 ms
Execution time:  mean = 73.512 us, total = 64.323 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.400 us, total = 5.040 ms, Queueing time: mean = 58.858 us, max = 98.151 us, min = 14.501 us, total = 35.315 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.500 us, total = 640.483 us, Queueing time: mean = 52.725 us, max = 108.181 us, min = 18.410 us, total = 3.216 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.360 us, total = 1.642 ms, Queueing time: mean = 39.615 us, max = 76.267 us, min = 10.395 us, total = 2.377 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 164.737 us, total = 9.884 ms, Queueing time: mean = 46.228 us, max = 99.874 us, min = 11.481 us, total = 2.774 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 736.710 us, total = 44.203 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 32.058 us, total = 384.694 us, Queueing time: mean = 30.451 us, max = 72.764 us, min = 13.492 us, total = 365.406 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 75.003 us, total = 525.019 us, Queueing time: mean = 345.038 us, max = 652.171 us, min = 17.312 us, total = 2.415 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.424 us, total = 32.543 us, Queueing time: mean = 48.791 us, max = 75.266 us, min = 28.285 us, total = 292.749 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 84.198 us, total = 84.198 us, Queueing time: mean = 8.529 us, max = 8.529 us, min = 8.529 us, total = 8.529 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 411.020 us, total = 411.020 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 580.231 us, total = 580.231 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 74.081 us, total = 74.081 us, Queueing time: mean = 581.177 us, max = 581.177 us, min = 581.177 us, total = 581.177 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 23.013 us, total = 23.013 us, Queueing time: mean = 13.504 us, max = 13.504 us, min = 13.504 us, total = 13.504 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 798.889 us, total = 798.889 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.014 us, max = 2.129 ms, min = 2.416 us, total = 322.084 ms
Execution time:  mean = 15.097 us, total = 90.021 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 15.098 us, total = 90.012 ms, Queueing time: mean = 54.022 us, max = 2.129 ms, min = 2.416 us, total = 322.081 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.604 us, total = 8.604 us, Queueing time: mean = 2.978 us, max = 2.978 us, min = 2.978 us, total = 2.978 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 31.879 us, max = 258.607 us, min = 9.669 us, total = 5.770 ms
Execution time:  mean = 306.886 us, total = 55.546 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 190.868 us, total = 11.452 ms, Queueing time: mean = 52.473 us, max = 83.954 us, min = 20.595 us, total = 3.148 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 33.017 us, total = 1.981 ms, Queueing time: mean = 39.384 us, max = 90.104 us, min = 9.669 us, total = 2.363 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 697.574 us, total = 41.854 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 258.804 us, total = 258.804 us, Queueing time: mean = 258.607 us, max = 258.607 us, min = 258.607 us, total = 258.607 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,498 I 660938 661720] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 53.702 us, max = 652.171 us, min = 8.529 us, total = 93.066 ms
Execution time:  mean = 73.809 us, total = 127.911 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.416 us, total = 10.100 ms, Queueing time: mean = 59.222 us, max = 110.344 us, min = 14.501 us, total = 71.067 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.473 us, total = 1.257 ms, Queueing time: mean = 55.736 us, max = 108.181 us, min = 15.460 us, total = 6.688 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.851 us, total = 3.462 ms, Queueing time: mean = 43.640 us, max = 76.267 us, min = 10.395 us, total = 5.237 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 169.570 us, total = 20.348 ms, Queueing time: mean = 46.001 us, max = 99.874 us, min = 11.481 us, total = 5.520 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 738.815 us, total = 88.658 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 34.485 us, total = 827.640 us, Queueing time: mean = 33.938 us, max = 77.160 us, min = 12.618 us, total = 814.511 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.047 us, total = 60.560 us, Queueing time: mean = 57.335 us, max = 142.792 us, min = 17.171 us, total = 688.017 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 75.003 us, total = 525.019 us, Queueing time: mean = 345.038 us, max = 652.171 us, min = 17.312 us, total = 2.415 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 350.640 us, total = 701.281 us, Queueing time: mean = 16.375 us, max = 32.750 us, min = 32.750 us, total = 32.750 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 84.198 us, total = 84.198 us, Queueing time: mean = 8.529 us, max = 8.529 us, min = 8.529 us, total = 8.529 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 798.889 us, total = 798.889 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 411.020 us, total = 411.020 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 580.231 us, total = 580.231 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 74.081 us, total = 74.081 us, Queueing time: mean = 581.177 us, max = 581.177 us, min = 581.177 us, total = 581.177 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 23.013 us, total = 23.013 us, Queueing time: mean = 13.504 us, max = 13.504 us, min = 13.504 us, total = 13.504 us

-----------------
Task execution event stats:

Global stats: 11927 total (1 active)
Queueing time: mean = 52.674 us, max = 2.129 ms, min = -0.000 s, total = 628.241 ms
Execution time:  mean = 15.100 us, total = 180.095 ms
Event stats:
	CoreWorker.CheckSignal - 11926 total (1 active), Execution time: mean = 15.100 us, total = 180.087 ms, Queueing time: mean = 52.678 us, max = 2.129 ms, min = -0.000 s, total = 628.238 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.604 us, total = 8.604 us, Queueing time: mean = 2.978 us, max = 2.978 us, min = 2.978 us, total = 2.978 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 32.058 us, max = 258.607 us, min = 9.669 us, total = 11.573 ms
Execution time:  mean = 314.017 us, total = 113.360 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 194.127 us, total = 23.295 ms, Queueing time: mean = 53.605 us, max = 105.455 us, min = 20.595 us, total = 6.433 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.216 us, total = 4.106 ms, Queueing time: mean = 40.681 us, max = 90.104 us, min = 9.669 us, total = 4.882 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 714.169 us, total = 85.700 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 258.804 us, total = 258.804 us, Queueing time: mean = 258.607 us, max = 258.607 us, min = 258.607 us, total = 258.607 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,499 I 660938 661720] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 51.481 us, max = 652.171 us, min = 8.529 us, total = 133.387 ms
Execution time:  mean = 74.480 us, total = 192.977 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.436 us, total = 15.176 ms, Queueing time: mean = 56.589 us, max = 120.766 us, min = 14.501 us, total = 101.804 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.770 us, total = 1.939 ms, Queueing time: mean = 53.643 us, max = 109.414 us, min = 12.619 us, total = 9.656 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 28.992 us, total = 5.219 ms, Queueing time: mean = 41.859 us, max = 76.267 us, min = 10.395 us, total = 7.535 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 175.069 us, total = 31.512 ms, Queueing time: mean = 47.768 us, max = 590.655 us, min = 11.481 us, total = 8.598 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 743.153 us, total = 133.768 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 36.511 us, total = 1.314 ms, Queueing time: mean = 36.718 us, max = 84.710 us, min = 12.618 us, total = 1.322 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.048 us, total = 90.863 us, Queueing time: mean = 77.008 us, max = 520.300 us, min = 17.171 us, total = 1.386 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 75.003 us, total = 525.019 us, Queueing time: mean = 345.038 us, max = 652.171 us, min = 17.312 us, total = 2.415 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 487.600 us, total = 1.463 ms, Queueing time: mean = 22.463 us, max = 34.638 us, min = 32.750 us, total = 67.388 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 84.198 us, total = 84.198 us, Queueing time: mean = 8.529 us, max = 8.529 us, min = 8.529 us, total = 8.529 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 798.889 us, total = 798.889 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 411.020 us, total = 411.020 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 580.231 us, total = 580.231 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 74.081 us, total = 74.081 us, Queueing time: mean = 581.177 us, max = 581.177 us, min = 581.177 us, total = 581.177 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 23.013 us, total = 23.013 us, Queueing time: mean = 13.504 us, max = 13.504 us, min = 13.504 us, total = 13.504 us

-----------------
Task execution event stats:

Global stats: 17891 total (1 active)
Queueing time: mean = 51.721 us, max = 2.129 ms, min = -0.000 s, total = 925.348 ms
Execution time:  mean = 15.159 us, total = 271.214 ms
Event stats:
	CoreWorker.CheckSignal - 17890 total (1 active), Execution time: mean = 15.160 us, total = 271.205 ms, Queueing time: mean = 51.724 us, max = 2.129 ms, min = -0.000 s, total = 925.345 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.604 us, total = 8.604 us, Queueing time: mean = 2.978 us, max = 2.978 us, min = 2.978 us, total = 2.978 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 30.657 us, max = 258.607 us, min = 9.669 us, total = 16.585 ms
Execution time:  mean = 316.360 us, total = 171.151 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 195.231 us, total = 35.142 ms, Queueing time: mean = 49.212 us, max = 105.455 us, min = 20.595 us, total = 8.858 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 34.649 us, total = 6.237 ms, Queueing time: mean = 41.492 us, max = 90.104 us, min = 9.669 us, total = 7.469 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 719.520 us, total = 129.514 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 258.804 us, total = 258.804 us, Queueing time: mean = 258.607 us, max = 258.607 us, min = 258.607 us, total = 258.607 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,500 I 660938 661720] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 49.934 us, max = 652.171 us, min = 5.729 us, total = 172.274 ms
Execution time:  mean = 74.586 us, total = 257.322 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.364 us, total = 20.066 ms, Queueing time: mean = 54.549 us, max = 124.965 us, min = 14.501 us, total = 130.864 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.926 us, total = 2.622 ms, Queueing time: mean = 53.820 us, max = 109.414 us, min = 12.619 us, total = 12.917 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.198 us, total = 7.008 ms, Queueing time: mean = 42.327 us, max = 168.158 us, min = 10.395 us, total = 10.158 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 177.194 us, total = 42.527 ms, Queueing time: mean = 46.212 us, max = 590.655 us, min = 5.729 us, total = 11.091 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 744.042 us, total = 178.570 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 37.352 us, total = 1.793 ms, Queueing time: mean = 41.322 us, max = 84.710 us, min = 12.618 us, total = 1.983 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.060 us, total = 121.429 us, Queueing time: mean = 89.040 us, max = 520.300 us, min = 17.171 us, total = 2.137 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 75.003 us, total = 525.019 us, Queueing time: mean = 345.038 us, max = 652.171 us, min = 17.312 us, total = 2.415 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 529.683 us, total = 2.119 ms, Queueing time: mean = 26.132 us, max = 37.141 us, min = 32.750 us, total = 104.529 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 84.198 us, total = 84.198 us, Queueing time: mean = 8.529 us, max = 8.529 us, min = 8.529 us, total = 8.529 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 798.889 us, total = 798.889 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 411.020 us, total = 411.020 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 580.231 us, total = 580.231 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 74.081 us, total = 74.081 us, Queueing time: mean = 581.177 us, max = 581.177 us, min = 581.177 us, total = 581.177 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 23.013 us, total = 23.013 us, Queueing time: mean = 13.504 us, max = 13.504 us, min = 13.504 us, total = 13.504 us

-----------------
Task execution event stats:

Global stats: 23854 total (1 active)
Queueing time: mean = 51.479 us, max = 2.129 ms, min = -0.000 s, total = 1.228 s
Execution time:  mean = 15.249 us, total = 363.740 ms
Event stats:
	CoreWorker.CheckSignal - 23853 total (1 active), Execution time: mean = 15.249 us, total = 363.732 ms, Queueing time: mean = 51.481 us, max = 2.129 ms, min = -0.000 s, total = 1.228 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.604 us, total = 8.604 us, Queueing time: mean = 2.978 us, max = 2.978 us, min = 2.978 us, total = 2.978 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 31.326 us, max = 258.607 us, min = 9.669 us, total = 22.586 ms
Execution time:  mean = 315.984 us, total = 227.825 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 196.951 us, total = 47.268 ms, Queueing time: mean = 51.211 us, max = 105.455 us, min = 20.595 us, total = 12.291 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 35.016 us, total = 8.404 ms, Queueing time: mean = 41.819 us, max = 90.104 us, min = 9.669 us, total = 10.037 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 716.225 us, total = 171.894 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 258.804 us, total = 258.804 us, Queueing time: mean = 258.607 us, max = 258.607 us, min = 258.607 us, total = 258.607 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,501 I 660938 661720] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 50.149 us, max = 652.171 us, min = 5.729 us, total = 216.141 ms
Execution time:  mean = 74.096 us, total = 319.352 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.358 us, total = 25.066 ms, Queueing time: mean = 54.837 us, max = 128.166 us, min = 14.501 us, total = 164.457 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.948 us, total = 3.284 ms, Queueing time: mean = 54.949 us, max = 110.872 us, min = 12.619 us, total = 16.485 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.508 us, total = 8.852 ms, Queueing time: mean = 41.334 us, max = 168.158 us, min = 10.395 us, total = 12.400 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 177.702 us, total = 53.311 ms, Queueing time: mean = 46.222 us, max = 590.655 us, min = 5.729 us, total = 13.867 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 736.911 us, total = 221.073 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 37.597 us, total = 2.256 ms, Queueing time: mean = 45.424 us, max = 84.710 us, min = 12.618 us, total = 2.725 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.205 us, total = 156.155 us, Queueing time: mean = 98.328 us, max = 520.300 us, min = 17.171 us, total = 2.950 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 75.003 us, total = 525.019 us, Queueing time: mean = 345.038 us, max = 652.171 us, min = 17.312 us, total = 2.415 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 564.623 us, total = 2.823 ms, Queueing time: mean = 34.808 us, max = 69.512 us, min = 32.750 us, total = 174.041 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 17.017 us, total = 34.034 us, Queueing time: mean = 32.473 us, max = 64.946 us, min = 64.946 us, total = 64.946 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 84.198 us, total = 84.198 us, Queueing time: mean = 8.529 us, max = 8.529 us, min = 8.529 us, total = 8.529 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 798.889 us, total = 798.889 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 411.020 us, total = 411.020 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 580.231 us, total = 580.231 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 74.081 us, total = 74.081 us, Queueing time: mean = 581.177 us, max = 581.177 us, min = 581.177 us, total = 581.177 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 23.013 us, total = 23.013 us, Queueing time: mean = 13.504 us, max = 13.504 us, min = 13.504 us, total = 13.504 us

-----------------
Task execution event stats:

Global stats: 29817 total (1 active)
Queueing time: mean = 51.632 us, max = 2.129 ms, min = -0.000 s, total = 1.540 s
Execution time:  mean = 15.269 us, total = 455.283 ms
Event stats:
	CoreWorker.CheckSignal - 29816 total (1 active), Execution time: mean = 15.269 us, total = 455.274 ms, Queueing time: mean = 51.633 us, max = 2.129 ms, min = -0.000 s, total = 1.540 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.604 us, total = 8.604 us, Queueing time: mean = 2.978 us, max = 2.978 us, min = 2.978 us, total = 2.978 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 31.298 us, max = 258.607 us, min = 9.669 us, total = 28.200 ms
Execution time:  mean = 313.341 us, total = 282.321 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 196.551 us, total = 58.965 ms, Queueing time: mean = 52.466 us, max = 105.455 us, min = 20.595 us, total = 15.740 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 35.100 us, total = 10.530 ms, Queueing time: mean = 40.671 us, max = 90.104 us, min = 9.669 us, total = 12.201 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 708.555 us, total = 212.566 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 258.804 us, total = 258.804 us, Queueing time: mean = 258.607 us, max = 258.607 us, min = 258.607 us, total = 258.607 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,501 I 660938 661720] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.592 us, max = 652.171 us, min = 5.729 us, total = 261.457 ms
Execution time:  mean = 73.501 us, total = 379.853 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.336 us, total = 29.993 ms, Queueing time: mean = 55.414 us, max = 128.166 us, min = 14.501 us, total = 199.378 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.248 us, total = 4.049 ms, Queueing time: mean = 55.884 us, max = 110.872 us, min = 12.619 us, total = 20.118 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.230 us, total = 10.523 ms, Queueing time: mean = 40.867 us, max = 168.158 us, min = 10.395 us, total = 14.712 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 176.462 us, total = 63.526 ms, Queueing time: mean = 47.349 us, max = 590.655 us, min = 5.729 us, total = 17.046 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 730.150 us, total = 262.854 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 37.328 us, total = 2.688 ms, Queueing time: mean = 44.074 us, max = 84.710 us, min = 12.618 us, total = 3.173 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.206 us, total = 187.411 us, Queueing time: mean = 102.885 us, max = 520.300 us, min = 17.171 us, total = 3.704 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 75.003 us, total = 525.019 us, Queueing time: mean = 345.038 us, max = 652.171 us, min = 17.312 us, total = 2.415 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 583.635 us, total = 3.502 ms, Queueing time: mean = 40.411 us, max = 69.512 us, min = 32.750 us, total = 242.466 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 17.017 us, total = 34.034 us, Queueing time: mean = 32.473 us, max = 64.946 us, min = 64.946 us, total = 64.946 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 84.198 us, total = 84.198 us, Queueing time: mean = 8.529 us, max = 8.529 us, min = 8.529 us, total = 8.529 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 798.889 us, total = 798.889 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 411.020 us, total = 411.020 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 580.231 us, total = 580.231 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 74.081 us, total = 74.081 us, Queueing time: mean = 581.177 us, max = 581.177 us, min = 581.177 us, total = 581.177 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 23.013 us, total = 23.013 us, Queueing time: mean = 13.504 us, max = 13.504 us, min = 13.504 us, total = 13.504 us

-----------------
Task execution event stats:

Global stats: 35776 total (1 active)
Queueing time: mean = 52.922 us, max = 2.129 ms, min = -0.000 s, total = 1.893 s
Execution time:  mean = 15.208 us, total = 544.071 ms
Event stats:
	CoreWorker.CheckSignal - 35775 total (1 active), Execution time: mean = 15.208 us, total = 544.062 ms, Queueing time: mean = 52.924 us, max = 2.129 ms, min = -0.000 s, total = 1.893 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.604 us, total = 8.604 us, Queueing time: mean = 2.978 us, max = 2.978 us, min = 2.978 us, total = 2.978 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 31.465 us, max = 258.607 us, min = 9.669 us, total = 34.014 ms
Execution time:  mean = 315.592 us, total = 341.155 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 196.036 us, total = 70.573 ms, Queueing time: mean = 52.598 us, max = 105.455 us, min = 20.116 us, total = 18.935 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 35.363 us, total = 12.731 ms, Queueing time: mean = 41.166 us, max = 90.104 us, min = 9.669 us, total = 14.820 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 715.535 us, total = 257.593 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 258.804 us, total = 258.804 us, Queueing time: mean = 258.607 us, max = 258.607 us, min = 258.607 us, total = 258.607 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660938 661720] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660938 661720] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660938 661720] core_worker.cc:5107: Number of alive nodes:0
