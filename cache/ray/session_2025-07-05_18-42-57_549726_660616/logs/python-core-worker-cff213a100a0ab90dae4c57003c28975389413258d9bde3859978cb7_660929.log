[2025-07-05 18:42:59,505 I 660929 660929] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660929
[2025-07-05 18:42:59,506 I 660929 660929] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,508 I 660929 660929] grpc_server.cc:141: worker server started, listening on port 42553.
[2025-07-05 18:42:59,510 I 660929 660929] core_worker.cc:542: Initializing worker at address: ***********:42553 worker_id=cff213a100a0ab90dae4c57003c28975389413258d9bde3859978cb7 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,511 I 660929 660929] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-07-05 18:42:59,512 I 660929 660929] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,512 I 660929 661805] core_worker.cc:902: Event stats:


Global stats: 12 total (6 active)
Queueing time: mean = 11.356 us, max = 80.093 us, min = 11.030 us, total = 136.271 us
Execution time:  mean = 134.268 us, total = 1.611 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 4 total (2 active, 1 running), Execution time: mean = 6.199 us, total = 24.798 us, Queueing time: mean = 28.236 us, max = 80.093 us, min = 32.851 us, total = 112.944 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 112.591 us, total = 112.591 us, Queueing time: mean = 11.030 us, max = 11.030 us, min = 11.030 us, total = 11.030 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 907.168 us, total = 907.168 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 45.707 us, total = 45.707 us, Queueing time: mean = 12.297 us, max = 12.297 us, min = 12.297 us, total = 12.297 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.947 us, total = 520.947 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 9.424 us, max = 31.488 us, min = 6.208 us, total = 37.696 us
Execution time:  mean = 180.423 us, total = 721.693 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.535 us, total = 18.535 us, Queueing time: mean = 31.488 us, max = 31.488 us, min = 31.488 us, total = 31.488 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 109.526 us, total = 109.526 us, Queueing time: mean = 6.208 us, max = 6.208 us, min = 6.208 us, total = 6.208 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 593.632 us, total = 593.632 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,513 I 660929 660929] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,513 I 660929 660929] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,513 I 660929 660929] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,513 I 660929 661805] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,513 I 660929 661805] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,517 W 660929 661798] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,513 I 660929 661805] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 54.406 us, max = 519.336 us, min = 7.836 us, total = 47.606 ms
Execution time:  mean = 74.681 us, total = 65.345 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.100 us, total = 4.860 ms, Queueing time: mean = 59.493 us, max = 162.830 us, min = 14.929 us, total = 35.696 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.598 us, total = 646.448 us, Queueing time: mean = 56.776 us, max = 83.431 us, min = 19.780 us, total = 3.463 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 174.227 us, total = 10.454 ms, Queueing time: mean = 55.293 us, max = 94.868 us, min = 20.519 us, total = 3.318 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 29.413 us, total = 1.765 ms, Queueing time: mean = 39.362 us, max = 64.823 us, min = 11.640 us, total = 2.362 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 744.394 us, total = 44.664 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 32.970 us, total = 395.645 us, Queueing time: mean = 43.099 us, max = 89.050 us, min = 14.983 us, total = 517.191 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 79.067 us, total = 553.471 us, Queueing time: mean = 227.284 us, max = 519.336 us, min = 7.836 us, total = 1.591 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.459 us, total = 32.755 us, Queueing time: mean = 46.965 us, max = 78.464 us, min = 31.600 us, total = 281.788 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.682 us, total = 63.682 us, Queueing time: mean = 353.857 us, max = 353.857 us, min = 353.857 us, total = 353.857 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 112.591 us, total = 112.591 us, Queueing time: mean = 11.030 us, max = 11.030 us, min = 11.030 us, total = 11.030 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 45.707 us, total = 45.707 us, Queueing time: mean = 12.297 us, max = 12.297 us, min = 12.297 us, total = 12.297 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 907.168 us, total = 907.168 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 325.166 us, total = 325.166 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.947 us, total = 520.947 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5961 total (1 active)
Queueing time: mean = 58.197 us, max = 1.157 ms, min = 2.826 us, total = 346.911 ms
Execution time:  mean = 14.962 us, total = 89.191 ms
Event stats:
	CoreWorker.CheckSignal - 5960 total (1 active), Execution time: mean = 14.964 us, total = 89.183 ms, Queueing time: mean = 58.206 us, max = 1.157 ms, min = 7.262 us, total = 346.908 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.187 us, total = 8.187 us, Queueing time: mean = 2.826 us, max = 2.826 us, min = 2.826 us, total = 2.826 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 33.462 us, max = 95.361 us, min = 6.208 us, total = 6.057 ms
Execution time:  mean = 318.469 us, total = 57.643 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 33.923 us, total = 2.035 ms, Queueing time: mean = 40.037 us, max = 69.067 us, min = 12.697 us, total = 2.402 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 202.635 us, total = 12.158 ms, Queueing time: mean = 60.803 us, max = 95.361 us, min = 21.169 us, total = 3.648 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 722.331 us, total = 43.340 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 109.526 us, total = 109.526 us, Queueing time: mean = 6.208 us, max = 6.208 us, min = 6.208 us, total = 6.208 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,514 I 660929 661805] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 52.731 us, max = 519.336 us, min = 7.836 us, total = 91.383 ms
Execution time:  mean = 73.761 us, total = 127.827 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.276 us, total = 9.932 ms, Queueing time: mean = 58.589 us, max = 188.315 us, min = 14.929 us, total = 70.307 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 178.720 us, total = 21.446 ms, Queueing time: mean = 54.943 us, max = 104.056 us, min = 20.519 us, total = 6.593 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.569 us, total = 1.268 ms, Queueing time: mean = 51.148 us, max = 100.706 us, min = 14.250 us, total = 6.138 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.708 us, total = 3.565 ms, Queueing time: mean = 40.819 us, max = 98.151 us, min = 11.640 us, total = 4.898 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 728.748 us, total = 87.450 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 37.661 us, total = 903.872 us, Queueing time: mean = 33.956 us, max = 89.050 us, min = 14.983 us, total = 814.937 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.420 us, total = 65.038 us, Queueing time: mean = 53.432 us, max = 89.768 us, min = 25.327 us, total = 641.185 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 79.067 us, total = 553.471 us, Queueing time: mean = 227.284 us, max = 519.336 us, min = 7.836 us, total = 1.591 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 334.195 us, total = 668.391 us, Queueing time: mean = 11.216 us, max = 22.431 us, min = 22.431 us, total = 22.431 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.682 us, total = 63.682 us, Queueing time: mean = 353.857 us, max = 353.857 us, min = 353.857 us, total = 353.857 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 112.591 us, total = 112.591 us, Queueing time: mean = 11.030 us, max = 11.030 us, min = 11.030 us, total = 11.030 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 45.707 us, total = 45.707 us, Queueing time: mean = 12.297 us, max = 12.297 us, min = 12.297 us, total = 12.297 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 907.168 us, total = 907.168 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 325.166 us, total = 325.166 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.947 us, total = 520.947 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11921 total (1 active)
Queueing time: mean = 57.220 us, max = 1.319 ms, min = 2.826 us, total = 682.117 ms
Execution time:  mean = 15.071 us, total = 179.663 ms
Event stats:
	CoreWorker.CheckSignal - 11920 total (1 active), Execution time: mean = 15.072 us, total = 179.654 ms, Queueing time: mean = 57.224 us, max = 1.319 ms, min = 7.262 us, total = 682.114 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.187 us, total = 8.187 us, Queueing time: mean = 2.826 us, max = 2.826 us, min = 2.826 us, total = 2.826 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 31.167 us, max = 98.343 us, min = 6.208 us, total = 11.251 ms
Execution time:  mean = 321.842 us, total = 116.185 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.789 us, total = 4.175 ms, Queueing time: mean = 38.806 us, max = 69.067 us, min = 12.697 us, total = 4.657 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 200.950 us, total = 24.114 ms, Queueing time: mean = 54.902 us, max = 98.343 us, min = 21.169 us, total = 6.588 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 731.556 us, total = 87.787 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 109.526 us, total = 109.526 us, Queueing time: mean = 6.208 us, max = 6.208 us, min = 6.208 us, total = 6.208 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,515 I 660929 661805] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 53.829 us, max = 645.221 us, min = 7.836 us, total = 139.470 ms
Execution time:  mean = 72.103 us, total = 186.818 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.285 us, total = 14.905 ms, Queueing time: mean = 59.596 us, max = 188.315 us, min = 14.929 us, total = 107.213 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 178.657 us, total = 32.158 ms, Queueing time: mean = 56.092 us, max = 104.056 us, min = 20.519 us, total = 10.097 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.523 us, total = 1.894 ms, Queueing time: mean = 52.142 us, max = 100.706 us, min = 14.250 us, total = 9.386 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.753 us, total = 5.356 ms, Queueing time: mean = 42.514 us, max = 98.151 us, min = 11.640 us, total = 7.653 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 705.710 us, total = 127.028 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 37.529 us, total = 1.351 ms, Queueing time: mean = 38.006 us, max = 89.050 us, min = 14.983 us, total = 1.368 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.373 us, total = 96.713 us, Queueing time: mean = 92.123 us, max = 645.221 us, min = 25.327 us, total = 1.658 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 79.067 us, total = 553.471 us, Queueing time: mean = 227.284 us, max = 519.336 us, min = 7.836 us, total = 1.591 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 500.203 us, total = 1.501 ms, Queueing time: mean = 42.767 us, max = 105.870 us, min = 22.431 us, total = 128.301 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.682 us, total = 63.682 us, Queueing time: mean = 353.857 us, max = 353.857 us, min = 353.857 us, total = 353.857 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 112.591 us, total = 112.591 us, Queueing time: mean = 11.030 us, max = 11.030 us, min = 11.030 us, total = 11.030 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 45.707 us, total = 45.707 us, Queueing time: mean = 12.297 us, max = 12.297 us, min = 12.297 us, total = 12.297 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 907.168 us, total = 907.168 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 325.166 us, total = 325.166 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.947 us, total = 520.947 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17883 total (1 active)
Queueing time: mean = 55.924 us, max = 1.319 ms, min = 2.826 us, total = 1.000 s
Execution time:  mean = 15.132 us, total = 270.612 ms
Event stats:
	CoreWorker.CheckSignal - 17882 total (1 active), Execution time: mean = 15.133 us, total = 270.603 ms, Queueing time: mean = 55.927 us, max = 1.319 ms, min = 7.262 us, total = 1.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.187 us, total = 8.187 us, Queueing time: mean = 2.826 us, max = 2.826 us, min = 2.826 us, total = 2.826 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 31.282 us, max = 98.343 us, min = 6.208 us, total = 16.924 ms
Execution time:  mean = 331.311 us, total = 179.239 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 36.461 us, total = 6.563 ms, Queueing time: mean = 41.130 us, max = 81.037 us, min = 12.697 us, total = 7.403 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 205.384 us, total = 36.969 ms, Queueing time: mean = 52.855 us, max = 98.343 us, min = 21.169 us, total = 9.514 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 753.319 us, total = 135.597 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 109.526 us, total = 109.526 us, Queueing time: mean = 6.208 us, max = 6.208 us, min = 6.208 us, total = 6.208 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,515 I 660929 661805] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 53.640 us, max = 645.221 us, min = 7.836 us, total = 185.060 ms
Execution time:  mean = 73.001 us, total = 251.853 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.231 us, total = 19.746 ms, Queueing time: mean = 59.056 us, max = 188.315 us, min = 14.929 us, total = 141.675 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 179.491 us, total = 43.078 ms, Queueing time: mean = 55.646 us, max = 104.056 us, min = 20.519 us, total = 13.355 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.618 us, total = 2.548 ms, Queueing time: mean = 54.034 us, max = 102.190 us, min = 14.250 us, total = 12.968 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 30.027 us, total = 7.206 ms, Queueing time: mean = 43.463 us, max = 98.151 us, min = 11.640 us, total = 10.431 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 719.296 us, total = 172.631 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 37.848 us, total = 1.817 ms, Queueing time: mean = 44.983 us, max = 99.455 us, min = 14.983 us, total = 2.159 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.323 us, total = 127.760 us, Queueing time: mean = 95.814 us, max = 645.221 us, min = 25.327 us, total = 2.300 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 79.067 us, total = 553.471 us, Queueing time: mean = 227.284 us, max = 519.336 us, min = 7.836 us, total = 1.591 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 542.759 us, total = 2.171 ms, Queueing time: mean = 50.886 us, max = 105.870 us, min = 22.431 us, total = 203.545 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.682 us, total = 63.682 us, Queueing time: mean = 353.857 us, max = 353.857 us, min = 353.857 us, total = 353.857 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 112.591 us, total = 112.591 us, Queueing time: mean = 11.030 us, max = 11.030 us, min = 11.030 us, total = 11.030 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 45.707 us, total = 45.707 us, Queueing time: mean = 12.297 us, max = 12.297 us, min = 12.297 us, total = 12.297 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 907.168 us, total = 907.168 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 325.166 us, total = 325.166 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.947 us, total = 520.947 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23842 total (1 active)
Queueing time: mean = 56.977 us, max = 1.319 ms, min = 2.826 us, total = 1.358 s
Execution time:  mean = 15.122 us, total = 360.542 ms
Event stats:
	CoreWorker.CheckSignal - 23841 total (1 active), Execution time: mean = 15.122 us, total = 360.534 ms, Queueing time: mean = 56.979 us, max = 1.319 ms, min = 7.262 us, total = 1.358 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.187 us, total = 8.187 us, Queueing time: mean = 2.826 us, max = 2.826 us, min = 2.826 us, total = 2.826 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 32.496 us, max = 110.618 us, min = 6.208 us, total = 23.430 ms
Execution time:  mean = 335.675 us, total = 242.022 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 37.588 us, total = 9.021 ms, Queueing time: mean = 41.858 us, max = 81.037 us, min = 12.697 us, total = 10.046 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 207.690 us, total = 49.846 ms, Queueing time: mean = 55.739 us, max = 110.618 us, min = 21.169 us, total = 13.377 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 762.689 us, total = 183.045 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 109.526 us, total = 109.526 us, Queueing time: mean = 6.208 us, max = 6.208 us, min = 6.208 us, total = 6.208 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,516 I 660929 661805] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 53.093 us, max = 645.221 us, min = 7.836 us, total = 228.830 ms
Execution time:  mean = 74.135 us, total = 319.523 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.266 us, total = 24.789 ms, Queueing time: mean = 58.311 us, max = 188.315 us, min = 14.090 us, total = 174.876 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 179.694 us, total = 53.908 ms, Queueing time: mean = 55.132 us, max = 104.056 us, min = 16.589 us, total = 16.540 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.651 us, total = 3.195 ms, Queueing time: mean = 54.138 us, max = 102.190 us, min = 14.250 us, total = 16.241 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.107 us, total = 9.032 ms, Queueing time: mean = 43.576 us, max = 98.151 us, min = 11.640 us, total = 13.073 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 736.007 us, total = 220.802 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 37.278 us, total = 2.237 ms, Queueing time: mean = 47.475 us, max = 99.455 us, min = 14.983 us, total = 2.849 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.385 us, total = 161.557 us, Queueing time: mean = 100.355 us, max = 645.221 us, min = 25.327 us, total = 3.011 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 79.067 us, total = 553.471 us, Queueing time: mean = 227.284 us, max = 519.336 us, min = 7.836 us, total = 1.591 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 572.251 us, total = 2.861 ms, Queueing time: mean = 46.842 us, max = 105.870 us, min = 22.431 us, total = 234.212 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.045 us, total = 8.091 us, Queueing time: mean = 19.352 us, max = 38.704 us, min = 38.704 us, total = 38.704 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.682 us, total = 63.682 us, Queueing time: mean = 353.857 us, max = 353.857 us, min = 353.857 us, total = 353.857 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 112.591 us, total = 112.591 us, Queueing time: mean = 11.030 us, max = 11.030 us, min = 11.030 us, total = 11.030 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 45.707 us, total = 45.707 us, Queueing time: mean = 12.297 us, max = 12.297 us, min = 12.297 us, total = 12.297 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 907.168 us, total = 907.168 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 325.166 us, total = 325.166 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.947 us, total = 520.947 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29801 total (1 active)
Queueing time: mean = 57.340 us, max = 1.319 ms, min = 2.826 us, total = 1.709 s
Execution time:  mean = 15.099 us, total = 449.969 ms
Event stats:
	CoreWorker.CheckSignal - 29800 total (1 active), Execution time: mean = 15.099 us, total = 449.961 ms, Queueing time: mean = 57.341 us, max = 1.319 ms, min = 7.262 us, total = 1.709 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.187 us, total = 8.187 us, Queueing time: mean = 2.826 us, max = 2.826 us, min = 2.826 us, total = 2.826 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 32.942 us, max = 122.609 us, min = 6.208 us, total = 29.681 ms
Execution time:  mean = 334.601 us, total = 301.476 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 37.795 us, total = 11.339 ms, Queueing time: mean = 42.586 us, max = 87.834 us, min = 12.697 us, total = 12.776 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 208.741 us, total = 62.622 ms, Queueing time: mean = 56.329 us, max = 122.609 us, min = 21.169 us, total = 16.899 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 758.018 us, total = 227.405 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 109.526 us, total = 109.526 us, Queueing time: mean = 6.208 us, max = 6.208 us, min = 6.208 us, total = 6.208 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,517 I 660929 661805] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 53.065 us, max = 645.221 us, min = 7.836 us, total = 274.239 ms
Execution time:  mean = 74.586 us, total = 385.460 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.308 us, total = 29.891 ms, Queueing time: mean = 58.121 us, max = 188.315 us, min = 14.090 us, total = 209.119 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 179.532 us, total = 64.632 ms, Queueing time: mean = 56.012 us, max = 107.066 us, min = 16.589 us, total = 20.164 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.661 us, total = 3.838 ms, Queueing time: mean = 54.190 us, max = 102.190 us, min = 14.250 us, total = 19.508 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.249 us, total = 10.890 ms, Queueing time: mean = 44.421 us, max = 98.151 us, min = 11.640 us, total = 15.992 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 742.474 us, total = 267.291 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 37.160 us, total = 2.676 ms, Queueing time: mean = 48.108 us, max = 99.455 us, min = 14.983 us, total = 3.464 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.354 us, total = 192.727 us, Queueing time: mean = 102.334 us, max = 645.221 us, min = 25.327 us, total = 3.684 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 79.067 us, total = 553.471 us, Queueing time: mean = 227.284 us, max = 519.336 us, min = 7.836 us, total = 1.591 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 585.755 us, total = 3.515 ms, Queueing time: mean = 50.074 us, max = 105.870 us, min = 22.431 us, total = 300.446 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.045 us, total = 8.091 us, Queueing time: mean = 19.352 us, max = 38.704 us, min = 38.704 us, total = 38.704 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.682 us, total = 63.682 us, Queueing time: mean = 353.857 us, max = 353.857 us, min = 353.857 us, total = 353.857 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 112.591 us, total = 112.591 us, Queueing time: mean = 11.030 us, max = 11.030 us, min = 11.030 us, total = 11.030 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 45.707 us, total = 45.707 us, Queueing time: mean = 12.297 us, max = 12.297 us, min = 12.297 us, total = 12.297 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 907.168 us, total = 907.168 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 325.166 us, total = 325.166 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.947 us, total = 520.947 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35762 total (1 active)
Queueing time: mean = 56.928 us, max = 2.165 ms, min = -0.000 s, total = 2.036 s
Execution time:  mean = 15.099 us, total = 539.957 ms
Event stats:
	CoreWorker.CheckSignal - 35761 total (1 active), Execution time: mean = 15.099 us, total = 539.949 ms, Queueing time: mean = 56.929 us, max = 2.165 ms, min = -0.000 s, total = 2.036 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.187 us, total = 8.187 us, Queueing time: mean = 2.826 us, max = 2.826 us, min = 2.826 us, total = 2.826 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 33.320 us, max = 122.609 us, min = 6.208 us, total = 36.019 ms
Execution time:  mean = 331.892 us, total = 358.775 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 37.559 us, total = 13.521 ms, Queueing time: mean = 43.201 us, max = 87.834 us, min = 12.697 us, total = 15.552 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 207.954 us, total = 74.863 ms, Queueing time: mean = 56.835 us, max = 122.609 us, min = 21.169 us, total = 20.461 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 750.779 us, total = 270.281 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 109.526 us, total = 109.526 us, Queueing time: mean = 6.208 us, max = 6.208 us, min = 6.208 us, total = 6.208 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660929 661805] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660929 661805] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660929 661805] core_worker.cc:5107: Number of alive nodes:0
