[2025-07-05 18:42:57,580 I 660691 660691] (gcs_server) gcs_server_main.cc:91: Ray cluster metadata ray_version=2.47.1 ray_commit=61d3f2f1aa33563faa398105f4abda88cb39440b
[2025-07-05 18:42:57,580 I 660691 660691] (gcs_server) io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:57,585 I 660691 660691] (gcs_server) event.cc:500: Ray Event initialized for GCS
[2025-07-05 18:42:57,585 I 660691 660691] (gcs_server) event.cc:500: Ray Event initialized for EXPORT_NODE
[2025-07-05 18:42:57,585 I 660691 660691] (gcs_server) event.cc:500: Ray Event initialized for EXPORT_ACTOR
[2025-07-05 18:42:57,585 I 660691 660691] (gcs_server) event.cc:500: Ray Event initialized for EXPORT_DRIVER_JOB
[2025-07-05 18:42:57,585 I 660691 660691] (gcs_server) event.cc:331: Set ray event level to warning
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_server.cc:77: GCS storage type is StorageType::IN_MEMORY
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:46: Loading job table data.
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:56: Loading node table data.
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:78: Loading actor table data.
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:89: Loading actor task spec table data.
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:66: Loading placement group table data.
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:50: Finished loading job table data, size = 0
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:60: Finished loading node table data, size = 0
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:83: Finished loading actor table data, size = 0
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:94: Finished loading actor task spec table data, size = 0
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_init_data.cc:72: Finished loading placement group table data, size = 0
[2025-07-05 18:42:57,586 I 660691 660691] (gcs_server) gcs_server.cc:161: No existing server cluster ID found. Generating new ID: 8770274bf56d098bb9c6d6a10e6d5bf0b6b2a670f53ca772dc307c48
[2025-07-05 18:42:57,587 I 660691 660691] (gcs_server) gcs_server.cc:673: Autoscaler V2 enabled: 0
[2025-07-05 18:42:57,588 I 660691 660691] (gcs_server) grpc_server.cc:141: GcsServer server started, listening on port 61069.
[2025-07-05 18:42:57,664 I 660691 660691] (gcs_server) gcs_server.cc:249: Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 0
- DrainNode request count: 0
- GetAllNodeInfo request count: 0

GcsActorManager: 
- RegisterActor request count: 0
- CreateActor request count: 0
- GetActorInfo request count: 0
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 0
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 0
- owners_: 0
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 0
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 0

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- WaitPlacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 0
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 0
-Total num of actor creation tasks: 0
-Total num of actor tasks: 0
-Total num of normal tasks: 0
-Total num of driver tasks: 0

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 0
- pending demands:



[2025-07-05 18:42:57,664 I 660691 660691] (gcs_server) gcs_server.cc:870: Main service Event stats:


Global stats: 25 total (4 active)
Queueing time: mean = 28.068 ms, max = 77.518 ms, min = 110.000 ns, total = 701.694 ms
Execution time:  mean = 6.208 ms, total = 155.198 ms
Event stats:
	GcsInMemoryStore.Put - 9 total (0 active), Execution time: mean = 8.615 ms, total = 77.534 ms, Queueing time: mean = 60.008 ms, max = 77.318 ms, min = 2.772 us, total = 540.071 ms
	GcsInMemoryStore.GetAll - 5 total (0 active), Execution time: mean = 14.664 us, total = 73.318 us, Queueing time: mean = 43.055 us, max = 46.807 us, min = 35.252 us, total = 215.274 us
	PeriodicalRunner.RunFnPeriodically - 4 total (2 active, 1 running), Execution time: mean = 2.259 us, total = 9.036 us, Queueing time: mean = 38.747 ms, max = 77.518 ms, min = 77.468 ms, total = 154.987 ms
	event_loop_lag_probe - 2 total (0 active), Execution time: mean = 7.040 us, total = 14.080 us, Queueing time: mean = 3.201 ms, max = 6.320 ms, min = 82.994 us, total = 6.403 ms
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	ClusterResourceManager.ResetRemoteNodeView - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	RayletLoadPulled - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Get - 1 total (0 active), Execution time: mean = 19.644 us, total = 19.644 us, Queueing time: mean = 17.585 us, max = 17.585 us, min = 17.585 us, total = 17.585 us


[2025-07-05 18:42:57,664 I 660691 660691] (gcs_server) gcs_server.cc:874: task_io_context Event stats:


Global stats: 4 total (1 active)
Queueing time: mean = 64.594 us, max = 235.041 us, min = 9.466 us, total = 258.377 us
Execution time:  mean = 46.764 us, total = 187.057 us
Event stats:
	event_loop_lag_probe - 2 total (0 active), Execution time: mean = 91.338 us, total = 182.677 us, Queueing time: mean = 124.456 us, max = 235.041 us, min = 13.870 us, total = 248.911 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 4.380 us, total = 4.380 us, Queueing time: mean = 9.466 us, max = 9.466 us, min = 9.466 us, total = 9.466 us
	GcsTaskManager.GcJobSummary - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:42:57,664 I 660691 660691] (gcs_server) gcs_server.cc:874: pubsub_io_context Event stats:


Global stats: 4 total (1 active)
Queueing time: mean = 226.489 us, max = 882.198 us, min = 10.063 us, total = 905.955 us
Execution time:  mean = 30.822 us, total = 123.287 us
Event stats:
	event_loop_lag_probe - 2 total (0 active), Execution time: mean = 59.703 us, total = 119.405 us, Queueing time: mean = 447.946 us, max = 882.198 us, min = 13.694 us, total = 895.892 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 3.882 us, total = 3.882 us, Queueing time: mean = 10.063 us, max = 10.063 us, min = 10.063 us, total = 10.063 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:42:57,664 I 660691 660691] (gcs_server) gcs_server.cc:874: ray_syncer_io_context Event stats:


Global stats: 4 total (0 active)
Queueing time: mean = 111.748 us, max = 411.929 us, min = 8.664 us, total = 446.992 us
Execution time:  mean = 31.317 us, total = 125.266 us
Event stats:
	event_loop_lag_probe - 2 total (0 active), Execution time: mean = 62.059 us, total = 124.118 us, Queueing time: mean = 213.493 us, max = 411.929 us, min = 15.057 us, total = 426.986 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 574.000 ns, total = 1.148 us, Queueing time: mean = 10.003 us, max = 11.342 us, min = 8.664 us, total = 20.006 us


[2025-07-05 18:42:58,875 I 660691 660691] (gcs_server) gcs_node_manager.cc:92: Registering node info, address = ***********, node name = *********** node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:58,875 I 660691 660691] (gcs_server) gcs_node_manager.cc:98: Finished registering node info, address = ***********, node name = ***********, is_head_node = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:58,875 I 660691 660691] (gcs_server) gcs_placement_group_mgr.cc:851: A new node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d registered, will try to reschedule all the infeasible placement groups.
[2025-07-05 18:42:58,880 I 660691 660749] (gcs_server) ray_syncer.cc:244: Get connection node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,732 I 660691 660691] (gcs_server) gcs_job_manager.cc:97: Adding job, job id = 01000000, driver pid = 660616
[2025-07-05 18:42:59,732 I 660691 660691] (gcs_server) gcs_job_manager.cc:118: Finished adding job, job id = 01000000, driver pid = 660616
[2025-07-05 18:42:59,853 I 660691 660691] (gcs_server) gcs_actor_manager.cc:403: Registering actor job_id=01000000 actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:42:59,853 I 660691 660691] (gcs_server) gcs_actor_manager.cc:408: Registered actor, job id = 01000000, actor id = c4dbba14b481690e7680c72d01000000
[2025-07-05 18:42:59,855 I 660691 660691] (gcs_server) gcs_actor_manager.cc:515: Creating actor job_id=01000000 actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:42:59,855 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:318: Start leasing worker from node 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d for actor c4dbba14b481690e7680c72d01000000, job id = 01000000
[2025-07-05 18:42:59,856 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:642: Finished leasing worker from 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d for actor c4dbba14b481690e7680c72d01000000, job id = 01000000
[2025-07-05 18:42:59,856 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:454: Start creating actor c4dbba14b481690e7680c72d01000000 on worker d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0 at node 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d, job id = 01000000
[2025-07-05 18:42:59,865 I 660691 660691] (gcs_server) gcs_actor_manager.cc:403: Registering actor job_id=01000000 actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:42:59,865 I 660691 660691] (gcs_server) gcs_actor_manager.cc:408: Registered actor, job id = 01000000, actor id = 3e5877903265ed7cd75303e201000000
[2025-07-05 18:42:59,866 I 660691 660691] (gcs_server) gcs_actor_manager.cc:515: Creating actor job_id=01000000 actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:42:59,866 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:318: Start leasing worker from node 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d for actor 3e5877903265ed7cd75303e201000000, job id = 01000000
[2025-07-05 18:42:59,867 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:642: Finished leasing worker from 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d for actor 3e5877903265ed7cd75303e201000000, job id = 01000000
[2025-07-05 18:42:59,867 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:454: Start creating actor 3e5877903265ed7cd75303e201000000 on worker dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385 at node 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d, job id = 01000000
[2025-07-05 18:42:59,923 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:491: Finished actor creation task for actor 3e5877903265ed7cd75303e201000000 on worker dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385 at node 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d, job id = 01000000
[2025-07-05 18:42:59,923 I 660691 660691] (gcs_server) gcs_actor_manager.cc:1586: Actor created successfully job_id=01000000 actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:42:59,923 I 660691 660691] (gcs_server) gcs_actor_manager.cc:530: Finished creating actor. Status: OK job_id=01000000 actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:43:07,595 W 660691 660714] (gcs_server) metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:57,665 I 660691 660691] (gcs_server) gcs_server.cc:249: Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 1
- DrainNode request count: 0
- GetAllNodeInfo request count: 53

GcsActorManager: 
- RegisterActor request count: 2
- CreateActor request count: 2
- GetActorInfo request count: 1
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 2
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 1
- owners_: 1
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 1
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 12

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- WaitPlacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:
GCS_NODE_INFO_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 589
- current buffered bytes: 0
RAY_LOG_CHANNEL
- cumulative published messages: 20
- cumulative published bytes: 15010
- current buffered bytes: 891
GCS_JOB_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 288
- current buffered bytes: 288
GCS_ACTOR_CHANNEL
- cumulative published messages: 5
- cumulative published bytes: 1591
- current buffered bytes: 0

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 7
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 4
-Total num of actor creation tasks: 2
-Total num of actor tasks: 1
-Total num of normal tasks: 0
-Total num of driver tasks: 1

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 12
- pending demands:



[2025-07-05 18:43:57,666 I 660691 660691] (gcs_server) gcs_server.cc:870: Main service Event stats:


Global stats: 1277 total (8 active)
Queueing time: mean = 583.755 us, max = 77.518 ms, min = 110.000 ns, total = 745.455 ms
Execution time:  mean = 380.541 us, total = 485.951 ms
Event stats:
	event_loop_lag_probe - 242 total (0 active), Execution time: mean = 16.976 us, total = 4.108 ms, Queueing time: mean = 32.606 us, max = 6.320 ms, min = 2.282 us, total = 7.891 ms
	GcsInMemoryStore.Put - 156 total (0 active), Execution time: mean = 815.198 us, total = 127.171 ms, Queueing time: mean = 3.497 ms, max = 77.318 ms, min = 1.430 us, total = 545.505 ms
	GcsInMemoryStore.Get - 80 total (0 active), Execution time: mean = 11.707 us, total = 936.588 us, Queueing time: mean = 5.971 us, max = 33.098 us, min = 1.323 us, total = 477.655 us
	InternalKVGcsService.grpc_server.InternalKVGet - 79 total (0 active), Execution time: mean = 271.387 us, total = 21.440 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVGet.HandleRequestImpl - 79 total (0 active), Execution time: mean = 19.077 us, total = 1.507 ms, Queueing time: mean = 42.348 us, max = 660.398 us, min = 5.120 us, total = 3.346 ms
	RayletLoadPulled - 60 total (1 active), Execution time: mean = 191.285 us, total = 11.477 ms, Queueing time: mean = 52.929 us, max = 99.556 us, min = 22.744 us, total = 3.176 ms
	NodeManagerService.grpc_client.GetResourceLoad.OnReplyReceived - 58 total (0 active), Execution time: mean = 56.075 us, total = 3.252 ms, Queueing time: mean = 44.163 us, max = 71.937 us, min = 16.415 us, total = 2.561 ms
	NodeManagerService.grpc_client.GetResourceLoad - 58 total (0 active), Execution time: mean = 890.252 us, total = 51.635 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo - 53 total (0 active), Execution time: mean = 295.580 us, total = 15.666 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo.HandleRequestImpl - 53 total (0 active), Execution time: mean = 16.928 us, total = 897.205 us, Queueing time: mean = 33.278 us, max = 284.738 us, min = 5.185 us, total = 1.764 ms
	InternalKVGcsService.grpc_server.InternalKVPut.HandleRequestImpl - 41 total (0 active), Execution time: mean = 29.588 us, total = 1.213 ms, Queueing time: mean = 35.349 us, max = 82.161 us, min = 6.258 us, total = 1.449 ms
	InternalKVGcsService.grpc_server.InternalKVPut - 41 total (0 active), Execution time: mean = 285.262 us, total = 11.696 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId.HandleRequestImpl - 29 total (0 active), Execution time: mean = 15.296 us, total = 443.594 us, Queueing time: mean = 23.785 us, max = 56.294 us, min = 7.831 us, total = 689.753 us
	NodeInfoGcsService.grpc_server.GetClusterId - 29 total (0 active), Execution time: mean = 188.320 us, total = 5.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo - 25 total (0 active), Execution time: mean = 273.568 us, total = 6.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo.HandleRequestImpl - 25 total (0 active), Execution time: mean = 11.905 us, total = 297.631 us, Queueing time: mean = 67.096 us, max = 637.610 us, min = 7.348 us, total = 1.677 ms
	ClusterResourceManager.ResetRemoteNodeView - 20 total (1 active), Execution time: mean = 7.192 us, total = 143.838 us, Queueing time: mean = 52.300 us, max = 79.784 us, min = 22.065 us, total = 1.046 ms
	HealthCheck - 18 total (0 active), Execution time: mean = 5.448 us, total = 98.063 us, Queueing time: mean = 44.284 us, max = 54.781 us, min = 15.854 us, total = 797.118 us
	NodeInfoGcsService.grpc_server.CheckAlive.HandleRequestImpl - 13 total (0 active), Execution time: mean = 15.258 us, total = 198.355 us, Queueing time: mean = 50.053 us, max = 72.578 us, min = 22.097 us, total = 650.692 us
	NodeInfoGcsService.grpc_server.CheckAlive - 13 total (0 active), Execution time: mean = 295.746 us, total = 3.845 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	AutoscalerStateService.grpc_server.GetClusterResourceState - 12 total (0 active), Execution time: mean = 324.075 us, total = 3.889 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage.HandleRequestImpl - 12 total (0 active), Execution time: mean = 53.239 us, total = 638.863 us, Queueing time: mean = 45.348 us, max = 56.967 us, min = 24.750 us, total = 544.179 us
	AutoscalerStateService.grpc_server.GetClusterResourceState.HandleRequestImpl - 12 total (0 active), Execution time: mean = 35.231 us, total = 422.769 us, Queueing time: mean = 45.801 us, max = 67.350 us, min = 19.390 us, total = 549.617 us
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage - 12 total (0 active), Execution time: mean = 365.600 us, total = 4.387 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.GetAll - 6 total (0 active), Execution time: mean = 14.959 us, total = 89.752 us, Queueing time: mean = 36.963 us, max = 46.807 us, min = 6.503 us, total = 221.777 us
	GCSServer.deadline_timer.debug_state_dump - 6 total (1 active), Execution time: mean = 1.160 ms, total = 6.960 ms, Queueing time: mean = 38.907 us, max = 65.133 us, min = 28.939 us, total = 233.442 us
	PeriodicalRunner.RunFnPeriodically - 4 total (0 active), Execution time: mean = 157.890 us, total = 631.559 us, Queueing time: mean = 38.864 ms, max = 77.518 ms, min = 31.166 us, total = 155.456 ms
	ActorInfoGcsService.grpc_server.RegisterActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 597.033 us, total = 1.194 ms, Queueing time: mean = 53.142 us, max = 57.937 us, min = 48.347 us, total = 106.284 us
	ActorInfoGcsService.grpc_server.CreateActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 383.161 us, total = 766.322 us, Queueing time: mean = 19.713 us, max = 21.036 us, min = 18.390 us, total = 39.426 us
	CoreWorkerService.grpc_client.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.RegisterActor - 2 total (0 active), Execution time: mean = 934.552 us, total = 1.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease - 2 total (0 active), Execution time: mean = 914.984 us, total = 1.830 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsHealthCheckManager::MarkNodeHealthy - 2 total (0 active), Execution time: mean = 1.360 us, total = 2.720 us, Queueing time: mean = 4.211 ms, max = 8.372 ms, min = 50.984 us, total = 8.423 ms
	ActorInfoGcsService.grpc_server.CreateActor - 2 total (1 active), Execution time: mean = 28.808 ms, total = 57.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 2 total (1 active), Execution time: mean = 27.784 ms, total = 55.568 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsResourceManager::Update - 2 total (0 active), Execution time: mean = 59.562 us, total = 119.123 us, Queueing time: mean = 4.199 ms, max = 8.366 ms, min = 32.414 us, total = 8.398 ms
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 2 total (0 active), Execution time: mean = 302.106 us, total = 604.212 us, Queueing time: mean = 49.912 us, max = 52.127 us, min = 47.697 us, total = 99.824 us
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 1 total (0 active), Execution time: mean = 337.099 us, total = 337.099 us, Queueing time: mean = 53.254 us, max = 53.254 us, min = 53.254 us, total = 53.254 us
	InternalKVGcsService.grpc_server.GetInternalConfig - 1 total (0 active), Execution time: mean = 449.486 us, total = 449.486 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.GetNextJobID - 1 total (0 active), Execution time: mean = 217.427 us, total = 217.427 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	JobInfoGcsService.grpc_server.GetNextJobID.HandleRequestImpl - 1 total (0 active), Execution time: mean = 7.157 us, total = 7.157 us, Queueing time: mean = 53.538 us, max = 53.538 us, min = 53.538 us, total = 53.538 us
	JobInfoGcsService.grpc_server.AddJob - 1 total (0 active), Execution time: mean = 179.464 us, total = 179.464 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.AddJob.HandleRequestImpl - 1 total (0 active), Execution time: mean = 62.233 us, total = 62.233 us, Queueing time: mean = 5.687 us, max = 5.687 us, min = 5.687 us, total = 5.687 us
	NodeInfoGcsService.grpc_server.RegisterNode - 1 total (0 active), Execution time: mean = 816.646 us, total = 816.646 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Delete - 1 total (0 active), Execution time: mean = 9.483 us, total = 9.483 us, Queueing time: mean = 7.193 us, max = 7.193 us, min = 7.193 us, total = 7.193 us
	JobInfoGcsService.grpc_server.GetAllJobInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 15.633 us, total = 15.633 us, Queueing time: mean = 54.801 us, max = 54.801 us, min = 54.801 us, total = 54.801 us
	InternalKVGcsService.grpc_server.GetInternalConfig.HandleRequestImpl - 1 total (0 active), Execution time: mean = 51.973 us, total = 51.973 us, Queueing time: mean = 57.801 us, max = 57.801 us, min = 57.801 us, total = 57.801 us
	GcsHealthCheckManager::AddNode - 1 total (0 active), Execution time: mean = 12.710 us, total = 12.710 us, Queueing time: mean = 554.000 ns, max = 554.000 ns, min = 554.000 ns, total = 554.000 ns
	ActorInfoGcsService.grpc_server.GetActorInfo - 1 total (0 active), Execution time: mean = 340.669 us, total = 340.669 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.GetActorInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 21.827 us, total = 21.827 us, Queueing time: mean = 15.907 us, max = 15.907 us, min = 15.907 us, total = 15.907 us
	JobInfoGcsService.grpc_server.GetAllJobInfo - 1 total (0 active), Execution time: mean = 455.736 us, total = 455.736 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GCSServer.deadline_timer.debug_state_event_stats_print - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVDel.HandleRequestImpl - 1 total (0 active), Execution time: mean = 50.454 us, total = 50.454 us, Queueing time: mean = 46.858 us, max = 46.858 us, min = 46.858 us, total = 46.858 us
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	GcsInMemoryStore.GetNextJobID - 1 total (0 active), Execution time: mean = 8.613 us, total = 8.613 us, Queueing time: mean = 6.080 us, max = 6.080 us, min = 6.080 us, total = 6.080 us
	NodeInfoGcsService.grpc_server.RegisterNode.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.004 us, total = 112.004 us, Queueing time: mean = 50.963 us, max = 50.963 us, min = 50.963 us, total = 50.963 us
	InternalKVGcsService.grpc_server.InternalKVDel - 1 total (0 active), Execution time: mean = 340.770 us, total = 340.770 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:43:57,666 I 660691 660691] (gcs_server) gcs_server.cc:874: task_io_context Event stats:


Global stats: 3192 total (1 active)
Queueing time: mean = 19.401 us, max = 235.041 us, min = 3.480 us, total = 61.929 ms
Execution time:  mean = 124.610 us, total = 397.756 ms
Event stats:
	TaskInfoGcsService.grpc_server.AddTaskEventData - 1468 total (0 active), Execution time: mean = 254.705 us, total = 373.907 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	TaskInfoGcsService.grpc_server.AddTaskEventData.HandleRequestImpl - 1468 total (0 active), Execution time: mean = 13.078 us, total = 19.198 ms, Queueing time: mean = 40.439 us, max = 154.245 us, min = 5.094 us, total = 59.364 ms
	event_loop_lag_probe - 242 total (0 active), Execution time: mean = 18.863 us, total = 4.565 ms, Queueing time: mean = 7.124 us, max = 235.041 us, min = 3.480 us, total = 1.724 ms
	GcsTaskManager.GcJobSummary - 13 total (1 active), Execution time: mean = 6.318 us, total = 82.128 us, Queueing time: mean = 63.933 us, max = 98.257 us, min = 35.161 us, total = 831.126 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 4.380 us, total = 4.380 us, Queueing time: mean = 9.466 us, max = 9.466 us, min = 9.466 us, total = 9.466 us


[2025-07-05 18:43:57,666 I 660691 660691] (gcs_server) gcs_server.cc:874: pubsub_io_context Event stats:


Global stats: 438 total (29 active)
Queueing time: mean = 13.647 us, max = 882.198 us, min = 2.979 us, total = 5.977 ms
Execution time:  mean = 124.421 ms, total = 54.496 s
Event stats:
	event_loop_lag_probe - 242 total (0 active), Execution time: mean = 18.416 us, total = 4.457 ms, Queueing time: mean = 10.016 us, max = 882.198 us, min = 2.979 us, total = 2.424 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll.HandleRequestImpl - 47 total (0 active), Execution time: mean = 21.311 us, total = 1.002 ms, Queueing time: mean = 35.875 us, max = 160.291 us, min = 4.744 us, total = 1.686 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll - 47 total (28 active), Execution time: mean = 1.159 s, total = 54.474 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch - 30 total (0 active), Execution time: mean = 252.052 us, total = 7.562 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch.HandleRequestImpl - 30 total (0 active), Execution time: mean = 28.413 us, total = 852.387 us, Queueing time: mean = 28.317 us, max = 142.940 us, min = 4.958 us, total = 849.505 us
	InternalPubSubGcsService.grpc_server.GcsPublish.HandleRequestImpl - 20 total (0 active), Execution time: mean = 73.460 us, total = 1.469 ms, Queueing time: mean = 50.394 us, max = 62.214 us, min = 12.086 us, total = 1.008 ms
	InternalPubSubGcsService.grpc_server.GcsPublish - 20 total (0 active), Execution time: mean = 366.269 us, total = 7.325 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 3.882 us, total = 3.882 us, Queueing time: mean = 10.063 us, max = 10.063 us, min = 10.063 us, total = 10.063 us


[2025-07-05 18:43:57,666 I 660691 660691] (gcs_server) gcs_server.cc:874: ray_syncer_io_context Event stats:


Global stats: 248 total (0 active)
Queueing time: mean = 8.586 us, max = 411.929 us, min = 274.000 ns, total = 2.129 ms
Execution time:  mean = 20.130 us, total = 4.992 ms
Event stats:
	event_loop_lag_probe - 242 total (0 active), Execution time: mean = 19.964 us, total = 4.831 ms, Queueing time: mean = 8.274 us, max = 411.929 us, min = 3.168 us, total = 2.002 ms
	 - 2 total (0 active), Execution time: mean = 67.501 us, total = 135.001 us, Queueing time: mean = 53.227 us, max = 54.007 us, min = 52.447 us, total = 106.454 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 574.000 ns, total = 1.148 us, Queueing time: mean = 10.003 us, max = 11.342 us, min = 8.664 us, total = 20.006 us
	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 12.356 us, total = 24.712 us, Queueing time: mean = 294.000 ns, max = 314.000 ns, min = 274.000 ns, total = 588.000 ns


[2025-07-05 18:44:57,666 I 660691 660691] (gcs_server) gcs_server.cc:249: Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 1
- DrainNode request count: 0
- GetAllNodeInfo request count: 53

GcsActorManager: 
- RegisterActor request count: 2
- CreateActor request count: 2
- GetActorInfo request count: 1
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 2
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 1
- owners_: 1
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 1
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 24

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- WaitPlacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:
GCS_NODE_INFO_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 589
- current buffered bytes: 0
RAY_LOG_CHANNEL
- cumulative published messages: 39
- cumulative published bytes: 27565
- current buffered bytes: 891
GCS_JOB_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 288
- current buffered bytes: 288
GCS_ACTOR_CHANNEL
- cumulative published messages: 5
- cumulative published bytes: 1591
- current buffered bytes: 0

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 7
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 4
-Total num of actor creation tasks: 2
-Total num of actor tasks: 1
-Total num of normal tasks: 0
-Total num of driver tasks: 1

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 24
- pending demands:



[2025-07-05 18:44:57,667 I 660691 660691] (gcs_server) gcs_server.cc:870: Main service Event stats:


Global stats: 1998 total (8 active)
Queueing time: mean = 382.520 us, max = 77.518 ms, min = 110.000 ns, total = 764.275 ms
Execution time:  mean = 302.019 us, total = 603.435 ms
Event stats:
	event_loop_lag_probe - 482 total (0 active), Execution time: mean = 17.663 us, total = 8.514 ms, Queueing time: mean = 19.682 us, max = 6.320 ms, min = 2.282 us, total = 9.487 ms
	GcsInMemoryStore.Put - 252 total (0 active), Execution time: mean = 506.141 us, total = 127.547 ms, Queueing time: mean = 2.183 ms, max = 77.318 ms, min = 1.430 us, total = 550.130 ms
	RayletLoadPulled - 120 total (1 active), Execution time: mean = 192.873 us, total = 23.145 ms, Queueing time: mean = 49.731 us, max = 99.556 us, min = 2.435 us, total = 5.968 ms
	NodeManagerService.grpc_client.GetResourceLoad.OnReplyReceived - 118 total (0 active), Execution time: mean = 57.683 us, total = 6.807 ms, Queueing time: mean = 45.531 us, max = 77.883 us, min = 16.415 us, total = 5.373 ms
	NodeManagerService.grpc_client.GetResourceLoad - 118 total (0 active), Execution time: mean = 896.745 us, total = 105.816 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Get - 92 total (0 active), Execution time: mean = 12.020 us, total = 1.106 ms, Queueing time: mean = 6.280 us, max = 33.098 us, min = 1.323 us, total = 577.793 us
	InternalKVGcsService.grpc_server.InternalKVGet - 91 total (0 active), Execution time: mean = 283.250 us, total = 25.776 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVGet.HandleRequestImpl - 91 total (0 active), Execution time: mean = 22.227 us, total = 2.023 ms, Queueing time: mean = 43.337 us, max = 660.398 us, min = 5.120 us, total = 3.944 ms
	InternalKVGcsService.grpc_server.InternalKVPut - 65 total (0 active), Execution time: mean = 300.905 us, total = 19.559 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut.HandleRequestImpl - 65 total (0 active), Execution time: mean = 31.330 us, total = 2.036 ms, Queueing time: mean = 40.679 us, max = 82.161 us, min = 6.258 us, total = 2.644 ms
	NodeInfoGcsService.grpc_server.GetAllNodeInfo - 53 total (0 active), Execution time: mean = 295.580 us, total = 15.666 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo.HandleRequestImpl - 53 total (0 active), Execution time: mean = 16.928 us, total = 897.205 us, Queueing time: mean = 33.278 us, max = 284.738 us, min = 5.185 us, total = 1.764 ms
	ClusterResourceManager.ResetRemoteNodeView - 40 total (1 active), Execution time: mean = 7.828 us, total = 313.116 us, Queueing time: mean = 74.597 us, max = 1.050 ms, min = 22.065 us, total = 2.984 ms
	HealthCheck - 38 total (0 active), Execution time: mean = 5.866 us, total = 222.890 us, Queueing time: mean = 43.091 us, max = 56.937 us, min = 15.854 us, total = 1.637 ms
	NodeInfoGcsService.grpc_server.GetClusterId.HandleRequestImpl - 29 total (0 active), Execution time: mean = 15.296 us, total = 443.594 us, Queueing time: mean = 23.785 us, max = 56.294 us, min = 7.831 us, total = 689.753 us
	NodeInfoGcsService.grpc_server.GetClusterId - 29 total (0 active), Execution time: mean = 188.320 us, total = 5.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.CheckAlive.HandleRequestImpl - 26 total (0 active), Execution time: mean = 15.894 us, total = 413.238 us, Queueing time: mean = 48.091 us, max = 72.578 us, min = 19.854 us, total = 1.250 ms
	NodeInfoGcsService.grpc_server.CheckAlive - 26 total (0 active), Execution time: mean = 302.912 us, total = 7.876 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo.HandleRequestImpl - 25 total (0 active), Execution time: mean = 11.905 us, total = 297.631 us, Queueing time: mean = 67.096 us, max = 637.610 us, min = 7.348 us, total = 1.677 ms
	WorkerInfoGcsService.grpc_server.AddWorkerInfo - 25 total (0 active), Execution time: mean = 273.568 us, total = 6.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	AutoscalerStateService.grpc_server.GetClusterResourceState - 24 total (0 active), Execution time: mean = 327.733 us, total = 7.866 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage.HandleRequestImpl - 24 total (0 active), Execution time: mean = 55.910 us, total = 1.342 ms, Queueing time: mean = 49.626 us, max = 70.464 us, min = 24.750 us, total = 1.191 ms
	AutoscalerStateService.grpc_server.GetClusterResourceState.HandleRequestImpl - 24 total (0 active), Execution time: mean = 36.927 us, total = 886.259 us, Queueing time: mean = 47.913 us, max = 67.350 us, min = 19.390 us, total = 1.150 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage - 24 total (0 active), Execution time: mean = 371.232 us, total = 8.910 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GCSServer.deadline_timer.debug_state_dump - 12 total (1 active), Execution time: mean = 1.722 ms, total = 20.668 ms, Queueing time: mean = 51.592 us, max = 67.552 us, min = 28.939 us, total = 619.101 us
	GcsInMemoryStore.GetAll - 6 total (0 active), Execution time: mean = 14.959 us, total = 89.752 us, Queueing time: mean = 36.963 us, max = 46.807 us, min = 6.503 us, total = 221.777 us
	PeriodicalRunner.RunFnPeriodically - 4 total (0 active), Execution time: mean = 157.890 us, total = 631.559 us, Queueing time: mean = 38.864 ms, max = 77.518 ms, min = 31.166 us, total = 155.456 ms
	GcsHealthCheckManager::MarkNodeHealthy - 2 total (0 active), Execution time: mean = 1.360 us, total = 2.720 us, Queueing time: mean = 4.211 ms, max = 8.372 ms, min = 50.984 us, total = 8.423 ms
	ActorInfoGcsService.grpc_server.CreateActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 383.161 us, total = 766.322 us, Queueing time: mean = 19.713 us, max = 21.036 us, min = 18.390 us, total = 39.426 us
	ActorInfoGcsService.grpc_server.RegisterActor - 2 total (0 active), Execution time: mean = 934.552 us, total = 1.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease - 2 total (0 active), Execution time: mean = 914.984 us, total = 1.830 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GCSServer.deadline_timer.debug_state_event_stats_print - 2 total (1 active, 1 running), Execution time: mean = 838.290 us, total = 1.677 ms, Queueing time: mean = 46.315 us, max = 92.629 us, min = 92.629 us, total = 92.629 us
	CoreWorkerService.grpc_client.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.RegisterActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 597.033 us, total = 1.194 ms, Queueing time: mean = 53.142 us, max = 57.937 us, min = 48.347 us, total = 106.284 us
	ActorInfoGcsService.grpc_server.CreateActor - 2 total (1 active), Execution time: mean = 28.808 ms, total = 57.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 2 total (1 active), Execution time: mean = 27.784 ms, total = 55.568 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsResourceManager::Update - 2 total (0 active), Execution time: mean = 59.562 us, total = 119.123 us, Queueing time: mean = 4.199 ms, max = 8.366 ms, min = 32.414 us, total = 8.398 ms
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 2 total (0 active), Execution time: mean = 302.106 us, total = 604.212 us, Queueing time: mean = 49.912 us, max = 52.127 us, min = 47.697 us, total = 99.824 us
	InternalKVGcsService.grpc_server.GetInternalConfig.HandleRequestImpl - 1 total (0 active), Execution time: mean = 51.973 us, total = 51.973 us, Queueing time: mean = 57.801 us, max = 57.801 us, min = 57.801 us, total = 57.801 us
	InternalKVGcsService.grpc_server.GetInternalConfig - 1 total (0 active), Execution time: mean = 449.486 us, total = 449.486 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.GetNextJobID - 1 total (0 active), Execution time: mean = 217.427 us, total = 217.427 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	JobInfoGcsService.grpc_server.GetNextJobID.HandleRequestImpl - 1 total (0 active), Execution time: mean = 7.157 us, total = 7.157 us, Queueing time: mean = 53.538 us, max = 53.538 us, min = 53.538 us, total = 53.538 us
	JobInfoGcsService.grpc_server.AddJob - 1 total (0 active), Execution time: mean = 179.464 us, total = 179.464 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.AddJob.HandleRequestImpl - 1 total (0 active), Execution time: mean = 62.233 us, total = 62.233 us, Queueing time: mean = 5.687 us, max = 5.687 us, min = 5.687 us, total = 5.687 us
	NodeInfoGcsService.grpc_server.RegisterNode - 1 total (0 active), Execution time: mean = 816.646 us, total = 816.646 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Delete - 1 total (0 active), Execution time: mean = 9.483 us, total = 9.483 us, Queueing time: mean = 7.193 us, max = 7.193 us, min = 7.193 us, total = 7.193 us
	JobInfoGcsService.grpc_server.GetAllJobInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 15.633 us, total = 15.633 us, Queueing time: mean = 54.801 us, max = 54.801 us, min = 54.801 us, total = 54.801 us
	GcsHealthCheckManager::AddNode - 1 total (0 active), Execution time: mean = 12.710 us, total = 12.710 us, Queueing time: mean = 554.000 ns, max = 554.000 ns, min = 554.000 ns, total = 554.000 ns
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 1 total (0 active), Execution time: mean = 337.099 us, total = 337.099 us, Queueing time: mean = 53.254 us, max = 53.254 us, min = 53.254 us, total = 53.254 us
	ActorInfoGcsService.grpc_server.GetActorInfo - 1 total (0 active), Execution time: mean = 340.669 us, total = 340.669 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.GetActorInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 21.827 us, total = 21.827 us, Queueing time: mean = 15.907 us, max = 15.907 us, min = 15.907 us, total = 15.907 us
	JobInfoGcsService.grpc_server.GetAllJobInfo - 1 total (0 active), Execution time: mean = 455.736 us, total = 455.736 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	InternalKVGcsService.grpc_server.InternalKVDel.HandleRequestImpl - 1 total (0 active), Execution time: mean = 50.454 us, total = 50.454 us, Queueing time: mean = 46.858 us, max = 46.858 us, min = 46.858 us, total = 46.858 us
	NodeInfoGcsService.grpc_server.RegisterNode.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.004 us, total = 112.004 us, Queueing time: mean = 50.963 us, max = 50.963 us, min = 50.963 us, total = 50.963 us
	GcsInMemoryStore.GetNextJobID - 1 total (0 active), Execution time: mean = 8.613 us, total = 8.613 us, Queueing time: mean = 6.080 us, max = 6.080 us, min = 6.080 us, total = 6.080 us
	InternalKVGcsService.grpc_server.InternalKVDel - 1 total (0 active), Execution time: mean = 340.770 us, total = 340.770 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:44:57,667 I 660691 660691] (gcs_server) gcs_server.cc:874: task_io_context Event stats:


Global stats: 6444 total (1 active)
Queueing time: mean = 19.003 us, max = 235.041 us, min = 3.480 us, total = 122.455 ms
Execution time:  mean = 123.968 us, total = 798.850 ms
Event stats:
	TaskInfoGcsService.grpc_server.AddTaskEventData - 2968 total (0 active), Execution time: mean = 253.305 us, total = 751.808 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	TaskInfoGcsService.grpc_server.AddTaskEventData.HandleRequestImpl - 2968 total (0 active), Execution time: mean = 12.747 us, total = 37.833 ms, Queueing time: mean = 39.588 us, max = 154.245 us, min = 4.863 us, total = 117.497 ms
	event_loop_lag_probe - 482 total (0 active), Execution time: mean = 18.759 us, total = 9.042 ms, Queueing time: mean = 6.628 us, max = 235.041 us, min = 3.480 us, total = 3.195 ms
	GcsTaskManager.GcJobSummary - 25 total (1 active), Execution time: mean = 6.501 us, total = 162.521 us, Queueing time: mean = 70.197 us, max = 105.437 us, min = 26.626 us, total = 1.755 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 4.380 us, total = 4.380 us, Queueing time: mean = 9.466 us, max = 9.466 us, min = 9.466 us, total = 9.466 us


[2025-07-05 18:44:57,668 I 660691 660691] (gcs_server) gcs_server.cc:874: pubsub_io_context Event stats:


Global stats: 754 total (29 active)
Queueing time: mean = 12.468 us, max = 882.198 us, min = 2.979 us, total = 9.401 ms
Execution time:  mean = 152.025 ms, total = 114.626 s
Event stats:
	event_loop_lag_probe - 482 total (0 active), Execution time: mean = 18.649 us, total = 8.989 ms, Queueing time: mean = 8.208 us, max = 882.198 us, min = 2.979 us, total = 3.956 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll.HandleRequestImpl - 66 total (0 active), Execution time: mean = 25.634 us, total = 1.692 ms, Queueing time: mean = 38.971 us, max = 160.291 us, min = 4.744 us, total = 2.572 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll - 66 total (28 active), Execution time: mean = 1.736 s, total = 114.590 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsPublish.HandleRequestImpl - 39 total (0 active), Execution time: mean = 70.912 us, total = 2.766 ms, Queueing time: mean = 51.623 us, max = 66.867 us, min = 12.086 us, total = 2.013 ms
	InternalPubSubGcsService.grpc_server.GcsPublish - 39 total (0 active), Execution time: mean = 370.572 us, total = 14.452 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch - 30 total (0 active), Execution time: mean = 252.052 us, total = 7.562 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch.HandleRequestImpl - 30 total (0 active), Execution time: mean = 28.413 us, total = 852.387 us, Queueing time: mean = 28.317 us, max = 142.940 us, min = 4.958 us, total = 849.505 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 3.882 us, total = 3.882 us, Queueing time: mean = 10.063 us, max = 10.063 us, min = 10.063 us, total = 10.063 us


[2025-07-05 18:44:57,668 I 660691 660691] (gcs_server) gcs_server.cc:874: ray_syncer_io_context Event stats:


Global stats: 488 total (0 active)
Queueing time: mean = 7.476 us, max = 411.929 us, min = 274.000 ns, total = 3.648 ms
Execution time:  mean = 19.390 us, total = 9.462 ms
Event stats:
	event_loop_lag_probe - 482 total (0 active), Execution time: mean = 19.298 us, total = 9.302 ms, Queueing time: mean = 7.306 us, max = 411.929 us, min = 3.168 us, total = 3.521 ms
	 - 2 total (0 active), Execution time: mean = 67.501 us, total = 135.001 us, Queueing time: mean = 53.227 us, max = 54.007 us, min = 52.447 us, total = 106.454 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 574.000 ns, total = 1.148 us, Queueing time: mean = 10.003 us, max = 11.342 us, min = 8.664 us, total = 20.006 us
	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 12.356 us, total = 24.712 us, Queueing time: mean = 294.000 ns, max = 314.000 ns, min = 274.000 ns, total = 588.000 ns


[2025-07-05 18:45:57,668 I 660691 660691] (gcs_server) gcs_server.cc:249: Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 1
- DrainNode request count: 0
- GetAllNodeInfo request count: 53

GcsActorManager: 
- RegisterActor request count: 2
- CreateActor request count: 2
- GetActorInfo request count: 1
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 2
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 1
- owners_: 1
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 1
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 36

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- WaitPlacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:
GCS_NODE_INFO_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 589
- current buffered bytes: 0
RAY_LOG_CHANNEL
- cumulative published messages: 58
- cumulative published bytes: 40120
- current buffered bytes: 891
GCS_JOB_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 288
- current buffered bytes: 288
GCS_ACTOR_CHANNEL
- cumulative published messages: 5
- cumulative published bytes: 1591
- current buffered bytes: 0

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 7
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 4
-Total num of actor creation tasks: 2
-Total num of actor tasks: 1
-Total num of normal tasks: 0
-Total num of driver tasks: 1

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 36
- pending demands:



[2025-07-05 18:45:57,669 I 660691 660691] (gcs_server) gcs_server.cc:870: Main service Event stats:


Global stats: 2719 total (8 active)
Queueing time: mean = 288.274 us, max = 77.518 ms, min = 110.000 ns, total = 783.818 ms
Execution time:  mean = 262.176 us, total = 712.857 ms
Event stats:
	event_loop_lag_probe - 722 total (0 active), Execution time: mean = 17.258 us, total = 12.460 ms, Queueing time: mean = 15.211 us, max = 6.320 ms, min = 2.282 us, total = 10.982 ms
	GcsInMemoryStore.Put - 348 total (0 active), Execution time: mean = 367.595 us, total = 127.923 ms, Queueing time: mean = 1.594 ms, max = 77.318 ms, min = 1.430 us, total = 554.737 ms
	RayletLoadPulled - 180 total (1 active), Execution time: mean = 190.991 us, total = 34.378 ms, Queueing time: mean = 54.398 us, max = 101.033 us, min = 2.435 us, total = 9.792 ms
	NodeManagerService.grpc_client.GetResourceLoad.OnReplyReceived - 178 total (0 active), Execution time: mean = 57.038 us, total = 10.153 ms, Queueing time: mean = 44.841 us, max = 77.883 us, min = 16.415 us, total = 7.982 ms
	NodeManagerService.grpc_client.GetResourceLoad - 178 total (0 active), Execution time: mean = 895.191 us, total = 159.344 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Get - 104 total (0 active), Execution time: mean = 12.249 us, total = 1.274 ms, Queueing time: mean = 6.505 us, max = 33.098 us, min = 1.323 us, total = 676.533 us
	InternalKVGcsService.grpc_server.InternalKVGet.HandleRequestImpl - 103 total (0 active), Execution time: mean = 24.216 us, total = 2.494 ms, Queueing time: mean = 44.240 us, max = 660.398 us, min = 5.120 us, total = 4.557 ms
	InternalKVGcsService.grpc_server.InternalKVGet - 103 total (0 active), Execution time: mean = 291.557 us, total = 30.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut - 89 total (0 active), Execution time: mean = 310.223 us, total = 27.610 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut.HandleRequestImpl - 89 total (0 active), Execution time: mean = 32.025 us, total = 2.850 ms, Queueing time: mean = 43.704 us, max = 82.161 us, min = 6.258 us, total = 3.890 ms
	ClusterResourceManager.ResetRemoteNodeView - 60 total (1 active), Execution time: mean = 7.576 us, total = 454.548 us, Queueing time: mean = 81.165 us, max = 1.050 ms, min = 22.065 us, total = 4.870 ms
	HealthCheck - 58 total (0 active), Execution time: mean = 6.229 us, total = 361.299 us, Queueing time: mean = 42.431 us, max = 58.394 us, min = 15.854 us, total = 2.461 ms
	NodeInfoGcsService.grpc_server.GetAllNodeInfo.HandleRequestImpl - 53 total (0 active), Execution time: mean = 16.928 us, total = 897.205 us, Queueing time: mean = 33.278 us, max = 284.738 us, min = 5.185 us, total = 1.764 ms
	NodeInfoGcsService.grpc_server.GetAllNodeInfo - 53 total (0 active), Execution time: mean = 295.580 us, total = 15.666 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.CheckAlive - 39 total (0 active), Execution time: mean = 301.116 us, total = 11.744 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.CheckAlive.HandleRequestImpl - 39 total (0 active), Execution time: mean = 15.402 us, total = 600.687 us, Queueing time: mean = 47.622 us, max = 72.578 us, min = 19.854 us, total = 1.857 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage - 36 total (0 active), Execution time: mean = 371.062 us, total = 13.358 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	AutoscalerStateService.grpc_server.GetClusterResourceState.HandleRequestImpl - 36 total (0 active), Execution time: mean = 36.948 us, total = 1.330 ms, Queueing time: mean = 49.429 us, max = 67.350 us, min = 19.390 us, total = 1.779 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage.HandleRequestImpl - 36 total (0 active), Execution time: mean = 54.893 us, total = 1.976 ms, Queueing time: mean = 51.326 us, max = 70.464 us, min = 24.750 us, total = 1.848 ms
	AutoscalerStateService.grpc_server.GetClusterResourceState - 36 total (0 active), Execution time: mean = 333.635 us, total = 12.011 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId - 29 total (0 active), Execution time: mean = 188.320 us, total = 5.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId.HandleRequestImpl - 29 total (0 active), Execution time: mean = 15.296 us, total = 443.594 us, Queueing time: mean = 23.785 us, max = 56.294 us, min = 7.831 us, total = 689.753 us
	WorkerInfoGcsService.grpc_server.AddWorkerInfo - 25 total (0 active), Execution time: mean = 273.568 us, total = 6.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo.HandleRequestImpl - 25 total (0 active), Execution time: mean = 11.905 us, total = 297.631 us, Queueing time: mean = 67.096 us, max = 637.610 us, min = 7.348 us, total = 1.677 ms
	GCSServer.deadline_timer.debug_state_dump - 18 total (1 active), Execution time: mean = 1.574 ms, total = 28.338 ms, Queueing time: mean = 55.220 us, max = 76.729 us, min = 24.113 us, total = 993.965 us
	GcsInMemoryStore.GetAll - 6 total (0 active), Execution time: mean = 14.959 us, total = 89.752 us, Queueing time: mean = 36.963 us, max = 46.807 us, min = 6.503 us, total = 221.777 us
	PeriodicalRunner.RunFnPeriodically - 4 total (0 active), Execution time: mean = 157.890 us, total = 631.559 us, Queueing time: mean = 38.864 ms, max = 77.518 ms, min = 31.166 us, total = 155.456 ms
	GCSServer.deadline_timer.debug_state_event_stats_print - 3 total (1 active, 1 running), Execution time: mean = 1.078 ms, total = 3.233 ms, Queueing time: mean = 55.206 us, max = 92.629 us, min = 72.990 us, total = 165.619 us
	CoreWorkerService.grpc_client.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.RegisterActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 597.033 us, total = 1.194 ms, Queueing time: mean = 53.142 us, max = 57.937 us, min = 48.347 us, total = 106.284 us
	NodeManagerService.grpc_client.RequestWorkerLease - 2 total (0 active), Execution time: mean = 914.984 us, total = 1.830 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsHealthCheckManager::MarkNodeHealthy - 2 total (0 active), Execution time: mean = 1.360 us, total = 2.720 us, Queueing time: mean = 4.211 ms, max = 8.372 ms, min = 50.984 us, total = 8.423 ms
	ActorInfoGcsService.grpc_server.RegisterActor - 2 total (0 active), Execution time: mean = 934.552 us, total = 1.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.CreateActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 383.161 us, total = 766.322 us, Queueing time: mean = 19.713 us, max = 21.036 us, min = 18.390 us, total = 39.426 us
	ActorInfoGcsService.grpc_server.CreateActor - 2 total (1 active), Execution time: mean = 28.808 ms, total = 57.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 2 total (1 active), Execution time: mean = 27.784 ms, total = 55.568 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsResourceManager::Update - 2 total (0 active), Execution time: mean = 59.562 us, total = 119.123 us, Queueing time: mean = 4.199 ms, max = 8.366 ms, min = 32.414 us, total = 8.398 ms
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 2 total (0 active), Execution time: mean = 302.106 us, total = 604.212 us, Queueing time: mean = 49.912 us, max = 52.127 us, min = 47.697 us, total = 99.824 us
	InternalKVGcsService.grpc_server.GetInternalConfig.HandleRequestImpl - 1 total (0 active), Execution time: mean = 51.973 us, total = 51.973 us, Queueing time: mean = 57.801 us, max = 57.801 us, min = 57.801 us, total = 57.801 us
	InternalKVGcsService.grpc_server.GetInternalConfig - 1 total (0 active), Execution time: mean = 449.486 us, total = 449.486 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.GetNextJobID - 1 total (0 active), Execution time: mean = 217.427 us, total = 217.427 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	JobInfoGcsService.grpc_server.GetNextJobID.HandleRequestImpl - 1 total (0 active), Execution time: mean = 7.157 us, total = 7.157 us, Queueing time: mean = 53.538 us, max = 53.538 us, min = 53.538 us, total = 53.538 us
	JobInfoGcsService.grpc_server.AddJob - 1 total (0 active), Execution time: mean = 179.464 us, total = 179.464 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.AddJob.HandleRequestImpl - 1 total (0 active), Execution time: mean = 62.233 us, total = 62.233 us, Queueing time: mean = 5.687 us, max = 5.687 us, min = 5.687 us, total = 5.687 us
	NodeInfoGcsService.grpc_server.RegisterNode - 1 total (0 active), Execution time: mean = 816.646 us, total = 816.646 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Delete - 1 total (0 active), Execution time: mean = 9.483 us, total = 9.483 us, Queueing time: mean = 7.193 us, max = 7.193 us, min = 7.193 us, total = 7.193 us
	JobInfoGcsService.grpc_server.GetAllJobInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 15.633 us, total = 15.633 us, Queueing time: mean = 54.801 us, max = 54.801 us, min = 54.801 us, total = 54.801 us
	GcsHealthCheckManager::AddNode - 1 total (0 active), Execution time: mean = 12.710 us, total = 12.710 us, Queueing time: mean = 554.000 ns, max = 554.000 ns, min = 554.000 ns, total = 554.000 ns
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 1 total (0 active), Execution time: mean = 337.099 us, total = 337.099 us, Queueing time: mean = 53.254 us, max = 53.254 us, min = 53.254 us, total = 53.254 us
	ActorInfoGcsService.grpc_server.GetActorInfo - 1 total (0 active), Execution time: mean = 340.669 us, total = 340.669 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.GetActorInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 21.827 us, total = 21.827 us, Queueing time: mean = 15.907 us, max = 15.907 us, min = 15.907 us, total = 15.907 us
	JobInfoGcsService.grpc_server.GetAllJobInfo - 1 total (0 active), Execution time: mean = 455.736 us, total = 455.736 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	InternalKVGcsService.grpc_server.InternalKVDel.HandleRequestImpl - 1 total (0 active), Execution time: mean = 50.454 us, total = 50.454 us, Queueing time: mean = 46.858 us, max = 46.858 us, min = 46.858 us, total = 46.858 us
	NodeInfoGcsService.grpc_server.RegisterNode.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.004 us, total = 112.004 us, Queueing time: mean = 50.963 us, max = 50.963 us, min = 50.963 us, total = 50.963 us
	GcsInMemoryStore.GetNextJobID - 1 total (0 active), Execution time: mean = 8.613 us, total = 8.613 us, Queueing time: mean = 6.080 us, max = 6.080 us, min = 6.080 us, total = 6.080 us
	InternalKVGcsService.grpc_server.InternalKVDel - 1 total (0 active), Execution time: mean = 340.770 us, total = 340.770 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:45:57,669 I 660691 660691] (gcs_server) gcs_server.cc:874: task_io_context Event stats:


Global stats: 9696 total (1 active)
Queueing time: mean = 19.210 us, max = 235.041 us, min = 3.093 us, total = 186.257 ms
Execution time:  mean = 124.683 us, total = 1.209 s
Event stats:
	TaskInfoGcsService.grpc_server.AddTaskEventData - 4468 total (0 active), Execution time: mean = 254.685 us, total = 1.138 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	TaskInfoGcsService.grpc_server.AddTaskEventData.HandleRequestImpl - 4468 total (0 active), Execution time: mean = 12.800 us, total = 57.190 ms, Queueing time: mean = 40.048 us, max = 186.151 us, min = 4.863 us, total = 178.934 ms
	event_loop_lag_probe - 722 total (0 active), Execution time: mean = 18.763 us, total = 13.547 ms, Queueing time: mean = 6.524 us, max = 235.041 us, min = 3.093 us, total = 4.710 ms
	GcsTaskManager.GcJobSummary - 37 total (1 active), Execution time: mean = 6.731 us, total = 249.043 us, Queueing time: mean = 70.345 us, max = 105.437 us, min = 26.626 us, total = 2.603 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 4.380 us, total = 4.380 us, Queueing time: mean = 9.466 us, max = 9.466 us, min = 9.466 us, total = 9.466 us


[2025-07-05 18:45:57,669 I 660691 660691] (gcs_server) gcs_server.cc:874: pubsub_io_context Event stats:


Global stats: 1070 total (29 active)
Queueing time: mean = 12.064 us, max = 882.198 us, min = 2.577 us, total = 12.909 ms
Execution time:  mean = 163.250 ms, total = 174.678 s
Event stats:
	event_loop_lag_probe - 722 total (0 active), Execution time: mean = 18.707 us, total = 13.507 ms, Queueing time: mean = 7.606 us, max = 882.198 us, min = 2.577 us, total = 5.492 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll.HandleRequestImpl - 85 total (0 active), Execution time: mean = 28.114 us, total = 2.390 ms, Queueing time: mean = 41.096 us, max = 160.291 us, min = 4.744 us, total = 3.493 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll - 85 total (28 active), Execution time: mean = 2.054 s, total = 174.628 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsPublish.HandleRequestImpl - 58 total (0 active), Execution time: mean = 72.119 us, total = 4.183 ms, Queueing time: mean = 52.829 us, max = 71.697 us, min = 12.086 us, total = 3.064 ms
	InternalPubSubGcsService.grpc_server.GcsPublish - 58 total (0 active), Execution time: mean = 372.777 us, total = 21.621 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch - 30 total (0 active), Execution time: mean = 252.052 us, total = 7.562 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch.HandleRequestImpl - 30 total (0 active), Execution time: mean = 28.413 us, total = 852.387 us, Queueing time: mean = 28.317 us, max = 142.940 us, min = 4.958 us, total = 849.505 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 3.882 us, total = 3.882 us, Queueing time: mean = 10.063 us, max = 10.063 us, min = 10.063 us, total = 10.063 us


[2025-07-05 18:45:57,670 I 660691 660691] (gcs_server) gcs_server.cc:874: ray_syncer_io_context Event stats:


Global stats: 728 total (0 active)
Queueing time: mean = 7.105 us, max = 411.929 us, min = 274.000 ns, total = 5.173 ms
Execution time:  mean = 19.096 us, total = 13.902 ms
Event stats:
	event_loop_lag_probe - 722 total (0 active), Execution time: mean = 19.032 us, total = 13.741 ms, Queueing time: mean = 6.988 us, max = 411.929 us, min = 3.168 us, total = 5.046 ms
	 - 2 total (0 active), Execution time: mean = 67.501 us, total = 135.001 us, Queueing time: mean = 53.227 us, max = 54.007 us, min = 52.447 us, total = 106.454 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 574.000 ns, total = 1.148 us, Queueing time: mean = 10.003 us, max = 11.342 us, min = 8.664 us, total = 20.006 us
	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 12.356 us, total = 24.712 us, Queueing time: mean = 294.000 ns, max = 314.000 ns, min = 274.000 ns, total = 588.000 ns


[2025-07-05 18:46:57,670 I 660691 660691] (gcs_server) gcs_server.cc:249: Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 1
- DrainNode request count: 0
- GetAllNodeInfo request count: 53

GcsActorManager: 
- RegisterActor request count: 2
- CreateActor request count: 2
- GetActorInfo request count: 1
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 2
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 1
- owners_: 1
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 1
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 48

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- WaitPlacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:
GCS_NODE_INFO_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 589
- current buffered bytes: 0
RAY_LOG_CHANNEL
- cumulative published messages: 76
- cumulative published bytes: 52641
- current buffered bytes: 891
GCS_JOB_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 288
- current buffered bytes: 288
GCS_ACTOR_CHANNEL
- cumulative published messages: 5
- cumulative published bytes: 1591
- current buffered bytes: 0

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 7
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 4
-Total num of actor creation tasks: 2
-Total num of actor tasks: 1
-Total num of normal tasks: 0
-Total num of driver tasks: 1

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 48
- pending demands:



[2025-07-05 18:46:57,671 I 660691 660691] (gcs_server) gcs_server.cc:870: Main service Event stats:


Global stats: 3440 total (8 active)
Queueing time: mean = 233.249 us, max = 77.518 ms, min = 110.000 ns, total = 802.375 ms
Execution time:  mean = 238.529 us, total = 820.541 ms
Event stats:
	event_loop_lag_probe - 962 total (0 active), Execution time: mean = 17.399 us, total = 16.738 ms, Queueing time: mean = 12.974 us, max = 6.320 ms, min = 2.282 us, total = 12.481 ms
	GcsInMemoryStore.Put - 444 total (0 active), Execution time: mean = 288.975 us, total = 128.305 ms, Queueing time: mean = 1.260 ms, max = 77.318 ms, min = 1.430 us, total = 559.335 ms
	RayletLoadPulled - 240 total (1 active), Execution time: mean = 187.726 us, total = 45.054 ms, Queueing time: mean = 55.855 us, max = 101.033 us, min = 2.435 us, total = 13.405 ms
	NodeManagerService.grpc_client.GetResourceLoad.OnReplyReceived - 238 total (0 active), Execution time: mean = 57.030 us, total = 13.573 ms, Queueing time: mean = 45.242 us, max = 77.883 us, min = 15.223 us, total = 10.768 ms
	NodeManagerService.grpc_client.GetResourceLoad - 238 total (0 active), Execution time: mean = 895.972 us, total = 213.241 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Get - 116 total (0 active), Execution time: mean = 12.520 us, total = 1.452 ms, Queueing time: mean = 6.746 us, max = 33.098 us, min = 1.323 us, total = 782.495 us
	InternalKVGcsService.grpc_server.InternalKVGet.HandleRequestImpl - 115 total (0 active), Execution time: mean = 25.860 us, total = 2.974 ms, Queueing time: mean = 44.449 us, max = 660.398 us, min = 5.120 us, total = 5.112 ms
	InternalKVGcsService.grpc_server.InternalKVGet - 115 total (0 active), Execution time: mean = 295.236 us, total = 33.952 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut - 113 total (0 active), Execution time: mean = 309.283 us, total = 34.949 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut.HandleRequestImpl - 113 total (0 active), Execution time: mean = 32.118 us, total = 3.629 ms, Queueing time: mean = 44.237 us, max = 82.161 us, min = 6.258 us, total = 4.999 ms
	ClusterResourceManager.ResetRemoteNodeView - 80 total (1 active), Execution time: mean = 7.413 us, total = 593.068 us, Queueing time: mean = 78.742 us, max = 1.050 ms, min = 22.065 us, total = 6.299 ms
	HealthCheck - 78 total (0 active), Execution time: mean = 6.111 us, total = 476.639 us, Queueing time: mean = 43.326 us, max = 58.394 us, min = 15.854 us, total = 3.379 ms
	NodeInfoGcsService.grpc_server.GetAllNodeInfo.HandleRequestImpl - 53 total (0 active), Execution time: mean = 16.928 us, total = 897.205 us, Queueing time: mean = 33.278 us, max = 284.738 us, min = 5.185 us, total = 1.764 ms
	NodeInfoGcsService.grpc_server.GetAllNodeInfo - 53 total (0 active), Execution time: mean = 295.580 us, total = 15.666 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.CheckAlive - 52 total (0 active), Execution time: mean = 298.490 us, total = 15.521 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.CheckAlive.HandleRequestImpl - 52 total (0 active), Execution time: mean = 14.989 us, total = 779.420 us, Queueing time: mean = 45.597 us, max = 72.578 us, min = 18.610 us, total = 2.371 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage - 48 total (0 active), Execution time: mean = 364.376 us, total = 17.490 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	AutoscalerStateService.grpc_server.GetClusterResourceState.HandleRequestImpl - 48 total (0 active), Execution time: mean = 38.002 us, total = 1.824 ms, Queueing time: mean = 48.167 us, max = 67.350 us, min = 19.390 us, total = 2.312 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage.HandleRequestImpl - 48 total (0 active), Execution time: mean = 54.276 us, total = 2.605 ms, Queueing time: mean = 48.756 us, max = 70.464 us, min = 19.944 us, total = 2.340 ms
	AutoscalerStateService.grpc_server.GetClusterResourceState - 48 total (0 active), Execution time: mean = 329.542 us, total = 15.818 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId - 29 total (0 active), Execution time: mean = 188.320 us, total = 5.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId.HandleRequestImpl - 29 total (0 active), Execution time: mean = 15.296 us, total = 443.594 us, Queueing time: mean = 23.785 us, max = 56.294 us, min = 7.831 us, total = 689.753 us
	WorkerInfoGcsService.grpc_server.AddWorkerInfo - 25 total (0 active), Execution time: mean = 273.568 us, total = 6.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo.HandleRequestImpl - 25 total (0 active), Execution time: mean = 11.905 us, total = 297.631 us, Queueing time: mean = 67.096 us, max = 637.610 us, min = 7.348 us, total = 1.677 ms
	GCSServer.deadline_timer.debug_state_dump - 24 total (1 active), Execution time: mean = 1.488 ms, total = 35.711 ms, Queueing time: mean = 55.357 us, max = 76.729 us, min = 24.113 us, total = 1.329 ms
	GcsInMemoryStore.GetAll - 6 total (0 active), Execution time: mean = 14.959 us, total = 89.752 us, Queueing time: mean = 36.963 us, max = 46.807 us, min = 6.503 us, total = 221.777 us
	GCSServer.deadline_timer.debug_state_event_stats_print - 4 total (1 active, 1 running), Execution time: mean = 1.230 ms, total = 4.920 ms, Queueing time: mean = 58.837 us, max = 92.629 us, min = 69.729 us, total = 235.348 us
	PeriodicalRunner.RunFnPeriodically - 4 total (0 active), Execution time: mean = 157.890 us, total = 631.559 us, Queueing time: mean = 38.864 ms, max = 77.518 ms, min = 31.166 us, total = 155.456 ms
	CoreWorkerService.grpc_client.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.RegisterActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 597.033 us, total = 1.194 ms, Queueing time: mean = 53.142 us, max = 57.937 us, min = 48.347 us, total = 106.284 us
	NodeManagerService.grpc_client.RequestWorkerLease - 2 total (0 active), Execution time: mean = 914.984 us, total = 1.830 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsHealthCheckManager::MarkNodeHealthy - 2 total (0 active), Execution time: mean = 1.360 us, total = 2.720 us, Queueing time: mean = 4.211 ms, max = 8.372 ms, min = 50.984 us, total = 8.423 ms
	ActorInfoGcsService.grpc_server.RegisterActor - 2 total (0 active), Execution time: mean = 934.552 us, total = 1.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.CreateActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 383.161 us, total = 766.322 us, Queueing time: mean = 19.713 us, max = 21.036 us, min = 18.390 us, total = 39.426 us
	ActorInfoGcsService.grpc_server.CreateActor - 2 total (1 active), Execution time: mean = 28.808 ms, total = 57.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 2 total (1 active), Execution time: mean = 27.784 ms, total = 55.568 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsResourceManager::Update - 2 total (0 active), Execution time: mean = 59.562 us, total = 119.123 us, Queueing time: mean = 4.199 ms, max = 8.366 ms, min = 32.414 us, total = 8.398 ms
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 2 total (0 active), Execution time: mean = 302.106 us, total = 604.212 us, Queueing time: mean = 49.912 us, max = 52.127 us, min = 47.697 us, total = 99.824 us
	InternalKVGcsService.grpc_server.GetInternalConfig.HandleRequestImpl - 1 total (0 active), Execution time: mean = 51.973 us, total = 51.973 us, Queueing time: mean = 57.801 us, max = 57.801 us, min = 57.801 us, total = 57.801 us
	InternalKVGcsService.grpc_server.GetInternalConfig - 1 total (0 active), Execution time: mean = 449.486 us, total = 449.486 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.GetNextJobID - 1 total (0 active), Execution time: mean = 217.427 us, total = 217.427 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	JobInfoGcsService.grpc_server.GetNextJobID.HandleRequestImpl - 1 total (0 active), Execution time: mean = 7.157 us, total = 7.157 us, Queueing time: mean = 53.538 us, max = 53.538 us, min = 53.538 us, total = 53.538 us
	JobInfoGcsService.grpc_server.AddJob - 1 total (0 active), Execution time: mean = 179.464 us, total = 179.464 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.AddJob.HandleRequestImpl - 1 total (0 active), Execution time: mean = 62.233 us, total = 62.233 us, Queueing time: mean = 5.687 us, max = 5.687 us, min = 5.687 us, total = 5.687 us
	NodeInfoGcsService.grpc_server.RegisterNode - 1 total (0 active), Execution time: mean = 816.646 us, total = 816.646 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Delete - 1 total (0 active), Execution time: mean = 9.483 us, total = 9.483 us, Queueing time: mean = 7.193 us, max = 7.193 us, min = 7.193 us, total = 7.193 us
	JobInfoGcsService.grpc_server.GetAllJobInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 15.633 us, total = 15.633 us, Queueing time: mean = 54.801 us, max = 54.801 us, min = 54.801 us, total = 54.801 us
	GcsHealthCheckManager::AddNode - 1 total (0 active), Execution time: mean = 12.710 us, total = 12.710 us, Queueing time: mean = 554.000 ns, max = 554.000 ns, min = 554.000 ns, total = 554.000 ns
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 1 total (0 active), Execution time: mean = 337.099 us, total = 337.099 us, Queueing time: mean = 53.254 us, max = 53.254 us, min = 53.254 us, total = 53.254 us
	ActorInfoGcsService.grpc_server.GetActorInfo - 1 total (0 active), Execution time: mean = 340.669 us, total = 340.669 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.GetActorInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 21.827 us, total = 21.827 us, Queueing time: mean = 15.907 us, max = 15.907 us, min = 15.907 us, total = 15.907 us
	JobInfoGcsService.grpc_server.GetAllJobInfo - 1 total (0 active), Execution time: mean = 455.736 us, total = 455.736 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	InternalKVGcsService.grpc_server.InternalKVDel.HandleRequestImpl - 1 total (0 active), Execution time: mean = 50.454 us, total = 50.454 us, Queueing time: mean = 46.858 us, max = 46.858 us, min = 46.858 us, total = 46.858 us
	NodeInfoGcsService.grpc_server.RegisterNode.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.004 us, total = 112.004 us, Queueing time: mean = 50.963 us, max = 50.963 us, min = 50.963 us, total = 50.963 us
	GcsInMemoryStore.GetNextJobID - 1 total (0 active), Execution time: mean = 8.613 us, total = 8.613 us, Queueing time: mean = 6.080 us, max = 6.080 us, min = 6.080 us, total = 6.080 us
	InternalKVGcsService.grpc_server.InternalKVDel - 1 total (0 active), Execution time: mean = 340.770 us, total = 340.770 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:46:57,671 I 660691 660691] (gcs_server) gcs_server.cc:874: task_io_context Event stats:


Global stats: 12948 total (1 active)
Queueing time: mean = 19.239 us, max = 235.041 us, min = 3.053 us, total = 249.109 ms
Execution time:  mean = 125.535 us, total = 1.625 s
Event stats:
	TaskInfoGcsService.grpc_server.AddTaskEventData - 5968 total (0 active), Execution time: mean = 256.374 us, total = 1.530 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	TaskInfoGcsService.grpc_server.AddTaskEventData.HandleRequestImpl - 5968 total (0 active), Execution time: mean = 12.851 us, total = 76.694 ms, Queueing time: mean = 40.119 us, max = 186.151 us, min = 4.607 us, total = 239.427 ms
	event_loop_lag_probe - 962 total (0 active), Execution time: mean = 19.071 us, total = 18.346 ms, Queueing time: mean = 6.572 us, max = 235.041 us, min = 3.053 us, total = 6.323 ms
	GcsTaskManager.GcJobSummary - 49 total (1 active), Execution time: mean = 6.873 us, total = 336.761 us, Queueing time: mean = 68.363 us, max = 111.068 us, min = 22.712 us, total = 3.350 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 4.380 us, total = 4.380 us, Queueing time: mean = 9.466 us, max = 9.466 us, min = 9.466 us, total = 9.466 us


[2025-07-05 18:46:57,671 I 660691 660691] (gcs_server) gcs_server.cc:874: pubsub_io_context Event stats:


Global stats: 1382 total (29 active)
Queueing time: mean = 11.788 us, max = 882.198 us, min = 2.577 us, total = 16.291 ms
Execution time:  mean = 169.896 ms, total = 234.796 s
Event stats:
	event_loop_lag_probe - 962 total (0 active), Execution time: mean = 18.899 us, total = 18.181 ms, Queueing time: mean = 7.362 us, max = 882.198 us, min = 2.577 us, total = 7.082 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll.HandleRequestImpl - 103 total (0 active), Execution time: mean = 29.150 us, total = 3.002 ms, Queueing time: mean = 42.374 us, max = 160.291 us, min = 4.744 us, total = 4.365 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll - 103 total (28 active), Execution time: mean = 2.279 s, total = 234.733 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsPublish.HandleRequestImpl - 76 total (0 active), Execution time: mean = 71.003 us, total = 5.396 ms, Queueing time: mean = 52.434 us, max = 71.697 us, min = 12.086 us, total = 3.985 ms
	InternalPubSubGcsService.grpc_server.GcsPublish - 76 total (0 active), Execution time: mean = 369.908 us, total = 28.113 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch - 30 total (0 active), Execution time: mean = 252.052 us, total = 7.562 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch.HandleRequestImpl - 30 total (0 active), Execution time: mean = 28.413 us, total = 852.387 us, Queueing time: mean = 28.317 us, max = 142.940 us, min = 4.958 us, total = 849.505 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 3.882 us, total = 3.882 us, Queueing time: mean = 10.063 us, max = 10.063 us, min = 10.063 us, total = 10.063 us


[2025-07-05 18:46:57,671 I 660691 660691] (gcs_server) gcs_server.cc:874: ray_syncer_io_context Event stats:


Global stats: 968 total (0 active)
Queueing time: mean = 6.932 us, max = 411.929 us, min = 274.000 ns, total = 6.711 ms
Execution time:  mean = 19.100 us, total = 18.489 ms
Event stats:
	event_loop_lag_probe - 962 total (0 active), Execution time: mean = 19.052 us, total = 18.328 ms, Queueing time: mean = 6.844 us, max = 411.929 us, min = 3.168 us, total = 6.584 ms
	 - 2 total (0 active), Execution time: mean = 67.501 us, total = 135.001 us, Queueing time: mean = 53.227 us, max = 54.007 us, min = 52.447 us, total = 106.454 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 574.000 ns, total = 1.148 us, Queueing time: mean = 10.003 us, max = 11.342 us, min = 8.664 us, total = 20.006 us
	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 12.356 us, total = 24.712 us, Queueing time: mean = 294.000 ns, max = 314.000 ns, min = 274.000 ns, total = 588.000 ns


[2025-07-05 18:47:57,671 I 660691 660691] (gcs_server) gcs_server.cc:249: Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 1
- DrainNode request count: 0
- GetAllNodeInfo request count: 53

GcsActorManager: 
- RegisterActor request count: 2
- CreateActor request count: 2
- GetActorInfo request count: 1
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 2
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 1
- owners_: 1
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 1
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 60

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- WaitPlacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:
GCS_NODE_INFO_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 589
- current buffered bytes: 0
RAY_LOG_CHANNEL
- cumulative published messages: 94
- cumulative published bytes: 65162
- current buffered bytes: 891
GCS_JOB_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 288
- current buffered bytes: 288
GCS_ACTOR_CHANNEL
- cumulative published messages: 5
- cumulative published bytes: 1591
- current buffered bytes: 0

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 7
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 4
-Total num of actor creation tasks: 2
-Total num of actor tasks: 1
-Total num of normal tasks: 0
-Total num of driver tasks: 1

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 60
- pending demands:



[2025-07-05 18:47:57,672 I 660691 660691] (gcs_server) gcs_server.cc:870: Main service Event stats:


Global stats: 4160 total (8 active)
Queueing time: mean = 197.420 us, max = 77.518 ms, min = 110.000 ns, total = 821.266 ms
Execution time:  mean = 224.396 us, total = 933.486 ms
Event stats:
	event_loop_lag_probe - 1201 total (0 active), Execution time: mean = 17.605 us, total = 21.144 ms, Queueing time: mean = 11.739 us, max = 6.320 ms, min = 2.282 us, total = 14.099 ms
	GcsInMemoryStore.Put - 540 total (0 active), Execution time: mean = 238.317 us, total = 128.691 ms, Queueing time: mean = 1.045 ms, max = 77.318 ms, min = 1.430 us, total = 564.379 ms
	RayletLoadPulled - 300 total (1 active), Execution time: mean = 189.600 us, total = 56.880 ms, Queueing time: mean = 54.555 us, max = 121.038 us, min = 2.435 us, total = 16.367 ms
	NodeManagerService.grpc_client.GetResourceLoad.OnReplyReceived - 298 total (0 active), Execution time: mean = 58.168 us, total = 17.334 ms, Queueing time: mean = 45.364 us, max = 77.883 us, min = 15.223 us, total = 13.518 ms
	NodeManagerService.grpc_client.GetResourceLoad - 298 total (0 active), Execution time: mean = 905.285 us, total = 269.775 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut - 137 total (0 active), Execution time: mean = 310.663 us, total = 42.561 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut.HandleRequestImpl - 137 total (0 active), Execution time: mean = 33.161 us, total = 4.543 ms, Queueing time: mean = 43.999 us, max = 82.161 us, min = 6.258 us, total = 6.028 ms
	GcsInMemoryStore.Get - 128 total (0 active), Execution time: mean = 12.689 us, total = 1.624 ms, Queueing time: mean = 6.872 us, max = 33.098 us, min = 1.323 us, total = 879.637 us
	InternalKVGcsService.grpc_server.InternalKVGet - 127 total (0 active), Execution time: mean = 300.809 us, total = 38.203 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVGet.HandleRequestImpl - 127 total (0 active), Execution time: mean = 27.414 us, total = 3.482 ms, Queueing time: mean = 44.636 us, max = 660.398 us, min = 5.120 us, total = 5.669 ms
	ClusterResourceManager.ResetRemoteNodeView - 100 total (1 active), Execution time: mean = 7.373 us, total = 737.348 us, Queueing time: mean = 79.950 us, max = 1.050 ms, min = 22.065 us, total = 7.995 ms
	HealthCheck - 98 total (0 active), Execution time: mean = 6.066 us, total = 594.451 us, Queueing time: mean = 44.226 us, max = 58.394 us, min = 15.854 us, total = 4.334 ms
	NodeInfoGcsService.grpc_server.CheckAlive.HandleRequestImpl - 65 total (0 active), Execution time: mean = 15.223 us, total = 989.485 us, Queueing time: mean = 46.193 us, max = 72.578 us, min = 18.610 us, total = 3.003 ms
	NodeInfoGcsService.grpc_server.CheckAlive - 65 total (0 active), Execution time: mean = 299.908 us, total = 19.494 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage - 60 total (0 active), Execution time: mean = 368.672 us, total = 22.120 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	AutoscalerStateService.grpc_server.GetClusterResourceState.HandleRequestImpl - 60 total (0 active), Execution time: mean = 38.168 us, total = 2.290 ms, Queueing time: mean = 47.554 us, max = 67.350 us, min = 19.390 us, total = 2.853 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage.HandleRequestImpl - 60 total (0 active), Execution time: mean = 54.627 us, total = 3.278 ms, Queueing time: mean = 50.266 us, max = 70.464 us, min = 19.944 us, total = 3.016 ms
	AutoscalerStateService.grpc_server.GetClusterResourceState - 60 total (0 active), Execution time: mean = 327.639 us, total = 19.658 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo - 53 total (0 active), Execution time: mean = 295.580 us, total = 15.666 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo.HandleRequestImpl - 53 total (0 active), Execution time: mean = 16.928 us, total = 897.205 us, Queueing time: mean = 33.278 us, max = 284.738 us, min = 5.185 us, total = 1.764 ms
	GCSServer.deadline_timer.debug_state_dump - 30 total (1 active), Execution time: mean = 1.420 ms, total = 42.598 ms, Queueing time: mean = 53.045 us, max = 76.729 us, min = 24.113 us, total = 1.591 ms
	NodeInfoGcsService.grpc_server.GetClusterId - 29 total (0 active), Execution time: mean = 188.320 us, total = 5.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId.HandleRequestImpl - 29 total (0 active), Execution time: mean = 15.296 us, total = 443.594 us, Queueing time: mean = 23.785 us, max = 56.294 us, min = 7.831 us, total = 689.753 us
	WorkerInfoGcsService.grpc_server.AddWorkerInfo - 25 total (0 active), Execution time: mean = 273.568 us, total = 6.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo.HandleRequestImpl - 25 total (0 active), Execution time: mean = 11.905 us, total = 297.631 us, Queueing time: mean = 67.096 us, max = 637.610 us, min = 7.348 us, total = 1.677 ms
	GcsInMemoryStore.GetAll - 6 total (0 active), Execution time: mean = 14.959 us, total = 89.752 us, Queueing time: mean = 36.963 us, max = 46.807 us, min = 6.503 us, total = 221.777 us
	GCSServer.deadline_timer.debug_state_event_stats_print - 5 total (1 active, 1 running), Execution time: mean = 1.311 ms, total = 6.556 ms, Queueing time: mean = 61.241 us, max = 92.629 us, min = 69.729 us, total = 306.207 us
	PeriodicalRunner.RunFnPeriodically - 4 total (0 active), Execution time: mean = 157.890 us, total = 631.559 us, Queueing time: mean = 38.864 ms, max = 77.518 ms, min = 31.166 us, total = 155.456 ms
	CoreWorkerService.grpc_client.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.RegisterActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 597.033 us, total = 1.194 ms, Queueing time: mean = 53.142 us, max = 57.937 us, min = 48.347 us, total = 106.284 us
	NodeManagerService.grpc_client.RequestWorkerLease - 2 total (0 active), Execution time: mean = 914.984 us, total = 1.830 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsHealthCheckManager::MarkNodeHealthy - 2 total (0 active), Execution time: mean = 1.360 us, total = 2.720 us, Queueing time: mean = 4.211 ms, max = 8.372 ms, min = 50.984 us, total = 8.423 ms
	ActorInfoGcsService.grpc_server.RegisterActor - 2 total (0 active), Execution time: mean = 934.552 us, total = 1.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.CreateActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 383.161 us, total = 766.322 us, Queueing time: mean = 19.713 us, max = 21.036 us, min = 18.390 us, total = 39.426 us
	ActorInfoGcsService.grpc_server.CreateActor - 2 total (1 active), Execution time: mean = 28.808 ms, total = 57.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 2 total (1 active), Execution time: mean = 27.784 ms, total = 55.568 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsResourceManager::Update - 2 total (0 active), Execution time: mean = 59.562 us, total = 119.123 us, Queueing time: mean = 4.199 ms, max = 8.366 ms, min = 32.414 us, total = 8.398 ms
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 2 total (0 active), Execution time: mean = 302.106 us, total = 604.212 us, Queueing time: mean = 49.912 us, max = 52.127 us, min = 47.697 us, total = 99.824 us
	InternalKVGcsService.grpc_server.GetInternalConfig.HandleRequestImpl - 1 total (0 active), Execution time: mean = 51.973 us, total = 51.973 us, Queueing time: mean = 57.801 us, max = 57.801 us, min = 57.801 us, total = 57.801 us
	InternalKVGcsService.grpc_server.GetInternalConfig - 1 total (0 active), Execution time: mean = 449.486 us, total = 449.486 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.GetNextJobID - 1 total (0 active), Execution time: mean = 217.427 us, total = 217.427 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	JobInfoGcsService.grpc_server.GetNextJobID.HandleRequestImpl - 1 total (0 active), Execution time: mean = 7.157 us, total = 7.157 us, Queueing time: mean = 53.538 us, max = 53.538 us, min = 53.538 us, total = 53.538 us
	JobInfoGcsService.grpc_server.AddJob - 1 total (0 active), Execution time: mean = 179.464 us, total = 179.464 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.AddJob.HandleRequestImpl - 1 total (0 active), Execution time: mean = 62.233 us, total = 62.233 us, Queueing time: mean = 5.687 us, max = 5.687 us, min = 5.687 us, total = 5.687 us
	NodeInfoGcsService.grpc_server.RegisterNode - 1 total (0 active), Execution time: mean = 816.646 us, total = 816.646 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Delete - 1 total (0 active), Execution time: mean = 9.483 us, total = 9.483 us, Queueing time: mean = 7.193 us, max = 7.193 us, min = 7.193 us, total = 7.193 us
	JobInfoGcsService.grpc_server.GetAllJobInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 15.633 us, total = 15.633 us, Queueing time: mean = 54.801 us, max = 54.801 us, min = 54.801 us, total = 54.801 us
	GcsHealthCheckManager::AddNode - 1 total (0 active), Execution time: mean = 12.710 us, total = 12.710 us, Queueing time: mean = 554.000 ns, max = 554.000 ns, min = 554.000 ns, total = 554.000 ns
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 1 total (0 active), Execution time: mean = 337.099 us, total = 337.099 us, Queueing time: mean = 53.254 us, max = 53.254 us, min = 53.254 us, total = 53.254 us
	ActorInfoGcsService.grpc_server.GetActorInfo - 1 total (0 active), Execution time: mean = 340.669 us, total = 340.669 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.GetActorInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 21.827 us, total = 21.827 us, Queueing time: mean = 15.907 us, max = 15.907 us, min = 15.907 us, total = 15.907 us
	JobInfoGcsService.grpc_server.GetAllJobInfo - 1 total (0 active), Execution time: mean = 455.736 us, total = 455.736 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	InternalKVGcsService.grpc_server.InternalKVDel.HandleRequestImpl - 1 total (0 active), Execution time: mean = 50.454 us, total = 50.454 us, Queueing time: mean = 46.858 us, max = 46.858 us, min = 46.858 us, total = 46.858 us
	NodeInfoGcsService.grpc_server.RegisterNode.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.004 us, total = 112.004 us, Queueing time: mean = 50.963 us, max = 50.963 us, min = 50.963 us, total = 50.963 us
	GcsInMemoryStore.GetNextJobID - 1 total (0 active), Execution time: mean = 8.613 us, total = 8.613 us, Queueing time: mean = 6.080 us, max = 6.080 us, min = 6.080 us, total = 6.080 us
	InternalKVGcsService.grpc_server.InternalKVDel - 1 total (0 active), Execution time: mean = 340.770 us, total = 340.770 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:47:57,672 I 660691 660691] (gcs_server) gcs_server.cc:874: task_io_context Event stats:


Global stats: 16199 total (1 active)
Queueing time: mean = 19.252 us, max = 235.041 us, min = 3.053 us, total = 311.858 ms
Execution time:  mean = 125.804 us, total = 2.038 s
Event stats:
	TaskInfoGcsService.grpc_server.AddTaskEventData - 7468 total (0 active), Execution time: mean = 256.970 us, total = 1.919 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	TaskInfoGcsService.grpc_server.AddTaskEventData.HandleRequestImpl - 7468 total (0 active), Execution time: mean = 12.793 us, total = 95.539 ms, Queueing time: mean = 40.193 us, max = 186.151 us, min = 4.607 us, total = 300.161 ms
	event_loop_lag_probe - 1201 total (0 active), Execution time: mean = 19.052 us, total = 22.882 ms, Queueing time: mean = 6.511 us, max = 235.041 us, min = 3.053 us, total = 7.819 ms
	GcsTaskManager.GcJobSummary - 61 total (1 active), Execution time: mean = 7.028 us, total = 428.681 us, Queueing time: mean = 63.418 us, max = 111.068 us, min = 22.712 us, total = 3.869 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 4.380 us, total = 4.380 us, Queueing time: mean = 9.466 us, max = 9.466 us, min = 9.466 us, total = 9.466 us


[2025-07-05 18:47:57,672 I 660691 660691] (gcs_server) gcs_server.cc:874: pubsub_io_context Event stats:


Global stats: 1694 total (29 active)
Queueing time: mean = 11.675 us, max = 882.198 us, min = 2.577 us, total = 19.777 ms
Execution time:  mean = 174.049 ms, total = 294.838 s
Event stats:
	event_loop_lag_probe - 1201 total (0 active), Execution time: mean = 18.751 us, total = 22.520 ms, Queueing time: mean = 7.168 us, max = 882.198 us, min = 2.577 us, total = 8.609 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll.HandleRequestImpl - 121 total (0 active), Execution time: mean = 29.547 us, total = 3.575 ms, Queueing time: mean = 43.985 us, max = 160.291 us, min = 4.744 us, total = 5.322 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll - 121 total (28 active), Execution time: mean = 2.436 s, total = 294.763 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsPublish.HandleRequestImpl - 94 total (0 active), Execution time: mean = 69.355 us, total = 6.519 ms, Queueing time: mean = 52.227 us, max = 71.697 us, min = 12.086 us, total = 4.909 ms
	InternalPubSubGcsService.grpc_server.GcsPublish - 94 total (0 active), Execution time: mean = 368.732 us, total = 34.661 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch - 30 total (0 active), Execution time: mean = 252.052 us, total = 7.562 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch.HandleRequestImpl - 30 total (0 active), Execution time: mean = 28.413 us, total = 852.387 us, Queueing time: mean = 28.317 us, max = 142.940 us, min = 4.958 us, total = 849.505 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.325 us, total = 16.650 us, Queueing time: mean = 38.544 us, max = 77.089 us, min = 77.089 us, total = 77.089 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 3.882 us, total = 3.882 us, Queueing time: mean = 10.063 us, max = 10.063 us, min = 10.063 us, total = 10.063 us


[2025-07-05 18:47:57,672 I 660691 660691] (gcs_server) gcs_server.cc:874: ray_syncer_io_context Event stats:


Global stats: 1207 total (0 active)
Queueing time: mean = 6.794 us, max = 411.929 us, min = 274.000 ns, total = 8.200 ms
Execution time:  mean = 19.030 us, total = 22.969 ms
Event stats:
	event_loop_lag_probe - 1201 total (0 active), Execution time: mean = 18.991 us, total = 22.808 ms, Queueing time: mean = 6.722 us, max = 411.929 us, min = 3.168 us, total = 8.073 ms
	 - 2 total (0 active), Execution time: mean = 67.501 us, total = 135.001 us, Queueing time: mean = 53.227 us, max = 54.007 us, min = 52.447 us, total = 106.454 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 574.000 ns, total = 1.148 us, Queueing time: mean = 10.003 us, max = 11.342 us, min = 8.664 us, total = 20.006 us
	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 12.356 us, total = 24.712 us, Queueing time: mean = 294.000 ns, max = 314.000 ns, min = 274.000 ns, total = 588.000 ns


[2025-07-05 18:48:57,672 I 660691 660691] (gcs_server) gcs_server.cc:249: Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 1
- DrainNode request count: 0
- GetAllNodeInfo request count: 53

GcsActorManager: 
- RegisterActor request count: 2
- CreateActor request count: 2
- GetActorInfo request count: 1
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 2
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 1
- owners_: 1
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 1
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 72

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- WaitPlacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:
GCS_NODE_INFO_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 589
- current buffered bytes: 0
RAY_LOG_CHANNEL
- cumulative published messages: 113
- cumulative published bytes: 77820
- current buffered bytes: 137
GCS_JOB_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 288
- current buffered bytes: 288
GCS_ACTOR_CHANNEL
- cumulative published messages: 5
- cumulative published bytes: 1591
- current buffered bytes: 0

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 7
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 4
-Total num of actor creation tasks: 2
-Total num of actor tasks: 1
-Total num of normal tasks: 0
-Total num of driver tasks: 1

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 72
- pending demands:



[2025-07-05 18:48:57,673 I 660691 660691] (gcs_server) gcs_server.cc:870: Main service Event stats:


Global stats: 4881 total (8 active)
Queueing time: mean = 171.959 us, max = 77.518 ms, min = 110.000 ns, total = 839.331 ms
Execution time:  mean = 213.038 us, total = 1.040 s
Event stats:
	event_loop_lag_probe - 1441 total (0 active), Execution time: mean = 17.522 us, total = 25.249 ms, Queueing time: mean = 10.822 us, max = 6.320 ms, min = 2.282 us, total = 15.594 ms
	GcsInMemoryStore.Put - 636 total (0 active), Execution time: mean = 202.967 us, total = 129.087 ms, Queueing time: mean = 894.769 us, max = 77.318 ms, min = 1.430 us, total = 569.073 ms
	RayletLoadPulled - 360 total (1 active), Execution time: mean = 188.054 us, total = 67.699 ms, Queueing time: mean = 55.072 us, max = 121.038 us, min = 2.435 us, total = 19.826 ms
	NodeManagerService.grpc_client.GetResourceLoad.OnReplyReceived - 358 total (0 active), Execution time: mean = 57.299 us, total = 20.513 ms, Queueing time: mean = 44.351 us, max = 77.883 us, min = 15.223 us, total = 15.878 ms
	NodeManagerService.grpc_client.GetResourceLoad - 358 total (0 active), Execution time: mean = 899.076 us, total = 321.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut - 161 total (0 active), Execution time: mean = 309.977 us, total = 49.906 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut.HandleRequestImpl - 161 total (0 active), Execution time: mean = 32.992 us, total = 5.312 ms, Queueing time: mean = 44.472 us, max = 82.161 us, min = 6.258 us, total = 7.160 ms
	GcsInMemoryStore.Get - 140 total (0 active), Execution time: mean = 12.939 us, total = 1.811 ms, Queueing time: mean = 6.965 us, max = 33.098 us, min = 1.323 us, total = 975.060 us
	InternalKVGcsService.grpc_server.InternalKVGet - 139 total (0 active), Execution time: mean = 305.205 us, total = 42.424 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVGet.HandleRequestImpl - 139 total (0 active), Execution time: mean = 28.589 us, total = 3.974 ms, Queueing time: mean = 44.718 us, max = 660.398 us, min = 5.120 us, total = 6.216 ms
	ClusterResourceManager.ResetRemoteNodeView - 120 total (1 active), Execution time: mean = 7.332 us, total = 879.805 us, Queueing time: mean = 77.035 us, max = 1.050 ms, min = 18.891 us, total = 9.244 ms
	HealthCheck - 118 total (0 active), Execution time: mean = 5.980 us, total = 705.676 us, Queueing time: mean = 43.358 us, max = 58.394 us, min = 15.854 us, total = 5.116 ms
	NodeInfoGcsService.grpc_server.CheckAlive.HandleRequestImpl - 78 total (0 active), Execution time: mean = 15.104 us, total = 1.178 ms, Queueing time: mean = 47.045 us, max = 72.578 us, min = 18.610 us, total = 3.670 ms
	NodeInfoGcsService.grpc_server.CheckAlive - 78 total (0 active), Execution time: mean = 301.680 us, total = 23.531 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage - 72 total (0 active), Execution time: mean = 373.053 us, total = 26.860 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	AutoscalerStateService.grpc_server.GetClusterResourceState.HandleRequestImpl - 72 total (0 active), Execution time: mean = 37.949 us, total = 2.732 ms, Queueing time: mean = 47.529 us, max = 67.350 us, min = 19.390 us, total = 3.422 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage.HandleRequestImpl - 72 total (0 active), Execution time: mean = 54.798 us, total = 3.945 ms, Queueing time: mean = 50.360 us, max = 70.464 us, min = 19.944 us, total = 3.626 ms
	AutoscalerStateService.grpc_server.GetClusterResourceState - 72 total (0 active), Execution time: mean = 325.602 us, total = 23.443 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo - 53 total (0 active), Execution time: mean = 295.580 us, total = 15.666 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo.HandleRequestImpl - 53 total (0 active), Execution time: mean = 16.928 us, total = 897.205 us, Queueing time: mean = 33.278 us, max = 284.738 us, min = 5.185 us, total = 1.764 ms
	GCSServer.deadline_timer.debug_state_dump - 36 total (1 active), Execution time: mean = 1.398 ms, total = 50.313 ms, Queueing time: mean = 54.099 us, max = 76.729 us, min = 24.113 us, total = 1.948 ms
	NodeInfoGcsService.grpc_server.GetClusterId - 29 total (0 active), Execution time: mean = 188.320 us, total = 5.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId.HandleRequestImpl - 29 total (0 active), Execution time: mean = 15.296 us, total = 443.594 us, Queueing time: mean = 23.785 us, max = 56.294 us, min = 7.831 us, total = 689.753 us
	WorkerInfoGcsService.grpc_server.AddWorkerInfo - 25 total (0 active), Execution time: mean = 273.568 us, total = 6.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo.HandleRequestImpl - 25 total (0 active), Execution time: mean = 11.905 us, total = 297.631 us, Queueing time: mean = 67.096 us, max = 637.610 us, min = 7.348 us, total = 1.677 ms
	GCSServer.deadline_timer.debug_state_event_stats_print - 6 total (1 active, 1 running), Execution time: mean = 1.245 ms, total = 7.472 ms, Queueing time: mean = 59.258 us, max = 92.629 us, min = 49.340 us, total = 355.547 us
	GcsInMemoryStore.GetAll - 6 total (0 active), Execution time: mean = 14.959 us, total = 89.752 us, Queueing time: mean = 36.963 us, max = 46.807 us, min = 6.503 us, total = 221.777 us
	PeriodicalRunner.RunFnPeriodically - 4 total (0 active), Execution time: mean = 157.890 us, total = 631.559 us, Queueing time: mean = 38.864 ms, max = 77.518 ms, min = 31.166 us, total = 155.456 ms
	CoreWorkerService.grpc_client.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.RegisterActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 597.033 us, total = 1.194 ms, Queueing time: mean = 53.142 us, max = 57.937 us, min = 48.347 us, total = 106.284 us
	NodeManagerService.grpc_client.RequestWorkerLease - 2 total (0 active), Execution time: mean = 914.984 us, total = 1.830 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsHealthCheckManager::MarkNodeHealthy - 2 total (0 active), Execution time: mean = 1.360 us, total = 2.720 us, Queueing time: mean = 4.211 ms, max = 8.372 ms, min = 50.984 us, total = 8.423 ms
	ActorInfoGcsService.grpc_server.RegisterActor - 2 total (0 active), Execution time: mean = 934.552 us, total = 1.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.CreateActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 383.161 us, total = 766.322 us, Queueing time: mean = 19.713 us, max = 21.036 us, min = 18.390 us, total = 39.426 us
	ActorInfoGcsService.grpc_server.CreateActor - 2 total (1 active), Execution time: mean = 28.808 ms, total = 57.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 2 total (1 active), Execution time: mean = 27.784 ms, total = 55.568 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsResourceManager::Update - 2 total (0 active), Execution time: mean = 59.562 us, total = 119.123 us, Queueing time: mean = 4.199 ms, max = 8.366 ms, min = 32.414 us, total = 8.398 ms
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 2 total (0 active), Execution time: mean = 302.106 us, total = 604.212 us, Queueing time: mean = 49.912 us, max = 52.127 us, min = 47.697 us, total = 99.824 us
	InternalKVGcsService.grpc_server.GetInternalConfig.HandleRequestImpl - 1 total (0 active), Execution time: mean = 51.973 us, total = 51.973 us, Queueing time: mean = 57.801 us, max = 57.801 us, min = 57.801 us, total = 57.801 us
	InternalKVGcsService.grpc_server.GetInternalConfig - 1 total (0 active), Execution time: mean = 449.486 us, total = 449.486 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.GetNextJobID - 1 total (0 active), Execution time: mean = 217.427 us, total = 217.427 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	JobInfoGcsService.grpc_server.GetNextJobID.HandleRequestImpl - 1 total (0 active), Execution time: mean = 7.157 us, total = 7.157 us, Queueing time: mean = 53.538 us, max = 53.538 us, min = 53.538 us, total = 53.538 us
	JobInfoGcsService.grpc_server.AddJob - 1 total (0 active), Execution time: mean = 179.464 us, total = 179.464 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.AddJob.HandleRequestImpl - 1 total (0 active), Execution time: mean = 62.233 us, total = 62.233 us, Queueing time: mean = 5.687 us, max = 5.687 us, min = 5.687 us, total = 5.687 us
	NodeInfoGcsService.grpc_server.RegisterNode - 1 total (0 active), Execution time: mean = 816.646 us, total = 816.646 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Delete - 1 total (0 active), Execution time: mean = 9.483 us, total = 9.483 us, Queueing time: mean = 7.193 us, max = 7.193 us, min = 7.193 us, total = 7.193 us
	JobInfoGcsService.grpc_server.GetAllJobInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 15.633 us, total = 15.633 us, Queueing time: mean = 54.801 us, max = 54.801 us, min = 54.801 us, total = 54.801 us
	GcsHealthCheckManager::AddNode - 1 total (0 active), Execution time: mean = 12.710 us, total = 12.710 us, Queueing time: mean = 554.000 ns, max = 554.000 ns, min = 554.000 ns, total = 554.000 ns
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 1 total (0 active), Execution time: mean = 337.099 us, total = 337.099 us, Queueing time: mean = 53.254 us, max = 53.254 us, min = 53.254 us, total = 53.254 us
	ActorInfoGcsService.grpc_server.GetActorInfo - 1 total (0 active), Execution time: mean = 340.669 us, total = 340.669 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.GetActorInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 21.827 us, total = 21.827 us, Queueing time: mean = 15.907 us, max = 15.907 us, min = 15.907 us, total = 15.907 us
	JobInfoGcsService.grpc_server.GetAllJobInfo - 1 total (0 active), Execution time: mean = 455.736 us, total = 455.736 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	InternalKVGcsService.grpc_server.InternalKVDel.HandleRequestImpl - 1 total (0 active), Execution time: mean = 50.454 us, total = 50.454 us, Queueing time: mean = 46.858 us, max = 46.858 us, min = 46.858 us, total = 46.858 us
	NodeInfoGcsService.grpc_server.RegisterNode.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.004 us, total = 112.004 us, Queueing time: mean = 50.963 us, max = 50.963 us, min = 50.963 us, total = 50.963 us
	GcsInMemoryStore.GetNextJobID - 1 total (0 active), Execution time: mean = 8.613 us, total = 8.613 us, Queueing time: mean = 6.080 us, max = 6.080 us, min = 6.080 us, total = 6.080 us
	InternalKVGcsService.grpc_server.InternalKVDel - 1 total (0 active), Execution time: mean = 340.770 us, total = 340.770 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s


[2025-07-05 18:48:57,673 I 660691 660691] (gcs_server) gcs_server.cc:874: task_io_context Event stats:


Global stats: 19451 total (1 active)
Queueing time: mean = 19.316 us, max = 321.759 us, min = 3.053 us, total = 375.722 ms
Execution time:  mean = 126.077 us, total = 2.452 s
Event stats:
	TaskInfoGcsService.grpc_server.AddTaskEventData - 8968 total (0 active), Execution time: mean = 257.495 us, total = 2.309 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	TaskInfoGcsService.grpc_server.AddTaskEventData.HandleRequestImpl - 8968 total (0 active), Execution time: mean = 12.789 us, total = 114.694 ms, Queueing time: mean = 40.325 us, max = 321.759 us, min = 3.511 us, total = 361.634 ms
	event_loop_lag_probe - 1441 total (0 active), Execution time: mean = 19.363 us, total = 27.903 ms, Queueing time: mean = 6.602 us, max = 235.041 us, min = 3.053 us, total = 9.513 ms
	GcsTaskManager.GcJobSummary - 73 total (1 active), Execution time: mean = 7.059 us, total = 515.281 us, Queueing time: mean = 62.537 us, max = 111.068 us, min = 22.712 us, total = 4.565 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 4.380 us, total = 4.380 us, Queueing time: mean = 9.466 us, max = 9.466 us, min = 9.466 us, total = 9.466 us


[2025-07-05 18:48:57,674 I 660691 660691] (gcs_server) gcs_server.cc:874: pubsub_io_context Event stats:


Global stats: 2010 total (29 active)
Queueing time: mean = 11.469 us, max = 882.198 us, min = 2.577 us, total = 23.053 ms
Execution time:  mean = 177.520 ms, total = 356.815 s
Event stats:
	event_loop_lag_probe - 1441 total (0 active), Execution time: mean = 18.608 us, total = 26.815 ms, Queueing time: mean = 6.989 us, max = 882.198 us, min = 2.577 us, total = 10.071 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll.HandleRequestImpl - 140 total (0 active), Execution time: mean = 29.867 us, total = 4.181 ms, Queueing time: mean = 44.389 us, max = 160.291 us, min = 4.744 us, total = 6.215 ms
	InternalPubSubGcsService.grpc_server.GcsSubscriberPoll - 140 total (28 active), Execution time: mean = 2.548 s, total = 356.726 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsPublish.HandleRequestImpl - 113 total (0 active), Execution time: mean = 68.383 us, total = 7.727 ms, Queueing time: mean = 51.603 us, max = 71.697 us, min = 12.086 us, total = 5.831 ms
	InternalPubSubGcsService.grpc_server.GcsPublish - 113 total (0 active), Execution time: mean = 368.288 us, total = 41.617 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch - 30 total (0 active), Execution time: mean = 252.052 us, total = 7.562 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalPubSubGcsService.grpc_server.GcsSubscriberCommandBatch.HandleRequestImpl - 30 total (0 active), Execution time: mean = 28.413 us, total = 852.387 us, Queueing time: mean = 28.317 us, max = 142.940 us, min = 4.958 us, total = 849.505 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.325 us, total = 16.650 us, Queueing time: mean = 38.544 us, max = 77.089 us, min = 77.089 us, total = 77.089 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 3.882 us, total = 3.882 us, Queueing time: mean = 10.063 us, max = 10.063 us, min = 10.063 us, total = 10.063 us


[2025-07-05 18:48:57,674 I 660691 660691] (gcs_server) gcs_server.cc:874: ray_syncer_io_context Event stats:


Global stats: 1447 total (0 active)
Queueing time: mean = 6.676 us, max = 411.929 us, min = 274.000 ns, total = 9.660 ms
Execution time:  mean = 18.907 us, total = 27.358 ms
Event stats:
	event_loop_lag_probe - 1441 total (0 active), Execution time: mean = 18.874 us, total = 27.197 ms, Queueing time: mean = 6.615 us, max = 411.929 us, min = 3.168 us, total = 9.533 ms
	 - 2 total (0 active), Execution time: mean = 67.501 us, total = 135.001 us, Queueing time: mean = 53.227 us, max = 54.007 us, min = 52.447 us, total = 106.454 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 574.000 ns, total = 1.148 us, Queueing time: mean = 10.003 us, max = 11.342 us, min = 8.664 us, total = 20.006 us
	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 12.356 us, total = 24.712 us, Queueing time: mean = 294.000 ns, max = 314.000 ns, min = 274.000 ns, total = 588.000 ns


[2025-07-05 18:49:07,816 I 660691 660691] (gcs_server) gcs_actor_scheduler.cc:491: Finished actor creation task for actor c4dbba14b481690e7680c72d01000000 on worker d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0 at node 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d, job id = 01000000
[2025-07-05 18:49:07,816 I 660691 660691] (gcs_server) gcs_actor_manager.cc:1586: Actor created successfully job_id=01000000 actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:49:07,816 I 660691 660691] (gcs_server) gcs_actor_manager.cc:530: Finished creating actor. Status: OK job_id=01000000 actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:49:08,662 I 660691 660691] (gcs_server) gcs_actor_manager.cc:1072: Destroying actor job_id=01000000 actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:49:08,663 I 660691 660691] (gcs_server) gcs_actor_manager.cc:1072: Destroying actor job_id=01000000 actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:49:08,664 I 660691 660691] (gcs_server) gcs_actor_manager.cc:1047: Worker 01000000ffffffffffffffffffffffffffffffffffffffffffffffff failed, destroying actor child, job id = 01000000
[2025-07-05 18:49:08,664 I 660691 660691] (gcs_server) gcs_actor_manager.cc:1047: Worker 01000000ffffffffffffffffffffffffffffffffffffffffffffffff failed, destroying actor child, job id = 01000000
[2025-07-05 18:49:08,664 I 660691 660691] (gcs_server) gcs_job_manager.cc:156: Finished marking job state, job id = 01000000
[2025-07-05 18:49:08,783 I 660691 660691] (gcs_server) gcs_node_manager.cc:386: Removing node, node name = ***********, death reason = EXPECTED_TERMINATION, death message = received SIGTERM node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,783 I 660691 660691] (gcs_server) gcs_placement_group_mgr.cc:802: Node is dead, rescheduling the placement groups on the dead node. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,783 I 660691 660691] (gcs_server) gcs_actor_manager.cc:1325: Node is dead, reconstructing actors. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,783 I 660691 660691] (gcs_server) gcs_job_manager.cc:478: Node is dead, mark all jobs from this node as finished node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,904 I 660691 660740] (gcs_server) ray_syncer_bidi_reactor_base.h:182: Failed to read the message from: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,904 I 660691 660740] (gcs_server) ray_syncer.cc:241: Connection is broken. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,947 I 660691 660691] (gcs_server) gcs_server_main.cc:172: GCS server received SIGTERM, shutting down...
[2025-07-05 18:49:08,947 I 660691 660691] (gcs_server) gcs_server.cc:271: Stopping GCS server.
[2025-07-05 18:49:08,964 I 660691 660691] (gcs_server) gcs_server.cc:288: GCS server stopped.
[2025-07-05 18:49:08,965 I 660691 660691] (gcs_server) io_service_pool.cc:48: IOServicePool is stopped.
[2025-07-05 18:49:09,073 I 660691 660691] (gcs_server) stats.h:120: Stats module has shutdown.
