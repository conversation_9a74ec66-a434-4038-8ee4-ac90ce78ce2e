[2025-07-05 18:42:59,655 I 660944 660944] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660944
[2025-07-05 18:42:59,656 I 660944 660944] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,658 I 660944 660944] grpc_server.cc:141: worker server started, listening on port 36333.
[2025-07-05 18:42:59,660 I 660944 660944] core_worker.cc:542: Initializing worker at address: ***********:36333 worker_id=b369b3a5cb51eb9161b475358507e3558634f0b64eb9cc8cc5742a00 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,660 I 660944 660944] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,661 I 660944 660944] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,661 I 660944 660944] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,661 I 660944 660944] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,661 I 660944 660944] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,662 I 660944 662068] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 8.536 us, max = 60.948 us, min = 9.643 us, total = 128.043 us
Execution time:  mean = 85.852 us, total = 1.288 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 3.683 us, total = 25.781 us, Queueing time: mean = 14.432 us, max = 60.948 us, min = 40.076 us, total = 101.024 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 227.285 us, total = 227.285 us, Queueing time: mean = 17.376 us, max = 17.376 us, min = 17.376 us, total = 17.376 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 565.193 us, total = 565.193 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.527 us, total = 17.527 us, Queueing time: mean = 9.643 us, max = 9.643 us, min = 9.643 us, total = 9.643 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 451.994 us, total = 451.994 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 4.737 us, max = 12.148 us, min = 6.799 us, total = 18.947 us
Execution time:  mean = 174.867 us, total = 699.467 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 564.313 us, total = 564.313 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.803 us, total = 17.803 us, Queueing time: mean = 12.148 us, max = 12.148 us, min = 12.148 us, total = 12.148 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 117.351 us, total = 117.351 us, Queueing time: mean = 6.799 us, max = 6.799 us, min = 6.799 us, total = 6.799 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,662 I 660944 662068] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,662 I 660944 662068] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,667 W 660944 662063] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,662 I 660944 662068] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 50.403 us, max = 1.001 ms, min = 9.643 us, total = 44.103 ms
Execution time:  mean = 76.144 us, total = 66.626 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.069 us, total = 4.841 ms, Queueing time: mean = 51.664 us, max = 160.510 us, min = 11.567 us, total = 30.998 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.139 us, total = 618.468 us, Queueing time: mean = 51.116 us, max = 92.308 us, min = 17.816 us, total = 3.118 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 25.821 us, total = 1.549 ms, Queueing time: mean = 38.869 us, max = 80.341 us, min = 12.323 us, total = 2.332 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 167.006 us, total = 10.020 ms, Queueing time: mean = 51.398 us, max = 86.413 us, min = 18.047 us, total = 3.084 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 772.166 us, total = 46.330 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 33.330 us, total = 399.962 us, Queueing time: mean = 29.119 us, max = 68.161 us, min = 15.550 us, total = 349.427 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 140.417 us, total = 982.919 us, Queueing time: mean = 454.358 us, max = 1.001 ms, min = 40.076 us, total = 3.181 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.667 us, total = 28.003 us, Queueing time: mean = 52.700 us, max = 88.887 us, min = 34.835 us, total = 316.198 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 480.480 us, total = 480.480 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.097 us, total = 113.097 us, Queueing time: mean = 697.268 us, max = 697.268 us, min = 697.268 us, total = 697.268 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 227.285 us, total = 227.285 us, Queueing time: mean = 17.376 us, max = 17.376 us, min = 17.376 us, total = 17.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 451.994 us, total = 451.994 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.527 us, total = 17.527 us, Queueing time: mean = 9.643 us, max = 9.643 us, min = 9.643 us, total = 9.643 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 565.193 us, total = 565.193 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.691 us, max = 2.346 ms, min = 456.000 ns, total = 326.121 ms
Execution time:  mean = 14.512 us, total = 86.535 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 14.513 us, total = 86.527 ms, Queueing time: mean = 54.699 us, max = 2.346 ms, min = 456.000 ns, total = 326.118 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.051 us, total = 8.051 us, Queueing time: mean = 2.741 us, max = 2.741 us, min = 2.741 us, total = 2.741 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 37.378 us, max = 89.786 us, min = 6.799 us, total = 6.765 ms
Execution time:  mean = 352.394 us, total = 63.783 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 800.549 us, total = 48.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 39.747 us, total = 2.385 ms, Queueing time: mean = 49.752 us, max = 75.427 us, min = 12.148 us, total = 2.985 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 220.804 us, total = 13.248 ms, Queueing time: mean = 62.893 us, max = 89.786 us, min = 28.103 us, total = 3.774 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 117.351 us, total = 117.351 us, Queueing time: mean = 6.799 us, max = 6.799 us, min = 6.799 us, total = 6.799 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,663 I 660944 662068] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 47.907 us, max = 1.001 ms, min = -0.000 s, total = 83.023 ms
Execution time:  mean = 78.797 us, total = 136.555 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.304 us, total = 9.965 ms, Queueing time: mean = 50.349 us, max = 195.384 us, min = -0.000 s, total = 60.418 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.727 us, total = 1.287 ms, Queueing time: mean = 52.018 us, max = 108.193 us, min = 17.216 us, total = 6.242 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.613 us, total = 3.434 ms, Queueing time: mean = 42.120 us, max = 92.350 us, min = 12.323 us, total = 5.054 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 183.217 us, total = 21.986 ms, Queueing time: mean = 50.627 us, max = 87.183 us, min = 18.047 us, total = 6.075 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 794.986 us, total = 95.398 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 36.359 us, total = 872.628 us, Queueing time: mean = 32.867 us, max = 71.124 us, min = 12.715 us, total = 788.813 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.836 us, total = 58.027 us, Queueing time: mean = 41.672 us, max = 88.887 us, min = 17.548 us, total = 500.066 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 140.417 us, total = 982.919 us, Queueing time: mean = 454.358 us, max = 1.001 ms, min = 40.076 us, total = 3.181 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 357.620 us, total = 715.241 us, Queueing time: mean = 19.421 us, max = 38.841 us, min = 38.841 us, total = 38.841 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 480.480 us, total = 480.480 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.097 us, total = 113.097 us, Queueing time: mean = 697.268 us, max = 697.268 us, min = 697.268 us, total = 697.268 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 227.285 us, total = 227.285 us, Queueing time: mean = 17.376 us, max = 17.376 us, min = 17.376 us, total = 17.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 451.994 us, total = 451.994 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.527 us, total = 17.527 us, Queueing time: mean = 9.643 us, max = 9.643 us, min = 9.643 us, total = 9.643 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 565.193 us, total = 565.193 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11922 total (1 active)
Queueing time: mean = 56.936 us, max = 2.346 ms, min = 456.000 ns, total = 678.786 ms
Execution time:  mean = 14.619 us, total = 174.285 ms
Event stats:
	CoreWorker.CheckSignal - 11921 total (1 active), Execution time: mean = 14.619 us, total = 174.277 ms, Queueing time: mean = 56.940 us, max = 2.346 ms, min = 456.000 ns, total = 678.783 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.051 us, total = 8.051 us, Queueing time: mean = 2.741 us, max = 2.741 us, min = 2.741 us, total = 2.741 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 35.940 us, max = 131.460 us, min = 6.799 us, total = 12.974 ms
Execution time:  mean = 353.960 us, total = 127.780 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 806.750 us, total = 96.810 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 39.198 us, total = 4.704 ms, Queueing time: mean = 45.399 us, max = 81.447 us, min = 12.148 us, total = 5.448 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 217.905 us, total = 26.149 ms, Queueing time: mean = 62.663 us, max = 131.460 us, min = 21.626 us, total = 7.520 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 117.351 us, total = 117.351 us, Queueing time: mean = 6.799 us, max = 6.799 us, min = 6.799 us, total = 6.799 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,664 I 660944 662068] core_worker.cc:902: Event stats:


Global stats: 2592 total (8 active)
Queueing time: mean = 47.480 us, max = 1.001 ms, min = -0.000 s, total = 123.069 ms
Execution time:  mean = 79.308 us, total = 205.565 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.372 us, total = 15.070 ms, Queueing time: mean = 50.184 us, max = 195.384 us, min = -0.000 s, total = 90.331 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 11.035 us, total = 1.986 ms, Queueing time: mean = 50.698 us, max = 108.193 us, min = 15.568 us, total = 9.126 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.310 us, total = 5.276 ms, Queueing time: mean = 42.066 us, max = 92.350 us, min = 12.323 us, total = 7.572 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 184.519 us, total = 33.214 ms, Queueing time: mean = 51.379 us, max = 99.799 us, min = 14.874 us, total = 9.248 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 800.701 us, total = 144.126 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 36.057 us, total = 1.298 ms, Queueing time: mean = 35.973 us, max = 77.739 us, min = 12.715 us, total = 1.295 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.850 us, total = 87.301 us, Queueing time: mean = 81.770 us, max = 738.945 us, min = 17.548 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 140.417 us, total = 982.919 us, Queueing time: mean = 454.358 us, max = 1.001 ms, min = 40.076 us, total = 3.181 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 556.662 us, total = 1.670 ms, Queueing time: mean = 40.154 us, max = 81.620 us, min = 38.841 us, total = 120.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 480.480 us, total = 480.480 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.097 us, total = 113.097 us, Queueing time: mean = 697.268 us, max = 697.268 us, min = 697.268 us, total = 697.268 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 227.285 us, total = 227.285 us, Queueing time: mean = 17.376 us, max = 17.376 us, min = 17.376 us, total = 17.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 451.994 us, total = 451.994 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.527 us, total = 17.527 us, Queueing time: mean = 9.643 us, max = 9.643 us, min = 9.643 us, total = 9.643 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 565.193 us, total = 565.193 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17887 total (1 active)
Queueing time: mean = 54.368 us, max = 2.346 ms, min = -0.000 s, total = 972.489 ms
Execution time:  mean = 14.751 us, total = 263.855 ms
Event stats:
	CoreWorker.CheckSignal - 17886 total (1 active), Execution time: mean = 14.752 us, total = 263.847 ms, Queueing time: mean = 54.371 us, max = 2.346 ms, min = -0.000 s, total = 972.486 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.051 us, total = 8.051 us, Queueing time: mean = 2.741 us, max = 2.741 us, min = 2.741 us, total = 2.741 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 35.659 us, max = 131.460 us, min = 6.799 us, total = 19.291 ms
Execution time:  mean = 352.974 us, total = 190.959 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 805.676 us, total = 145.022 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 39.850 us, total = 7.173 ms, Queueing time: mean = 45.508 us, max = 81.447 us, min = 12.148 us, total = 8.191 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 214.704 us, total = 38.647 ms, Queueing time: mean = 61.628 us, max = 131.460 us, min = 21.626 us, total = 11.093 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 117.351 us, total = 117.351 us, Queueing time: mean = 6.799 us, max = 6.799 us, min = 6.799 us, total = 6.799 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,664 I 660944 662068] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 48.005 us, max = 1.001 ms, min = -0.000 s, total = 165.617 ms
Execution time:  mean = 78.559 us, total = 271.027 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.372 us, total = 20.085 ms, Queueing time: mean = 51.060 us, max = 195.384 us, min = -0.000 s, total = 122.493 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.942 us, total = 2.626 ms, Queueing time: mean = 51.409 us, max = 108.193 us, min = 15.568 us, total = 12.338 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.271 us, total = 7.025 ms, Queueing time: mean = 43.105 us, max = 94.724 us, min = 12.323 us, total = 10.345 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 182.716 us, total = 43.852 ms, Queueing time: mean = 52.527 us, max = 115.752 us, min = 10.979 us, total = 12.607 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 794.716 us, total = 190.732 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.260 us, total = 1.692 ms, Queueing time: mean = 39.067 us, max = 77.739 us, min = 5.899 us, total = 1.875 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 4.954 us, total = 118.894 us, Queueing time: mean = 79.624 us, max = 738.945 us, min = 17.548 us, total = 1.911 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 140.417 us, total = 982.919 us, Queueing time: mean = 454.358 us, max = 1.001 ms, min = 40.076 us, total = 3.181 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 514.261 us, total = 2.057 ms, Queueing time: mean = 35.804 us, max = 81.620 us, min = 22.757 us, total = 143.218 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 480.480 us, total = 480.480 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.097 us, total = 113.097 us, Queueing time: mean = 697.268 us, max = 697.268 us, min = 697.268 us, total = 697.268 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 227.285 us, total = 227.285 us, Queueing time: mean = 17.376 us, max = 17.376 us, min = 17.376 us, total = 17.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 451.994 us, total = 451.994 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.527 us, total = 17.527 us, Queueing time: mean = 9.643 us, max = 9.643 us, min = 9.643 us, total = 9.643 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 565.193 us, total = 565.193 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23851 total (1 active)
Queueing time: mean = 53.589 us, max = 2.346 ms, min = -0.000 s, total = 1.278 s
Execution time:  mean = 14.784 us, total = 352.616 ms
Event stats:
	CoreWorker.CheckSignal - 23850 total (1 active), Execution time: mean = 14.784 us, total = 352.608 ms, Queueing time: mean = 53.592 us, max = 2.346 ms, min = -0.000 s, total = 1.278 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.051 us, total = 8.051 us, Queueing time: mean = 2.741 us, max = 2.741 us, min = 2.741 us, total = 2.741 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 34.308 us, max = 131.460 us, min = 6.799 us, total = 24.736 ms
Execution time:  mean = 350.343 us, total = 252.597 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 799.367 us, total = 191.848 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 39.725 us, total = 9.534 ms, Queueing time: mean = 44.380 us, max = 117.978 us, min = 12.148 us, total = 10.651 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 212.908 us, total = 51.098 ms, Queueing time: mean = 58.659 us, max = 131.460 us, min = 21.626 us, total = 14.078 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 117.351 us, total = 117.351 us, Queueing time: mean = 6.799 us, max = 6.799 us, min = 6.799 us, total = 6.799 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,665 I 660944 662068] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 48.445 us, max = 1.001 ms, min = -0.000 s, total = 208.799 ms
Execution time:  mean = 79.288 us, total = 341.730 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.368 us, total = 25.095 ms, Queueing time: mean = 51.522 us, max = 195.384 us, min = -0.000 s, total = 154.516 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.170 us, total = 3.351 ms, Queueing time: mean = 52.166 us, max = 108.193 us, min = 15.568 us, total = 15.650 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.240 us, total = 9.072 ms, Queueing time: mean = 44.329 us, max = 94.724 us, min = 12.323 us, total = 13.299 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 185.448 us, total = 55.634 ms, Queueing time: mean = 53.188 us, max = 115.752 us, min = 10.979 us, total = 15.956 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 802.381 us, total = 240.714 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.794 us, total = 2.148 ms, Queueing time: mean = 42.004 us, max = 77.739 us, min = 5.899 us, total = 2.520 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 4.921 us, total = 147.638 us, Queueing time: mean = 89.157 us, max = 738.945 us, min = 17.548 us, total = 2.675 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 140.417 us, total = 982.919 us, Queueing time: mean = 454.358 us, max = 1.001 ms, min = 40.076 us, total = 3.181 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 544.798 us, total = 2.724 ms, Queueing time: mean = 42.046 us, max = 81.620 us, min = 22.757 us, total = 210.232 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.747 us, total = 5.493 us, Queueing time: mean = 34.052 us, max = 68.104 us, min = 68.104 us, total = 68.104 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 480.480 us, total = 480.480 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.097 us, total = 113.097 us, Queueing time: mean = 697.268 us, max = 697.268 us, min = 697.268 us, total = 697.268 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 227.285 us, total = 227.285 us, Queueing time: mean = 17.376 us, max = 17.376 us, min = 17.376 us, total = 17.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 451.994 us, total = 451.994 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.527 us, total = 17.527 us, Queueing time: mean = 9.643 us, max = 9.643 us, min = 9.643 us, total = 9.643 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 565.193 us, total = 565.193 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29814 total (1 active)
Queueing time: mean = 52.961 us, max = 2.346 ms, min = -0.000 s, total = 1.579 s
Execution time:  mean = 14.863 us, total = 443.124 ms
Event stats:
	CoreWorker.CheckSignal - 29813 total (1 active), Execution time: mean = 14.863 us, total = 443.116 ms, Queueing time: mean = 52.961 us, max = 2.346 ms, min = -0.000 s, total = 1.579 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.051 us, total = 8.051 us, Queueing time: mean = 2.741 us, max = 2.741 us, min = 2.741 us, total = 2.741 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 33.779 us, max = 131.460 us, min = 6.799 us, total = 30.434 ms
Execution time:  mean = 350.952 us, total = 316.208 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 800.338 us, total = 240.102 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 40.064 us, total = 12.019 ms, Queueing time: mean = 43.352 us, max = 117.978 us, min = 12.148 us, total = 13.006 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 213.233 us, total = 63.970 ms, Queueing time: mean = 58.074 us, max = 131.460 us, min = 21.626 us, total = 17.422 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 117.351 us, total = 117.351 us, Queueing time: mean = 6.799 us, max = 6.799 us, min = 6.799 us, total = 6.799 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,666 I 660944 662068] core_worker.cc:902: Event stats:


Global stats: 5169 total (8 active)
Queueing time: mean = 49.041 us, max = 1.001 ms, min = -0.000 s, total = 253.491 ms
Execution time:  mean = 79.636 us, total = 411.637 ms
Event stats:
	CoreWorker.RecoverObjects - 3599 total (1 active), Execution time: mean = 8.422 us, total = 30.311 ms, Queueing time: mean = 52.310 us, max = 195.384 us, min = -0.000 s, total = 188.263 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.379 us, total = 4.097 ms, Queueing time: mean = 52.642 us, max = 108.193 us, min = 15.568 us, total = 18.951 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.721 us, total = 11.060 ms, Queueing time: mean = 44.595 us, max = 94.724 us, min = 12.323 us, total = 16.054 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 187.445 us, total = 67.480 ms, Queueing time: mean = 54.022 us, max = 115.752 us, min = 10.979 us, total = 19.448 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 804.779 us, total = 289.720 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.673 us, total = 2.568 ms, Queueing time: mean = 44.253 us, max = 77.739 us, min = 5.899 us, total = 3.186 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 4.955 us, total = 178.398 us, Queueing time: mean = 92.659 us, max = 738.945 us, min = 17.548 us, total = 3.336 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 140.417 us, total = 982.919 us, Queueing time: mean = 454.358 us, max = 1.001 ms, min = 40.076 us, total = 3.181 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 563.130 us, total = 3.379 ms, Queueing time: mean = 46.670 us, max = 81.620 us, min = 22.757 us, total = 280.021 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.747 us, total = 5.493 us, Queueing time: mean = 34.052 us, max = 68.104 us, min = 68.104 us, total = 68.104 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 480.480 us, total = 480.480 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.097 us, total = 113.097 us, Queueing time: mean = 697.268 us, max = 697.268 us, min = 697.268 us, total = 697.268 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 227.285 us, total = 227.285 us, Queueing time: mean = 17.376 us, max = 17.376 us, min = 17.376 us, total = 17.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 451.994 us, total = 451.994 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.527 us, total = 17.527 us, Queueing time: mean = 9.643 us, max = 9.643 us, min = 9.643 us, total = 9.643 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 565.193 us, total = 565.193 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35775 total (1 active)
Queueing time: mean = 53.456 us, max = 2.473 ms, min = -0.000 s, total = 1.912 s
Execution time:  mean = 14.868 us, total = 531.918 ms
Event stats:
	CoreWorker.CheckSignal - 35774 total (1 active), Execution time: mean = 14.869 us, total = 531.910 ms, Queueing time: mean = 53.457 us, max = 2.473 ms, min = -0.000 s, total = 1.912 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.051 us, total = 8.051 us, Queueing time: mean = 2.741 us, max = 2.741 us, min = 2.741 us, total = 2.741 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 34.202 us, max = 131.460 us, min = 6.799 us, total = 36.972 ms
Execution time:  mean = 349.883 us, total = 378.224 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 798.543 us, total = 287.475 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 39.605 us, total = 14.258 ms, Queueing time: mean = 44.096 us, max = 117.978 us, min = 12.148 us, total = 15.875 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 212.149 us, total = 76.374 ms, Queueing time: mean = 58.585 us, max = 131.460 us, min = 21.626 us, total = 21.091 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 117.351 us, total = 117.351 us, Queueing time: mean = 6.799 us, max = 6.799 us, min = 6.799 us, total = 6.799 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660944 662068] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660944 662068] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660944 662068] core_worker.cc:5107: Number of alive nodes:0
