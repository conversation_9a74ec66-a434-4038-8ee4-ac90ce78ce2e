[2025-07-05 18:42:59,485 I 660941 660941] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660941
[2025-07-05 18:42:59,487 I 660941 660941] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,489 I 660941 660941] grpc_server.cc:141: worker server started, listening on port 43365.
[2025-07-05 18:42:59,491 I 660941 660941] core_worker.cc:542: Initializing worker at address: ***********:43365 worker_id=f17830cb49805f98d67e80734a2c57232e7970d84d1d7ab25a3d251f node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,493 I 660941 660941] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,494 I 660941 661624] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,494 I 660941 661624] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,495 I 660941 660941] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,495 I 660941 661624] core_worker.cc:902: Event stats:


Global stats: 12 total (4 active)
Queueing time: mean = 8.044 us, max = 61.085 us, min = 7.010 us, total = 96.534 us
Execution time:  mean = 147.987 us, total = 1.776 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 7.132 us, total = 21.395 us, Queueing time: mean = 23.378 us, max = 61.085 us, min = 9.048 us, total = 70.133 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.004 us, total = 17.004 us, Queueing time: mean = 11.498 us, max = 11.498 us, min = 11.498 us, total = 11.498 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 119.941 us, total = 119.941 us, Queueing time: mean = 7.010 us, max = 7.010 us, min = 7.010 us, total = 7.010 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 347.408 us, total = 347.408 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.541 us, total = 58.541 us, Queueing time: mean = 7.893 us, max = 7.893 us, min = 7.893 us, total = 7.893 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 837.862 us, total = 837.862 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 373.691 us, total = 373.691 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.603 us, max = 8.433 us, min = 5.979 us, total = 14.412 us
Execution time:  mean = 125.434 us, total = 501.738 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 340.565 us, total = 340.565 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.957 us, total = 144.957 us, Queueing time: mean = 5.979 us, max = 5.979 us, min = 5.979 us, total = 5.979 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.216 us, total = 16.216 us, Queueing time: mean = 8.433 us, max = 8.433 us, min = 8.433 us, total = 8.433 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,496 I 660941 660941] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,496 I 660941 660941] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,496 I 660941 660941] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,499 W 660941 661613] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,496 I 660941 661624] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 43.917 us, max = 488.270 us, min = 7.010 us, total = 38.427 ms
Execution time:  mean = 67.623 us, total = 59.170 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.366 us, total = 5.020 ms, Queueing time: mean = 49.065 us, max = 105.851 us, min = 14.822 us, total = 29.439 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 9.884 us, total = 602.901 us, Queueing time: mean = 39.019 us, max = 100.119 us, min = 13.503 us, total = 2.380 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 161.196 us, total = 9.672 ms, Queueing time: mean = 38.011 us, max = 76.424 us, min = 14.726 us, total = 2.281 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 26.029 us, total = 1.562 ms, Queueing time: mean = 37.309 us, max = 66.507 us, min = 10.760 us, total = 2.239 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 661.059 us, total = 39.664 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 29.427 us, total = 353.122 us, Queueing time: mean = 30.821 us, max = 102.240 us, min = 15.311 us, total = 369.856 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.670 us, total = 515.688 us, Queueing time: mean = 213.799 us, max = 488.270 us, min = 9.048 us, total = 1.497 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.522 us, total = 27.134 us, Queueing time: mean = 32.641 us, max = 65.389 us, min = 28.166 us, total = 195.847 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 347.408 us, total = 347.408 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 119.941 us, total = 119.941 us, Queueing time: mean = 7.010 us, max = 7.010 us, min = 7.010 us, total = 7.010 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 373.691 us, total = 373.691 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 837.862 us, total = 837.862 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.004 us, total = 17.004 us, Queueing time: mean = 11.498 us, max = 11.498 us, min = 11.498 us, total = 11.498 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.541 us, total = 58.541 us, Queueing time: mean = 7.893 us, max = 7.893 us, min = 7.893 us, total = 7.893 us

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.017 us, max = 1.988 ms, min = -0.000 s, total = 322.103 ms
Execution time:  mean = 14.649 us, total = 87.351 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 14.650 us, total = 87.341 ms, Queueing time: mean = 54.025 us, max = 1.988 ms, min = -0.000 s, total = 322.100 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.543 us, total = 9.543 us, Queueing time: mean = 3.220 us, max = 3.220 us, min = 3.220 us, total = 3.220 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 25.047 us, max = 110.715 us, min = 5.979 us, total = 4.534 ms
Execution time:  mean = 321.835 us, total = 58.252 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 728.251 us, total = 43.695 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 203.428 us, total = 12.206 ms, Queueing time: mean = 41.958 us, max = 110.715 us, min = 15.043 us, total = 2.517 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 36.773 us, total = 2.206 ms, Queueing time: mean = 33.501 us, max = 61.037 us, min = 8.433 us, total = 2.010 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.957 us, total = 144.957 us, Queueing time: mean = 5.979 us, max = 5.979 us, min = 5.979 us, total = 5.979 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,497 I 660941 661624] core_worker.cc:902: Event stats:


Global stats: 1734 total (8 active)
Queueing time: mean = 45.578 us, max = 488.270 us, min = -0.000 s, total = 79.033 ms
Execution time:  mean = 71.423 us, total = 123.847 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.441 us, total = 10.130 ms, Queueing time: mean = 50.323 us, max = 127.597 us, min = 14.822 us, total = 60.387 ms
	CoreWorker.ExitIfParentRayletDies - 121 total (1 active), Execution time: mean = 10.342 us, total = 1.251 ms, Queueing time: mean = 45.746 us, max = 111.921 us, min = 13.503 us, total = 5.535 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 172.190 us, total = 20.663 ms, Queueing time: mean = 42.001 us, max = 78.140 us, min = 14.726 us, total = 5.040 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.429 us, total = 3.411 ms, Queueing time: mean = 41.905 us, max = 98.824 us, min = 10.760 us, total = 5.029 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 703.941 us, total = 84.473 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 34.718 us, total = 833.236 us, Queueing time: mean = 33.525 us, max = 102.240 us, min = -0.000 s, total = 804.607 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.409 us, total = 64.906 us, Queueing time: mean = 56.700 us, max = 304.646 us, min = 19.010 us, total = 680.403 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.670 us, total = 515.688 us, Queueing time: mean = 213.799 us, max = 488.270 us, min = 9.048 us, total = 1.497 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 375.146 us, total = 750.292 us, Queueing time: mean = 16.806 us, max = 33.612 us, min = 33.612 us, total = 33.612 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 347.408 us, total = 347.408 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 119.941 us, total = 119.941 us, Queueing time: mean = 7.010 us, max = 7.010 us, min = 7.010 us, total = 7.010 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 373.691 us, total = 373.691 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 837.862 us, total = 837.862 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.004 us, total = 17.004 us, Queueing time: mean = 11.498 us, max = 11.498 us, min = 11.498 us, total = 11.498 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.541 us, total = 58.541 us, Queueing time: mean = 7.893 us, max = 7.893 us, min = 7.893 us, total = 7.893 us

-----------------
Task execution event stats:

Global stats: 11925 total (1 active)
Queueing time: mean = 54.278 us, max = 1.988 ms, min = -0.000 s, total = 647.269 ms
Execution time:  mean = 14.938 us, total = 178.132 ms
Event stats:
	CoreWorker.CheckSignal - 11924 total (1 active), Execution time: mean = 14.938 us, total = 178.122 ms, Queueing time: mean = 54.283 us, max = 1.988 ms, min = -0.000 s, total = 647.266 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.543 us, total = 9.543 us, Queueing time: mean = 3.220 us, max = 3.220 us, min = 3.220 us, total = 3.220 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 27.642 us, max = 110.715 us, min = 5.979 us, total = 9.979 ms
Execution time:  mean = 322.918 us, total = 116.573 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 729.962 us, total = 87.595 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 204.600 us, total = 24.552 ms, Queueing time: mean = 47.728 us, max = 110.715 us, min = 15.043 us, total = 5.727 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 35.675 us, total = 4.281 ms, Queueing time: mean = 35.377 us, max = 64.291 us, min = 8.433 us, total = 4.245 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.957 us, total = 144.957 us, Queueing time: mean = 5.979 us, max = 5.979 us, min = 5.979 us, total = 5.979 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,498 I 660941 661624] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 48.127 us, max = 677.028 us, min = -0.000 s, total = 124.698 ms
Execution time:  mean = 71.764 us, total = 185.940 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.387 us, total = 15.088 ms, Queueing time: mean = 53.211 us, max = 127.597 us, min = 14.822 us, total = 95.727 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 172.232 us, total = 31.002 ms, Queueing time: mean = 44.508 us, max = 95.015 us, min = 14.726 us, total = 8.011 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 28.766 us, total = 5.178 ms, Queueing time: mean = 41.923 us, max = 98.824 us, min = 10.760 us, total = 7.546 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.471 us, total = 1.885 ms, Queueing time: mean = 48.917 us, max = 111.921 us, min = 13.503 us, total = 8.805 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 708.886 us, total = 127.599 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 34.972 us, total = 1.259 ms, Queueing time: mean = 36.352 us, max = 102.240 us, min = -0.000 s, total = 1.309 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.595 us, total = 100.712 us, Queueing time: mean = 92.275 us, max = 677.028 us, min = 17.408 us, total = 1.661 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.670 us, total = 515.688 us, Queueing time: mean = 213.799 us, max = 488.270 us, min = 9.048 us, total = 1.497 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 519.217 us, total = 1.558 ms, Queueing time: mean = 38.731 us, max = 82.582 us, min = 33.612 us, total = 116.194 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 347.408 us, total = 347.408 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 119.941 us, total = 119.941 us, Queueing time: mean = 7.010 us, max = 7.010 us, min = 7.010 us, total = 7.010 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 373.691 us, total = 373.691 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 837.862 us, total = 837.862 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.004 us, total = 17.004 us, Queueing time: mean = 11.498 us, max = 11.498 us, min = 11.498 us, total = 11.498 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.541 us, total = 58.541 us, Queueing time: mean = 7.893 us, max = 7.893 us, min = 7.893 us, total = 7.893 us

-----------------
Task execution event stats:

Global stats: 17885 total (1 active)
Queueing time: mean = 54.935 us, max = 1.988 ms, min = -0.000 s, total = 982.516 ms
Execution time:  mean = 14.980 us, total = 267.911 ms
Event stats:
	CoreWorker.CheckSignal - 17884 total (1 active), Execution time: mean = 14.980 us, total = 267.902 ms, Queueing time: mean = 54.938 us, max = 1.988 ms, min = -0.000 s, total = 982.513 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.543 us, total = 9.543 us, Queueing time: mean = 3.220 us, max = 3.220 us, min = 3.220 us, total = 3.220 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 28.908 us, max = 125.624 us, min = 5.979 us, total = 15.639 ms
Execution time:  mean = 326.379 us, total = 176.571 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 741.547 us, total = 133.478 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 203.250 us, total = 36.585 ms, Queueing time: mean = 50.607 us, max = 110.715 us, min = 15.043 us, total = 9.109 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 35.349 us, total = 6.363 ms, Queueing time: mean = 36.243 us, max = 125.624 us, min = 8.433 us, total = 6.524 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.957 us, total = 144.957 us, Queueing time: mean = 5.979 us, max = 5.979 us, min = 5.979 us, total = 5.979 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,498 I 660941 661624] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 49.565 us, max = 677.028 us, min = -0.000 s, total = 170.999 ms
Execution time:  mean = 71.845 us, total = 247.867 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.348 us, total = 20.026 ms, Queueing time: mean = 54.669 us, max = 127.597 us, min = 14.822 us, total = 131.151 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 173.685 us, total = 41.685 ms, Queueing time: mean = 46.184 us, max = 95.015 us, min = 14.726 us, total = 11.084 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.127 us, total = 6.990 ms, Queueing time: mean = 44.365 us, max = 131.081 us, min = 10.760 us, total = 10.648 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.513 us, total = 2.523 ms, Queueing time: mean = 50.723 us, max = 111.921 us, min = 10.663 us, total = 12.174 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 709.555 us, total = 170.293 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.204 us, total = 1.690 ms, Queueing time: mean = 37.931 us, max = 102.240 us, min = -0.000 s, total = 1.821 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.603 us, total = 134.464 us, Queueing time: mean = 100.435 us, max = 677.028 us, min = 17.408 us, total = 2.410 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.670 us, total = 515.688 us, Queueing time: mean = 213.799 us, max = 488.270 us, min = 9.048 us, total = 1.497 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 563.851 us, total = 2.255 ms, Queueing time: mean = 47.048 us, max = 82.582 us, min = 33.612 us, total = 188.192 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 347.408 us, total = 347.408 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 119.941 us, total = 119.941 us, Queueing time: mean = 7.010 us, max = 7.010 us, min = 7.010 us, total = 7.010 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 373.691 us, total = 373.691 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 837.862 us, total = 837.862 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.004 us, total = 17.004 us, Queueing time: mean = 11.498 us, max = 11.498 us, min = 11.498 us, total = 11.498 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.541 us, total = 58.541 us, Queueing time: mean = 7.893 us, max = 7.893 us, min = 7.893 us, total = 7.893 us

-----------------
Task execution event stats:

Global stats: 23844 total (1 active)
Queueing time: mean = 56.019 us, max = 1.988 ms, min = -0.000 s, total = 1.336 s
Execution time:  mean = 14.966 us, total = 356.838 ms
Event stats:
	CoreWorker.CheckSignal - 23843 total (1 active), Execution time: mean = 14.966 us, total = 356.829 ms, Queueing time: mean = 56.022 us, max = 1.988 ms, min = -0.000 s, total = 1.336 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.543 us, total = 9.543 us, Queueing time: mean = 3.220 us, max = 3.220 us, min = 3.220 us, total = 3.220 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 30.157 us, max = 125.624 us, min = 5.979 us, total = 21.743 ms
Execution time:  mean = 330.168 us, total = 238.051 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 751.630 us, total = 180.391 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 203.818 us, total = 48.916 ms, Queueing time: mean = 52.060 us, max = 110.715 us, min = 15.043 us, total = 12.494 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 35.826 us, total = 8.598 ms, Queueing time: mean = 38.511 us, max = 125.624 us, min = 8.433 us, total = 9.243 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.957 us, total = 144.957 us, Queueing time: mean = 5.979 us, max = 5.979 us, min = 5.979 us, total = 5.979 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,499 I 660941 661624] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 50.091 us, max = 677.028 us, min = -0.000 s, total = 215.893 ms
Execution time:  mean = 71.406 us, total = 307.759 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.339 us, total = 25.008 ms, Queueing time: mean = 55.449 us, max = 211.750 us, min = 14.822 us, total = 166.293 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 173.685 us, total = 52.105 ms, Queueing time: mean = 45.671 us, max = 95.015 us, min = 14.726 us, total = 13.701 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 28.881 us, total = 8.664 ms, Queueing time: mean = 43.030 us, max = 131.081 us, min = 10.760 us, total = 12.909 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.464 us, total = 3.139 ms, Queueing time: mean = 52.159 us, max = 111.921 us, min = 10.663 us, total = 15.648 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 704.309 us, total = 211.293 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.267 us, total = 2.116 ms, Queueing time: mean = 40.621 us, max = 102.240 us, min = -0.000 s, total = 2.437 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.530 us, total = 165.901 us, Queueing time: mean = 102.893 us, max = 677.028 us, min = 17.408 us, total = 3.087 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.670 us, total = 515.688 us, Queueing time: mean = 213.799 us, max = 488.270 us, min = 9.048 us, total = 1.497 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 596.396 us, total = 2.982 ms, Queueing time: mean = 50.991 us, max = 82.582 us, min = 33.612 us, total = 254.954 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 7.324 us, total = 14.647 us, Queueing time: mean = 20.270 us, max = 40.540 us, min = 40.540 us, total = 40.540 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 347.408 us, total = 347.408 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 119.941 us, total = 119.941 us, Queueing time: mean = 7.010 us, max = 7.010 us, min = 7.010 us, total = 7.010 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 373.691 us, total = 373.691 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 837.862 us, total = 837.862 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.004 us, total = 17.004 us, Queueing time: mean = 11.498 us, max = 11.498 us, min = 11.498 us, total = 11.498 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.541 us, total = 58.541 us, Queueing time: mean = 7.893 us, max = 7.893 us, min = 7.893 us, total = 7.893 us

-----------------
Task execution event stats:

Global stats: 29804 total (1 active)
Queueing time: mean = 56.249 us, max = 1.988 ms, min = -0.000 s, total = 1.676 s
Execution time:  mean = 14.959 us, total = 445.826 ms
Event stats:
	CoreWorker.CheckSignal - 29803 total (1 active), Execution time: mean = 14.959 us, total = 445.816 ms, Queueing time: mean = 56.251 us, max = 1.988 ms, min = -0.000 s, total = 1.676 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.543 us, total = 9.543 us, Queueing time: mean = 3.220 us, max = 3.220 us, min = 3.220 us, total = 3.220 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 30.808 us, max = 125.624 us, min = 5.979 us, total = 27.758 ms
Execution time:  mean = 329.994 us, total = 297.325 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 752.545 us, total = 225.764 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 202.430 us, total = 60.729 ms, Queueing time: mean = 53.492 us, max = 110.715 us, min = 15.043 us, total = 16.048 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 35.625 us, total = 10.687 ms, Queueing time: mean = 39.015 us, max = 125.624 us, min = 8.433 us, total = 11.705 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.957 us, total = 144.957 us, Queueing time: mean = 5.979 us, max = 5.979 us, min = 5.979 us, total = 5.979 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,500 I 660941 661624] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.670 us, max = 677.028 us, min = -0.000 s, total = 261.864 ms
Execution time:  mean = 71.218 us, total = 368.056 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.334 us, total = 29.987 ms, Queueing time: mean = 56.199 us, max = 211.750 us, min = 14.188 us, total = 202.205 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 173.416 us, total = 62.430 ms, Queueing time: mean = 46.449 us, max = 95.015 us, min = 14.726 us, total = 16.722 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 28.791 us, total = 10.365 ms, Queueing time: mean = 41.098 us, max = 131.081 us, min = 10.760 us, total = 14.795 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.886 us, total = 3.919 ms, Queueing time: mean = 53.422 us, max = 111.921 us, min = 10.663 us, total = 19.232 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 701.472 us, total = 252.530 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 36.707 us, total = 2.643 ms, Queueing time: mean = 43.856 us, max = 102.240 us, min = -0.000 s, total = 3.158 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.512 us, total = 198.423 us, Queueing time: mean = 107.749 us, max = 677.028 us, min = 17.408 us, total = 3.879 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.670 us, total = 515.688 us, Queueing time: mean = 213.799 us, max = 488.270 us, min = 9.048 us, total = 1.497 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 616.529 us, total = 3.699 ms, Queueing time: mean = 51.655 us, max = 82.582 us, min = 33.612 us, total = 309.932 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 7.324 us, total = 14.647 us, Queueing time: mean = 20.270 us, max = 40.540 us, min = 40.540 us, total = 40.540 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 347.408 us, total = 347.408 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 119.941 us, total = 119.941 us, Queueing time: mean = 7.010 us, max = 7.010 us, min = 7.010 us, total = 7.010 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 373.691 us, total = 373.691 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 837.862 us, total = 837.862 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.004 us, total = 17.004 us, Queueing time: mean = 11.498 us, max = 11.498 us, min = 11.498 us, total = 11.498 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.541 us, total = 58.541 us, Queueing time: mean = 7.893 us, max = 7.893 us, min = 7.893 us, total = 7.893 us

-----------------
Task execution event stats:

Global stats: 35764 total (1 active)
Queueing time: mean = 56.434 us, max = 9.746 ms, min = -0.000 s, total = 2.018 s
Execution time:  mean = 14.995 us, total = 536.269 ms
Event stats:
	CoreWorker.CheckSignal - 35763 total (1 active), Execution time: mean = 14.995 us, total = 536.260 ms, Queueing time: mean = 56.436 us, max = 9.746 ms, min = -0.000 s, total = 2.018 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 9.543 us, total = 9.543 us, Queueing time: mean = 3.220 us, max = 3.220 us, min = 3.220 us, total = 3.220 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 30.717 us, max = 125.624 us, min = 5.979 us, total = 33.206 ms
Execution time:  mean = 330.099 us, total = 356.837 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 751.211 us, total = 270.436 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 203.615 us, total = 73.301 ms, Queueing time: mean = 53.097 us, max = 110.715 us, min = 15.043 us, total = 19.115 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 35.985 us, total = 12.955 ms, Queueing time: mean = 39.124 us, max = 125.624 us, min = 8.433 us, total = 14.085 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.957 us, total = 144.957 us, Queueing time: mean = 5.979 us, max = 5.979 us, min = 5.979 us, total = 5.979 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660941 661624] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660941 661624] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660941 661624] core_worker.cc:5107: Number of alive nodes:0
