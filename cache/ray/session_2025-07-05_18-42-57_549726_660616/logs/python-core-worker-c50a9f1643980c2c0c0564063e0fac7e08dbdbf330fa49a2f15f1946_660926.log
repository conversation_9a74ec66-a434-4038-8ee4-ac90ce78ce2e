[2025-07-05 18:42:59,522 I 660926 660926] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660926
[2025-07-05 18:42:59,523 I 660926 660926] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,525 I 660926 660926] grpc_server.cc:141: worker server started, listening on port 39385.
[2025-07-05 18:42:59,526 I 660926 660926] core_worker.cc:542: Initializing worker at address: ***********:39385 worker_id=c50a9f1643980c2c0c0564063e0fac7e08dbdbf330fa49a2f15f1946 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,527 I 660926 660926] task_event_buffer.cc:287: Reporting task events to <PERSON><PERSON> every 1000ms.
[2025-07-05 18:42:59,528 I 660926 661914] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,528 I 660926 661914] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,528 I 660926 660926] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,528 I 660926 661914] core_worker.cc:902: Event stats:


Global stats: 12 total (4 active)
Queueing time: mean = 10.296 us, max = 68.378 us, min = 6.716 us, total = 123.554 us
Execution time:  mean = 102.632 us, total = 1.232 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 11.475 us, total = 34.426 us, Queueing time: mean = 25.031 us, max = 68.378 us, min = 6.716 us, total = 75.094 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 329.048 us, total = 329.048 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 310.891 us, total = 310.891 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.656 us, total = 20.656 us, Queueing time: mean = 15.200 us, max = 15.200 us, min = 15.200 us, total = 15.200 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.301 us, total = 60.301 us, Queueing time: mean = 9.327 us, max = 9.327 us, min = 9.327 us, total = 9.327 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 369.391 us, total = 369.391 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.872 us, total = 106.872 us, Queueing time: mean = 23.933 us, max = 23.933 us, min = 23.933 us, total = 23.933 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.705 us, max = 8.027 us, min = 6.794 us, total = 14.821 us
Execution time:  mean = 219.400 us, total = 877.599 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 116.777 us, total = 116.777 us, Queueing time: mean = 6.794 us, max = 6.794 us, min = 6.794 us, total = 6.794 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.394 us, total = 19.394 us, Queueing time: mean = 8.027 us, max = 8.027 us, min = 8.027 us, total = 8.027 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 741.428 us, total = 741.428 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,529 I 660926 660926] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,529 I 660926 660926] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,529 I 660926 660926] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,534 W 660926 661894] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,529 I 660926 661914] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 50.475 us, max = 456.602 us, min = 6.716 us, total = 44.166 ms
Execution time:  mean = 67.307 us, total = 58.894 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.034 us, total = 4.821 ms, Queueing time: mean = 56.471 us, max = 94.906 us, min = 14.423 us, total = 33.882 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.124 us, total = 617.544 us, Queueing time: mean = 53.238 us, max = 75.230 us, min = 21.117 us, total = 3.248 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 164.780 us, total = 9.887 ms, Queueing time: mean = 39.122 us, max = 81.779 us, min = 17.532 us, total = 2.347 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.653 us, total = 1.659 ms, Queueing time: mean = 42.312 us, max = 82.204 us, min = 10.330 us, total = 2.539 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 664.044 us, total = 39.843 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 28.644 us, total = 343.725 us, Queueing time: mean = 34.105 us, max = 71.844 us, min = 15.183 us, total = 409.263 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.050 us, total = 497.352 us, Queueing time: mean = 199.176 us, max = 456.602 us, min = 6.716 us, total = 1.394 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.763 us, total = 28.575 us, Queueing time: mean = 49.638 us, max = 66.817 us, min = 35.290 us, total = 297.829 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.656 us, total = 20.656 us, Queueing time: mean = 15.200 us, max = 15.200 us, min = 15.200 us, total = 15.200 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 310.891 us, total = 310.891 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 329.048 us, total = 329.048 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 369.391 us, total = 369.391 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.872 us, total = 106.872 us, Queueing time: mean = 23.933 us, max = 23.933 us, min = 23.933 us, total = 23.933 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.301 us, total = 60.301 us, Queueing time: mean = 9.327 us, max = 9.327 us, min = 9.327 us, total = 9.327 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5962 total (1 active)
Queueing time: mean = 55.464 us, max = 2.138 ms, min = 2.421 us, total = 330.676 ms
Execution time:  mean = 14.979 us, total = 89.305 ms
Event stats:
	CoreWorker.CheckSignal - 5961 total (1 active), Execution time: mean = 14.980 us, total = 89.297 ms, Queueing time: mean = 55.473 us, max = 2.138 ms, min = 9.835 us, total = 330.673 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.649 us, total = 7.649 us, Queueing time: mean = 2.421 us, max = 2.421 us, min = 2.421 us, total = 2.421 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 30.016 us, max = 98.167 us, min = 6.794 us, total = 5.433 ms
Execution time:  mean = 326.517 us, total = 59.100 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 210.702 us, total = 12.642 ms, Queueing time: mean = 43.833 us, max = 94.512 us, min = 27.676 us, total = 2.630 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 38.778 us, total = 2.327 ms, Queueing time: mean = 46.602 us, max = 98.167 us, min = 8.027 us, total = 2.796 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 733.567 us, total = 44.014 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 116.777 us, total = 116.777 us, Queueing time: mean = 6.794 us, max = 6.794 us, min = 6.794 us, total = 6.794 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,530 I 660926 661914] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 48.316 us, max = 456.602 us, min = 6.469 us, total = 83.732 ms
Execution time:  mean = 70.846 us, total = 122.776 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.281 us, total = 9.937 ms, Queueing time: mean = 53.964 us, max = 115.083 us, min = 6.469 us, total = 64.756 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 179.850 us, total = 21.582 ms, Queueing time: mean = 42.298 us, max = 125.558 us, min = 17.532 us, total = 5.076 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.525 us, total = 1.263 ms, Queueing time: mean = 51.752 us, max = 96.488 us, min = 15.672 us, total = 6.210 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.114 us, total = 3.494 ms, Queueing time: mean = 38.927 us, max = 82.204 us, min = 10.330 us, total = 4.671 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 694.162 us, total = 83.299 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 33.044 us, total = 793.061 us, Queueing time: mean = 33.826 us, max = 71.844 us, min = 11.993 us, total = 811.828 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.926 us, total = 59.109 us, Queueing time: mean = 58.485 us, max = 148.498 us, min = 15.862 us, total = 701.823 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.050 us, total = 497.352 us, Queueing time: mean = 199.176 us, max = 456.602 us, min = 6.716 us, total = 1.394 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 327.248 us, total = 654.497 us, Queueing time: mean = 31.121 us, max = 62.242 us, min = 62.242 us, total = 62.242 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.656 us, total = 20.656 us, Queueing time: mean = 15.200 us, max = 15.200 us, min = 15.200 us, total = 15.200 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 310.891 us, total = 310.891 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 329.048 us, total = 329.048 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 369.391 us, total = 369.391 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.872 us, total = 106.872 us, Queueing time: mean = 23.933 us, max = 23.933 us, min = 23.933 us, total = 23.933 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.301 us, total = 60.301 us, Queueing time: mean = 9.327 us, max = 9.327 us, min = 9.327 us, total = 9.327 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11925 total (1 active)
Queueing time: mean = 54.483 us, max = 2.515 ms, min = -0.000 s, total = 649.713 ms
Execution time:  mean = 14.952 us, total = 178.304 ms
Event stats:
	CoreWorker.CheckSignal - 11924 total (1 active), Execution time: mean = 14.953 us, total = 178.297 ms, Queueing time: mean = 54.488 us, max = 2.515 ms, min = -0.000 s, total = 649.710 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.649 us, total = 7.649 us, Queueing time: mean = 2.421 us, max = 2.421 us, min = 2.421 us, total = 2.421 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 30.902 us, max = 108.837 us, min = 6.794 us, total = 11.156 ms
Execution time:  mean = 337.096 us, total = 121.692 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 214.751 us, total = 25.770 ms, Queueing time: mean = 47.699 us, max = 108.837 us, min = 27.676 us, total = 5.724 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 38.966 us, total = 4.676 ms, Queueing time: mean = 45.209 us, max = 106.938 us, min = 8.027 us, total = 5.425 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 759.407 us, total = 91.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 116.777 us, total = 116.777 us, Queueing time: mean = 6.794 us, max = 6.794 us, min = 6.794 us, total = 6.794 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,531 I 660926 661914] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 47.830 us, max = 685.557 us, min = -0.000 s, total = 123.928 ms
Execution time:  mean = 73.212 us, total = 189.693 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.449 us, total = 15.199 ms, Queueing time: mean = 52.745 us, max = 135.157 us, min = -0.000 s, total = 94.889 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 182.844 us, total = 32.912 ms, Queueing time: mean = 44.571 us, max = 125.558 us, min = -0.000 s, total = 8.023 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.719 us, total = 1.929 ms, Queueing time: mean = 50.784 us, max = 123.945 us, min = 15.672 us, total = 9.141 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.932 us, total = 5.388 ms, Queueing time: mean = 41.535 us, max = 84.717 us, min = 10.330 us, total = 7.476 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 720.699 us, total = 129.726 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 33.230 us, total = 1.196 ms, Queueing time: mean = 31.894 us, max = 71.844 us, min = 11.993 us, total = 1.148 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.163 us, total = 92.928 us, Queueing time: mean = 93.408 us, max = 685.557 us, min = 15.862 us, total = 1.681 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.050 us, total = 497.352 us, Queueing time: mean = 199.176 us, max = 456.602 us, min = 6.716 us, total = 1.394 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 518.366 us, total = 1.555 ms, Queueing time: mean = 42.039 us, max = 63.874 us, min = 62.242 us, total = 126.116 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.656 us, total = 20.656 us, Queueing time: mean = 15.200 us, max = 15.200 us, min = 15.200 us, total = 15.200 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 310.891 us, total = 310.891 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 329.048 us, total = 329.048 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 369.391 us, total = 369.391 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.872 us, total = 106.872 us, Queueing time: mean = 23.933 us, max = 23.933 us, min = 23.933 us, total = 23.933 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.301 us, total = 60.301 us, Queueing time: mean = 9.327 us, max = 9.327 us, min = 9.327 us, total = 9.327 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17884 total (1 active)
Queueing time: mean = 56.007 us, max = 2.515 ms, min = -0.000 s, total = 1.002 s
Execution time:  mean = 14.991 us, total = 268.090 ms
Event stats:
	CoreWorker.CheckSignal - 17883 total (1 active), Execution time: mean = 14.991 us, total = 268.082 ms, Queueing time: mean = 56.010 us, max = 2.515 ms, min = -0.000 s, total = 1.002 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.649 us, total = 7.649 us, Queueing time: mean = 2.421 us, max = 2.421 us, min = 2.421 us, total = 2.421 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 31.954 us, max = 109.753 us, min = 6.794 us, total = 17.287 ms
Execution time:  mean = 342.487 us, total = 185.286 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 216.598 us, total = 38.988 ms, Queueing time: mean = 49.788 us, max = 109.753 us, min = 27.676 us, total = 8.962 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 40.757 us, total = 7.336 ms, Queueing time: mean = 46.213 us, max = 106.938 us, min = 8.027 us, total = 8.318 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 771.360 us, total = 138.845 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 116.777 us, total = 116.777 us, Queueing time: mean = 6.794 us, max = 6.794 us, min = 6.794 us, total = 6.794 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,531 I 660926 661914] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 48.081 us, max = 685.557 us, min = -0.000 s, total = 165.879 ms
Execution time:  mean = 73.944 us, total = 255.106 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.442 us, total = 20.252 ms, Queueing time: mean = 53.188 us, max = 626.519 us, min = -0.000 s, total = 127.599 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 183.342 us, total = 44.002 ms, Queueing time: mean = 45.645 us, max = 125.558 us, min = -0.000 s, total = 10.955 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.747 us, total = 2.579 ms, Queueing time: mean = 50.067 us, max = 123.945 us, min = 14.769 us, total = 12.016 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.693 us, total = 7.126 ms, Queueing time: mean = 40.719 us, max = 84.717 us, min = 10.330 us, total = 9.772 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 730.990 us, total = 175.438 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 34.736 us, total = 1.667 ms, Queueing time: mean = 34.040 us, max = 73.418 us, min = 11.993 us, total = 1.634 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.239 us, total = 125.738 us, Queueing time: mean = 95.768 us, max = 685.557 us, min = 15.862 us, total = 2.298 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.050 us, total = 497.352 us, Queueing time: mean = 199.176 us, max = 456.602 us, min = 6.716 us, total = 1.394 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 555.192 us, total = 2.221 ms, Queueing time: mean = 40.417 us, max = 63.874 us, min = 35.552 us, total = 161.668 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.656 us, total = 20.656 us, Queueing time: mean = 15.200 us, max = 15.200 us, min = 15.200 us, total = 15.200 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 310.891 us, total = 310.891 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 329.048 us, total = 329.048 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 369.391 us, total = 369.391 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.872 us, total = 106.872 us, Queueing time: mean = 23.933 us, max = 23.933 us, min = 23.933 us, total = 23.933 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.301 us, total = 60.301 us, Queueing time: mean = 9.327 us, max = 9.327 us, min = 9.327 us, total = 9.327 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23846 total (1 active)
Queueing time: mean = 55.108 us, max = 2.515 ms, min = -0.000 s, total = 1.314 s
Execution time:  mean = 15.018 us, total = 358.123 ms
Event stats:
	CoreWorker.CheckSignal - 23845 total (1 active), Execution time: mean = 15.018 us, total = 358.116 ms, Queueing time: mean = 55.111 us, max = 2.515 ms, min = -0.000 s, total = 1.314 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.649 us, total = 7.649 us, Queueing time: mean = 2.421 us, max = 2.421 us, min = 2.421 us, total = 2.421 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 32.267 us, max = 138.898 us, min = 6.794 us, total = 23.264 ms
Execution time:  mean = 345.787 us, total = 249.313 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 218.064 us, total = 52.335 ms, Queueing time: mean = 51.109 us, max = 138.898 us, min = 24.302 us, total = 12.266 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 41.384 us, total = 9.932 ms, Queueing time: mean = 45.798 us, max = 106.938 us, min = 8.027 us, total = 10.991 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 778.869 us, total = 186.929 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 116.777 us, total = 116.777 us, Queueing time: mean = 6.794 us, max = 6.794 us, min = 6.794 us, total = 6.794 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,532 I 660926 661914] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 49.106 us, max = 685.557 us, min = -0.000 s, total = 211.647 ms
Execution time:  mean = 73.595 us, total = 317.192 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.335 us, total = 24.995 ms, Queueing time: mean = 54.253 us, max = 626.519 us, min = -0.000 s, total = 162.704 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 180.482 us, total = 54.145 ms, Queueing time: mean = 45.960 us, max = 125.558 us, min = -0.000 s, total = 13.788 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.638 us, total = 3.191 ms, Queueing time: mean = 52.371 us, max = 123.945 us, min = 14.769 us, total = 15.711 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.513 us, total = 8.854 ms, Queueing time: mean = 41.193 us, max = 84.717 us, min = 10.330 us, total = 12.358 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 730.616 us, total = 219.185 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 34.868 us, total = 2.092 ms, Queueing time: mean = 39.658 us, max = 74.278 us, min = 11.993 us, total = 2.380 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.160 us, total = 154.797 us, Queueing time: mean = 100.347 us, max = 685.557 us, min = 15.862 us, total = 3.010 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.050 us, total = 497.352 us, Queueing time: mean = 199.176 us, max = 456.602 us, min = 6.716 us, total = 1.394 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 573.555 us, total = 2.868 ms, Queueing time: mean = 37.201 us, max = 63.874 us, min = 24.338 us, total = 186.006 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.695 us, total = 13.390 us, Queueing time: mean = 33.758 us, max = 67.517 us, min = 67.517 us, total = 67.517 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.656 us, total = 20.656 us, Queueing time: mean = 15.200 us, max = 15.200 us, min = 15.200 us, total = 15.200 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 310.891 us, total = 310.891 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 329.048 us, total = 329.048 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 369.391 us, total = 369.391 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.872 us, total = 106.872 us, Queueing time: mean = 23.933 us, max = 23.933 us, min = 23.933 us, total = 23.933 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.301 us, total = 60.301 us, Queueing time: mean = 9.327 us, max = 9.327 us, min = 9.327 us, total = 9.327 us

-----------------
Task execution event stats:

Global stats: 29810 total (1 active)
Queueing time: mean = 54.123 us, max = 2.515 ms, min = -0.000 s, total = 1.613 s
Execution time:  mean = 15.107 us, total = 450.330 ms
Event stats:
	CoreWorker.CheckSignal - 29809 total (1 active), Execution time: mean = 15.107 us, total = 450.323 ms, Queueing time: mean = 54.124 us, max = 2.515 ms, min = -0.000 s, total = 1.613 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.649 us, total = 7.649 us, Queueing time: mean = 2.421 us, max = 2.421 us, min = 2.421 us, total = 2.421 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 32.683 us, max = 138.898 us, min = 6.794 us, total = 29.447 ms
Execution time:  mean = 343.570 us, total = 309.556 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 216.662 us, total = 64.998 ms, Queueing time: mean = 52.725 us, max = 138.898 us, min = 24.302 us, total = 15.817 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 40.625 us, total = 12.188 ms, Queueing time: mean = 45.409 us, max = 106.938 us, min = 8.027 us, total = 13.623 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 774.179 us, total = 232.254 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 116.777 us, total = 116.777 us, Queueing time: mean = 6.794 us, max = 6.794 us, min = 6.794 us, total = 6.794 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,533 I 660926 661914] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.025 us, max = 685.557 us, min = -0.000 s, total = 258.530 ms
Execution time:  mean = 73.627 us, total = 380.505 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.412 us, total = 30.267 ms, Queueing time: mean = 55.100 us, max = 626.519 us, min = -0.000 s, total = 198.250 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 180.642 us, total = 65.031 ms, Queueing time: mean = 47.508 us, max = 125.558 us, min = -0.000 s, total = 17.103 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.965 us, total = 3.947 ms, Queueing time: mean = 53.649 us, max = 123.945 us, min = 14.769 us, total = 19.314 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.624 us, total = 10.665 ms, Queueing time: mean = 42.664 us, max = 84.717 us, min = 10.330 us, total = 15.359 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 729.587 us, total = 262.651 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.068 us, total = 2.525 ms, Queueing time: mean = 42.349 us, max = 77.723 us, min = 11.993 us, total = 3.049 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.225 us, total = 188.096 us, Queueing time: mean = 102.506 us, max = 685.557 us, min = 15.862 us, total = 3.690 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.050 us, total = 497.352 us, Queueing time: mean = 199.176 us, max = 456.602 us, min = 6.716 us, total = 1.394 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 587.121 us, total = 3.523 ms, Queueing time: mean = 42.471 us, max = 68.823 us, min = 24.338 us, total = 254.829 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.695 us, total = 13.390 us, Queueing time: mean = 33.758 us, max = 67.517 us, min = 67.517 us, total = 67.517 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.656 us, total = 20.656 us, Queueing time: mean = 15.200 us, max = 15.200 us, min = 15.200 us, total = 15.200 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 310.891 us, total = 310.891 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 329.048 us, total = 329.048 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 369.391 us, total = 369.391 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.872 us, total = 106.872 us, Queueing time: mean = 23.933 us, max = 23.933 us, min = 23.933 us, total = 23.933 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 60.301 us, total = 60.301 us, Queueing time: mean = 9.327 us, max = 9.327 us, min = 9.327 us, total = 9.327 us

-----------------
Task execution event stats:

Global stats: 35769 total (1 active)
Queueing time: mean = 55.027 us, max = 6.092 ms, min = -0.000 s, total = 1.968 s
Execution time:  mean = 15.088 us, total = 539.679 ms
Event stats:
	CoreWorker.CheckSignal - 35768 total (1 active), Execution time: mean = 15.088 us, total = 539.671 ms, Queueing time: mean = 55.029 us, max = 6.092 ms, min = -0.000 s, total = 1.968 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.649 us, total = 7.649 us, Queueing time: mean = 2.421 us, max = 2.421 us, min = 2.421 us, total = 2.421 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 32.353 us, max = 138.898 us, min = 6.794 us, total = 34.974 ms
Execution time:  mean = 342.162 us, total = 369.877 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 213.951 us, total = 77.022 ms, Queueing time: mean = 51.476 us, max = 138.898 us, min = 22.051 us, total = 18.531 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 39.952 us, total = 14.383 ms, Queueing time: mean = 45.654 us, max = 106.938 us, min = 8.027 us, total = 16.435 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 773.210 us, total = 278.356 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 116.777 us, total = 116.777 us, Queueing time: mean = 6.794 us, max = 6.794 us, min = 6.794 us, total = 6.794 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660926 661914] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660926 661914] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660926 661914] core_worker.cc:5107: Number of alive nodes:0
