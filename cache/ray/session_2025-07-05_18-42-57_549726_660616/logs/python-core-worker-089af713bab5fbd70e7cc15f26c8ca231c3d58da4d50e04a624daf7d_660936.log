[2025-07-05 18:42:59,484 I 660936 660936] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660936
[2025-07-05 18:42:59,487 I 660936 660936] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,489 I 660936 660936] grpc_server.cc:141: worker server started, listening on port 35103.
[2025-07-05 18:42:59,492 I 660936 660936] core_worker.cc:542: Initializing worker at address: ***********:35103 worker_id=089af713bab5fbd70e7cc15f26c8ca231c3d58da4d50e04a624daf7d node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,493 I 660936 660936] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,494 I 660936 661620] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,494 I 660936 661620] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,494 I 660936 660936] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,494 I 660936 661620] core_worker.cc:902: Event stats:


Global stats: 12 total (4 active)
Queueing time: mean = 28.590 us, max = 198.661 us, min = 7.600 us, total = 343.077 us
Execution time:  mean = 108.804 us, total = 1.306 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 7.441 us, total = 22.323 us, Queueing time: mean = 92.176 us, max = 198.661 us, min = 77.867 us, total = 276.528 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 393.890 us, total = 393.890 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 409.488 us, total = 409.488 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 99.077 us, total = 99.077 us, Queueing time: mean = 11.441 us, max = 11.441 us, min = 11.441 us, total = 11.441 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 307.485 us, total = 307.485 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.837 us, total = 18.837 us, Queueing time: mean = 47.508 us, max = 47.508 us, min = 47.508 us, total = 47.508 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.543 us, total = 54.543 us, Queueing time: mean = 7.600 us, max = 7.600 us, min = 7.600 us, total = 7.600 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 58.734 us, max = 217.647 us, min = 17.290 us, total = 234.937 us
Execution time:  mean = 137.243 us, total = 548.973 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 433.201 us, total = 433.201 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 11.612 us, total = 11.612 us, Queueing time: mean = 17.290 us, max = 17.290 us, min = 17.290 us, total = 17.290 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 104.160 us, total = 104.160 us, Queueing time: mean = 217.647 us, max = 217.647 us, min = 217.647 us, total = 217.647 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,494 I 660936 660936] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,495 I 660936 660936] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,495 I 660936 660936] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,498 W 660936 661600] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,495 I 660936 661620] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 45.095 us, max = 835.136 us, min = 7.169 us, total = 39.458 ms
Execution time:  mean = 68.962 us, total = 60.342 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.624 us, total = 5.174 ms, Queueing time: mean = 49.153 us, max = 475.078 us, min = 11.393 us, total = 29.492 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 11.144 us, total = 679.790 us, Queueing time: mean = 42.094 us, max = 79.158 us, min = 20.303 us, total = 2.568 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 26.565 us, total = 1.594 ms, Queueing time: mean = 34.248 us, max = 91.664 us, min = 9.559 us, total = 2.055 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 172.031 us, total = 10.322 ms, Queueing time: mean = 39.502 us, max = 77.198 us, min = 11.194 us, total = 2.370 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 666.996 us, total = 40.020 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 31.531 us, total = 378.367 us, Queueing time: mean = 25.566 us, max = 67.100 us, min = 7.169 us, total = 306.792 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 123.512 us, total = 864.587 us, Queueing time: mean = 343.315 us, max = 835.136 us, min = 7.247 us, total = 2.403 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.348 us, total = 26.089 us, Queueing time: mean = 32.892 us, max = 87.009 us, min = 19.298 us, total = 197.353 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 307.485 us, total = 307.485 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 99.077 us, total = 99.077 us, Queueing time: mean = 11.441 us, max = 11.441 us, min = 11.441 us, total = 11.441 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.543 us, total = 54.543 us, Queueing time: mean = 7.600 us, max = 7.600 us, min = 7.600 us, total = 7.600 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 393.890 us, total = 393.890 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 409.488 us, total = 409.488 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.837 us, total = 18.837 us, Queueing time: mean = 47.508 us, max = 47.508 us, min = 47.508 us, total = 47.508 us

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.759 us, max = 2.345 ms, min = 2.807 us, total = 326.528 ms
Execution time:  mean = 14.990 us, total = 89.385 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 14.991 us, total = 89.377 ms, Queueing time: mean = 54.768 us, max = 2.345 ms, min = 4.730 us, total = 326.526 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.198 us, total = 8.198 us, Queueing time: mean = 2.807 us, max = 2.807 us, min = 2.807 us, total = 2.807 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 30.395 us, max = 217.647 us, min = 9.653 us, total = 5.501 ms
Execution time:  mean = 297.864 us, total = 53.913 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 664.367 us, total = 39.862 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 33.607 us, total = 2.016 ms, Queueing time: mean = 38.473 us, max = 71.043 us, min = 9.653 us, total = 2.308 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 198.846 us, total = 11.931 ms, Queueing time: mean = 49.590 us, max = 91.944 us, min = 16.202 us, total = 2.975 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 104.160 us, total = 104.160 us, Queueing time: mean = 217.647 us, max = 217.647 us, min = 217.647 us, total = 217.647 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,496 I 660936 661620] core_worker.cc:902: Event stats:


Global stats: 1734 total (8 active)
Queueing time: mean = 45.261 us, max = 835.136 us, min = -0.000 s, total = 78.482 ms
Execution time:  mean = 71.271 us, total = 123.583 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.774 us, total = 10.529 ms, Queueing time: mean = 50.010 us, max = 475.078 us, min = -0.000 s, total = 60.012 ms
	CoreWorker.ExitIfParentRayletDies - 121 total (1 active), Execution time: mean = 11.457 us, total = 1.386 ms, Queueing time: mean = 45.964 us, max = 94.867 us, min = 20.303 us, total = 5.562 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.974 us, total = 3.477 ms, Queueing time: mean = 36.628 us, max = 91.664 us, min = 9.559 us, total = 4.395 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 183.791 us, total = 22.055 ms, Queueing time: mean = 38.700 us, max = 77.198 us, min = 11.194 us, total = 4.644 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 686.191 us, total = 82.343 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 35.927 us, total = 862.250 us, Queueing time: mean = 36.255 us, max = 72.761 us, min = 7.169 us, total = 870.131 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.168 us, total = 62.022 us, Queueing time: mean = 39.937 us, max = 87.009 us, min = 19.298 us, total = 479.246 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 123.512 us, total = 864.587 us, Queueing time: mean = 343.315 us, max = 835.136 us, min = 7.247 us, total = 2.403 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 360.609 us, total = 721.218 us, Queueing time: mean = 25.061 us, max = 50.122 us, min = 50.122 us, total = 50.122 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 307.485 us, total = 307.485 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 99.077 us, total = 99.077 us, Queueing time: mean = 11.441 us, max = 11.441 us, min = 11.441 us, total = 11.441 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.543 us, total = 54.543 us, Queueing time: mean = 7.600 us, max = 7.600 us, min = 7.600 us, total = 7.600 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 393.890 us, total = 393.890 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 409.488 us, total = 409.488 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.837 us, total = 18.837 us, Queueing time: mean = 47.508 us, max = 47.508 us, min = 47.508 us, total = 47.508 us

-----------------
Task execution event stats:

Global stats: 11921 total (1 active)
Queueing time: mean = 56.859 us, max = 2.345 ms, min = 2.807 us, total = 677.821 ms
Execution time:  mean = 15.346 us, total = 182.940 ms
Event stats:
	CoreWorker.CheckSignal - 11920 total (1 active), Execution time: mean = 15.347 us, total = 182.932 ms, Queueing time: mean = 56.864 us, max = 2.345 ms, min = 4.730 us, total = 677.818 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.198 us, total = 8.198 us, Queueing time: mean = 2.807 us, max = 2.807 us, min = 2.807 us, total = 2.807 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 30.271 us, max = 217.647 us, min = 9.653 us, total = 10.928 ms
Execution time:  mean = 311.810 us, total = 112.563 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 696.903 us, total = 83.628 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.997 us, total = 4.200 ms, Queueing time: mean = 39.507 us, max = 71.043 us, min = 9.653 us, total = 4.741 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 205.262 us, total = 24.631 ms, Queueing time: mean = 49.745 us, max = 154.029 us, min = 16.202 us, total = 5.969 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 104.160 us, total = 104.160 us, Queueing time: mean = 217.647 us, max = 217.647 us, min = 217.647 us, total = 217.647 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,497 I 660936 661620] core_worker.cc:902: Event stats:


Global stats: 2592 total (8 active)
Queueing time: mean = 45.619 us, max = 835.136 us, min = -0.000 s, total = 118.243 ms
Execution time:  mean = 72.177 us, total = 187.083 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.788 us, total = 15.819 ms, Queueing time: mean = 50.215 us, max = 475.078 us, min = -0.000 s, total = 90.387 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.541 us, total = 5.317 ms, Queueing time: mean = 38.404 us, max = 91.664 us, min = 9.559 us, total = 6.913 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 185.117 us, total = 33.321 ms, Queueing time: mean = 40.274 us, max = 92.947 us, min = 11.194 us, total = 7.249 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 696.586 us, total = 125.385 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 11.433 us, total = 2.058 ms, Queueing time: mean = 46.935 us, max = 94.867 us, min = 14.016 us, total = 8.448 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.269 us, total = 1.270 ms, Queueing time: mean = 34.184 us, max = 72.761 us, min = 7.169 us, total = 1.231 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.975 us, total = 89.555 us, Queueing time: mean = 77.415 us, max = 698.091 us, min = 15.592 us, total = 1.393 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 123.512 us, total = 864.587 us, Queueing time: mean = 343.315 us, max = 835.136 us, min = 7.247 us, total = 2.403 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 558.410 us, total = 1.675 ms, Queueing time: mean = 50.729 us, max = 102.065 us, min = 50.122 us, total = 152.187 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 99.077 us, total = 99.077 us, Queueing time: mean = 11.441 us, max = 11.441 us, min = 11.441 us, total = 11.441 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 307.485 us, total = 307.485 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.543 us, total = 54.543 us, Queueing time: mean = 7.600 us, max = 7.600 us, min = 7.600 us, total = 7.600 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 393.890 us, total = 393.890 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 409.488 us, total = 409.488 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.837 us, total = 18.837 us, Queueing time: mean = 47.508 us, max = 47.508 us, min = 47.508 us, total = 47.508 us

-----------------
Task execution event stats:

Global stats: 17886 total (1 active)
Queueing time: mean = 54.291 us, max = 2.345 ms, min = -0.000 s, total = 971.048 ms
Execution time:  mean = 15.409 us, total = 275.610 ms
Event stats:
	CoreWorker.CheckSignal - 17885 total (1 active), Execution time: mean = 15.410 us, total = 275.601 ms, Queueing time: mean = 54.294 us, max = 2.345 ms, min = -0.000 s, total = 971.045 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.198 us, total = 8.198 us, Queueing time: mean = 2.807 us, max = 2.807 us, min = 2.807 us, total = 2.807 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 30.397 us, max = 217.647 us, min = 9.653 us, total = 16.445 ms
Execution time:  mean = 313.797 us, total = 169.764 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 702.971 us, total = 126.535 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 35.185 us, total = 6.333 ms, Queueing time: mean = 39.559 us, max = 71.043 us, min = 9.653 us, total = 7.121 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 204.400 us, total = 36.792 ms, Queueing time: mean = 50.592 us, max = 154.029 us, min = 16.202 us, total = 9.107 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 104.160 us, total = 104.160 us, Queueing time: mean = 217.647 us, max = 217.647 us, min = 217.647 us, total = 217.647 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,498 I 660936 661620] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 46.284 us, max = 835.136 us, min = -0.000 s, total = 159.679 ms
Execution time:  mean = 73.165 us, total = 252.418 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.743 us, total = 20.976 ms, Queueing time: mean = 50.705 us, max = 475.078 us, min = -0.000 s, total = 121.642 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.567 us, total = 7.096 ms, Queueing time: mean = 39.617 us, max = 104.074 us, min = 9.559 us, total = 9.508 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 186.357 us, total = 44.726 ms, Queueing time: mean = 41.846 us, max = 108.725 us, min = 11.194 us, total = 10.043 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 710.529 us, total = 170.527 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 11.518 us, total = 2.764 ms, Queueing time: mean = 48.359 us, max = 134.118 us, min = 12.362 us, total = 11.606 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.007 us, total = 1.680 ms, Queueing time: mean = 40.146 us, max = 81.370 us, min = 7.169 us, total = 1.927 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.104 us, total = 122.498 us, Queueing time: mean = 95.777 us, max = 698.091 us, min = 15.592 us, total = 2.299 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 123.512 us, total = 864.587 us, Queueing time: mean = 343.315 us, max = 835.136 us, min = 7.247 us, total = 2.403 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 594.705 us, total = 2.379 ms, Queueing time: mean = 46.154 us, max = 102.065 us, min = 32.431 us, total = 184.618 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 99.077 us, total = 99.077 us, Queueing time: mean = 11.441 us, max = 11.441 us, min = 11.441 us, total = 11.441 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 307.485 us, total = 307.485 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.543 us, total = 54.543 us, Queueing time: mean = 7.600 us, max = 7.600 us, min = 7.600 us, total = 7.600 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 393.890 us, total = 393.890 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 409.488 us, total = 409.488 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.837 us, total = 18.837 us, Queueing time: mean = 47.508 us, max = 47.508 us, min = 47.508 us, total = 47.508 us

-----------------
Task execution event stats:

Global stats: 23849 total (1 active)
Queueing time: mean = 53.636 us, max = 2.345 ms, min = -0.000 s, total = 1.279 s
Execution time:  mean = 15.429 us, total = 367.957 ms
Event stats:
	CoreWorker.CheckSignal - 23848 total (1 active), Execution time: mean = 15.429 us, total = 367.949 ms, Queueing time: mean = 53.638 us, max = 2.345 ms, min = -0.000 s, total = 1.279 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.198 us, total = 8.198 us, Queueing time: mean = 2.807 us, max = 2.807 us, min = 2.807 us, total = 2.807 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 30.588 us, max = 217.647 us, min = 9.653 us, total = 22.054 ms
Execution time:  mean = 318.047 us, total = 229.312 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 712.807 us, total = 171.074 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.376 us, total = 8.730 ms, Queueing time: mean = 40.309 us, max = 81.298 us, min = 9.653 us, total = 9.674 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 205.849 us, total = 49.404 ms, Queueing time: mean = 50.677 us, max = 154.029 us, min = 16.202 us, total = 12.162 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 104.160 us, total = 104.160 us, Queueing time: mean = 217.647 us, max = 217.647 us, min = 217.647 us, total = 217.647 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,498 I 660936 661620] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 47.529 us, max = 835.136 us, min = -0.000 s, total = 204.848 ms
Execution time:  mean = 73.496 us, total = 316.766 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.613 us, total = 25.832 ms, Queueing time: mean = 52.195 us, max = 475.078 us, min = -0.000 s, total = 156.534 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.434 us, total = 8.830 ms, Queueing time: mean = 39.238 us, max = 104.074 us, min = 9.559 us, total = 11.771 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 184.253 us, total = 55.276 ms, Queueing time: mean = 44.276 us, max = 123.626 us, min = 11.194 us, total = 13.283 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 719.818 us, total = 215.945 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.314 us, total = 3.394 ms, Queueing time: mean = 50.557 us, max = 134.118 us, min = 12.362 us, total = 15.167 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.361 us, total = 2.122 ms, Queueing time: mean = 41.126 us, max = 81.370 us, min = 7.169 us, total = 2.468 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.059 us, total = 151.777 us, Queueing time: mean = 95.075 us, max = 698.091 us, min = 15.592 us, total = 2.852 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 123.512 us, total = 864.587 us, Queueing time: mean = 343.315 us, max = 835.136 us, min = 7.247 us, total = 2.403 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 611.936 us, total = 3.060 ms, Queueing time: mean = 45.225 us, max = 102.065 us, min = 32.431 us, total = 226.124 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 3.790 us, total = 7.580 us, Queueing time: mean = 38.608 us, max = 77.217 us, min = 77.217 us, total = 77.217 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 99.077 us, total = 99.077 us, Queueing time: mean = 11.441 us, max = 11.441 us, min = 11.441 us, total = 11.441 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 307.485 us, total = 307.485 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.543 us, total = 54.543 us, Queueing time: mean = 7.600 us, max = 7.600 us, min = 7.600 us, total = 7.600 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 393.890 us, total = 393.890 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 409.488 us, total = 409.488 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.837 us, total = 18.837 us, Queueing time: mean = 47.508 us, max = 47.508 us, min = 47.508 us, total = 47.508 us

-----------------
Task execution event stats:

Global stats: 29811 total (1 active)
Queueing time: mean = 53.667 us, max = 2.345 ms, min = -0.000 s, total = 1.600 s
Execution time:  mean = 15.426 us, total = 459.861 ms
Event stats:
	CoreWorker.CheckSignal - 29810 total (1 active), Execution time: mean = 15.426 us, total = 459.853 ms, Queueing time: mean = 53.668 us, max = 2.345 ms, min = -0.000 s, total = 1.600 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.198 us, total = 8.198 us, Queueing time: mean = 2.807 us, max = 2.807 us, min = 2.807 us, total = 2.807 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 31.075 us, max = 217.647 us, min = 9.653 us, total = 27.998 ms
Execution time:  mean = 322.932 us, total = 290.961 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 723.784 us, total = 217.135 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.867 us, total = 11.060 ms, Queueing time: mean = 41.361 us, max = 81.298 us, min = 9.653 us, total = 12.408 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 208.874 us, total = 62.662 ms, Queueing time: mean = 51.240 us, max = 154.029 us, min = 16.202 us, total = 15.372 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 104.160 us, total = 104.160 us, Queueing time: mean = 217.647 us, max = 217.647 us, min = 217.647 us, total = 217.647 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,499 I 660936 661620] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 48.541 us, max = 835.136 us, min = -0.000 s, total = 250.862 ms
Execution time:  mean = 74.040 us, total = 382.637 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.720 us, total = 31.376 ms, Queueing time: mean = 53.259 us, max = 475.078 us, min = -0.000 s, total = 191.627 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.762 us, total = 10.714 ms, Queueing time: mean = 39.466 us, max = 104.074 us, min = 9.559 us, total = 14.208 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 184.570 us, total = 66.445 ms, Queueing time: mean = 46.321 us, max = 123.626 us, min = 11.194 us, total = 16.676 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 725.923 us, total = 261.332 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.450 us, total = 4.122 ms, Queueing time: mean = 52.229 us, max = 134.118 us, min = 12.362 us, total = 18.802 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.874 us, total = 2.583 ms, Queueing time: mean = 44.479 us, max = 91.977 us, min = 7.169 us, total = 3.203 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.103 us, total = 183.701 us, Queueing time: mean = 97.404 us, max = 698.091 us, min = 15.592 us, total = 3.507 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 123.512 us, total = 864.587 us, Queueing time: mean = 343.315 us, max = 835.136 us, min = 7.247 us, total = 2.403 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 620.853 us, total = 3.725 ms, Queueing time: mean = 48.883 us, max = 102.065 us, min = 32.431 us, total = 293.299 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 3.790 us, total = 7.580 us, Queueing time: mean = 38.608 us, max = 77.217 us, min = 77.217 us, total = 77.217 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 99.077 us, total = 99.077 us, Queueing time: mean = 11.441 us, max = 11.441 us, min = 11.441 us, total = 11.441 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 307.485 us, total = 307.485 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.543 us, total = 54.543 us, Queueing time: mean = 7.600 us, max = 7.600 us, min = 7.600 us, total = 7.600 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 393.890 us, total = 393.890 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 409.488 us, total = 409.488 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.837 us, total = 18.837 us, Queueing time: mean = 47.508 us, max = 47.508 us, min = 47.508 us, total = 47.508 us

-----------------
Task execution event stats:

Global stats: 35769 total (1 active)
Queueing time: mean = 54.851 us, max = 3.139 ms, min = -0.000 s, total = 1.962 s
Execution time:  mean = 15.370 us, total = 549.759 ms
Event stats:
	CoreWorker.CheckSignal - 35768 total (1 active), Execution time: mean = 15.370 us, total = 549.751 ms, Queueing time: mean = 54.852 us, max = 3.139 ms, min = -0.000 s, total = 1.962 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.198 us, total = 8.198 us, Queueing time: mean = 2.807 us, max = 2.807 us, min = 2.807 us, total = 2.807 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 31.713 us, max = 217.647 us, min = 9.653 us, total = 34.281 ms
Execution time:  mean = 325.550 us, total = 351.919 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 732.085 us, total = 263.551 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.728 us, total = 13.222 ms, Queueing time: mean = 42.462 us, max = 109.930 us, min = 9.653 us, total = 15.286 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 208.451 us, total = 75.042 ms, Queueing time: mean = 52.159 us, max = 154.029 us, min = 16.202 us, total = 18.777 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 104.160 us, total = 104.160 us, Queueing time: mean = 217.647 us, max = 217.647 us, min = 217.647 us, total = 217.647 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660936 661620] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660936 661620] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660936 661620] core_worker.cc:5107: Number of alive nodes:0
