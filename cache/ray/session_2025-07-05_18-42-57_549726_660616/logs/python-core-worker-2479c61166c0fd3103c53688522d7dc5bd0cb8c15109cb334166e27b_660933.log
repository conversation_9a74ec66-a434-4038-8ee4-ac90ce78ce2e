[2025-07-05 18:42:59,529 I 660933 660933] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660933
[2025-07-05 18:42:59,530 I 660933 660933] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,532 I 660933 660933] grpc_server.cc:141: worker server started, listening on port 39819.
[2025-07-05 18:42:59,534 I 660933 660933] core_worker.cc:542: Initializing worker at address: ***********:39819 worker_id=2479c61166c0fd3103c53688522d7dc5bd0cb8c15109cb334166e27b node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,534 I 660933 660933] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,535 I 660933 661964] core_worker.cc:902: Event stats:


Global stats: 11 total (5 active)
Queueing time: mean = 8.493 us, max = 70.825 us, min = 5.461 us, total = 93.428 us
Execution time:  mean = 78.914 us, total = 868.054 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 8.600 us, total = 25.801 us, Queueing time: mean = 26.602 us, max = 70.825 us, min = 8.981 us, total = 79.806 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.752 us, total = 309.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.922 us, total = 123.922 us, Queueing time: mean = 5.461 us, max = 5.461 us, min = 5.461 us, total = 5.461 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 379.688 us, total = 379.688 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 28.891 us, total = 28.891 us, Queueing time: mean = 8.161 us, max = 8.161 us, min = 8.161 us, total = 8.161 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 9.380 us, max = 31.807 us, min = 5.715 us, total = 37.522 us
Execution time:  mean = 157.565 us, total = 630.261 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 514.144 us, total = 514.144 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.200 us, total = 98.200 us, Queueing time: mean = 5.715 us, max = 5.715 us, min = 5.715 us, total = 5.715 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.917 us, total = 17.917 us, Queueing time: mean = 31.807 us, max = 31.807 us, min = 31.807 us, total = 31.807 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,535 I 660933 661964] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,535 I 660933 661964] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,536 I 660933 660933] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,536 I 660933 660933] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,536 I 660933 660933] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,536 I 660933 660933] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,541 W 660933 661959] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,536 I 660933 661964] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 52.610 us, max = 200.910 us, min = 5.461 us, total = 46.033 ms
Execution time:  mean = 73.001 us, total = 63.876 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.212 us, total = 4.927 ms, Queueing time: mean = 59.318 us, max = 121.521 us, min = 15.516 us, total = 35.591 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.121 us, total = 617.362 us, Queueing time: mean = 52.846 us, max = 86.518 us, min = 21.077 us, total = 3.224 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 29.111 us, total = 1.747 ms, Queueing time: mean = 48.645 us, max = 80.777 us, min = 12.643 us, total = 2.919 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 169.477 us, total = 10.169 ms, Queueing time: mean = 53.785 us, max = 105.093 us, min = 19.115 us, total = 3.227 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 737.623 us, total = 44.257 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 32.697 us, total = 392.362 us, Queueing time: mean = 35.531 us, max = 71.383 us, min = 14.710 us, total = 426.368 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.329 us, total = 499.302 us, Queueing time: mean = 18.195 us, max = 70.825 us, min = 6.733 us, total = 127.366 us
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.173 us, total = 31.037 us, Queueing time: mean = 50.858 us, max = 71.754 us, min = 34.452 us, total = 305.145 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 379.688 us, total = 379.688 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 28.891 us, total = 28.891 us, Queueing time: mean = 8.161 us, max = 8.161 us, min = 8.161 us, total = 8.161 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.752 us, total = 309.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 343.685 us, total = 343.685 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.922 us, total = 123.922 us, Queueing time: mean = 5.461 us, max = 5.461 us, min = 5.461 us, total = 5.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 49.904 us, total = 49.904 us, Queueing time: mean = 200.910 us, max = 200.910 us, min = 200.910 us, total = 200.910 us

-----------------
Task execution event stats:

Global stats: 5964 total (1 active)
Queueing time: mean = 52.432 us, max = 1.985 ms, min = -0.000 s, total = 312.706 ms
Execution time:  mean = 14.937 us, total = 89.085 ms
Event stats:
	CoreWorker.CheckSignal - 5963 total (1 active), Execution time: mean = 14.938 us, total = 89.078 ms, Queueing time: mean = 52.441 us, max = 1.985 ms, min = -0.000 s, total = 312.704 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.830 us, total = 7.830 us, Queueing time: mean = 2.811 us, max = 2.811 us, min = 2.811 us, total = 2.811 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 31.461 us, max = 107.507 us, min = 5.715 us, total = 5.694 ms
Execution time:  mean = 336.531 us, total = 60.912 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 761.909 us, total = 45.715 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 211.601 us, total = 12.696 ms, Queueing time: mean = 51.992 us, max = 97.144 us, min = 22.755 us, total = 3.120 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 40.056 us, total = 2.403 ms, Queueing time: mean = 42.821 us, max = 107.507 us, min = 11.632 us, total = 2.569 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.200 us, total = 98.200 us, Queueing time: mean = 5.715 us, max = 5.715 us, min = 5.715 us, total = 5.715 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,537 I 660933 661964] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 54.077 us, max = 200.910 us, min = 5.461 us, total = 93.716 ms
Execution time:  mean = 74.671 us, total = 129.404 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.222 us, total = 9.866 ms, Queueing time: mean = 60.652 us, max = 125.070 us, min = 15.516 us, total = 72.782 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.107 us, total = 1.213 ms, Queueing time: mean = 53.035 us, max = 86.518 us, min = 16.562 us, total = 6.364 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.630 us, total = 3.556 ms, Queueing time: mean = 48.537 us, max = 80.777 us, min = 12.643 us, total = 5.824 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 172.176 us, total = 20.661 ms, Queueing time: mean = 56.335 us, max = 105.093 us, min = 19.115 us, total = 6.760 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 756.555 us, total = 90.787 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 35.670 us, total = 856.073 us, Queueing time: mean = 40.464 us, max = 81.846 us, min = 13.198 us, total = 971.143 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.098 us, total = 61.173 us, Queueing time: mean = 50.228 us, max = 77.032 us, min = 15.743 us, total = 602.737 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.329 us, total = 499.302 us, Queueing time: mean = 18.195 us, max = 70.825 us, min = 6.733 us, total = 127.366 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 334.500 us, total = 669.001 us, Queueing time: mean = 34.505 us, max = 69.011 us, min = 69.011 us, total = 69.011 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 379.688 us, total = 379.688 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 28.891 us, total = 28.891 us, Queueing time: mean = 8.161 us, max = 8.161 us, min = 8.161 us, total = 8.161 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.752 us, total = 309.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 343.685 us, total = 343.685 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.922 us, total = 123.922 us, Queueing time: mean = 5.461 us, max = 5.461 us, min = 5.461 us, total = 5.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 49.904 us, total = 49.904 us, Queueing time: mean = 200.910 us, max = 200.910 us, min = 200.910 us, total = 200.910 us

-----------------
Task execution event stats:

Global stats: 11929 total (1 active)
Queueing time: mean = 50.906 us, max = 1.985 ms, min = -0.000 s, total = 607.254 ms
Execution time:  mean = 15.273 us, total = 182.190 ms
Event stats:
	CoreWorker.CheckSignal - 11928 total (1 active), Execution time: mean = 15.273 us, total = 182.182 ms, Queueing time: mean = 50.910 us, max = 1.985 ms, min = -0.000 s, total = 607.251 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.830 us, total = 7.830 us, Queueing time: mean = 2.811 us, max = 2.811 us, min = 2.811 us, total = 2.811 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 33.154 us, max = 112.811 us, min = 5.715 us, total = 11.969 ms
Execution time:  mean = 349.905 us, total = 126.316 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 788.969 us, total = 94.676 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 220.065 us, total = 26.408 ms, Queueing time: mean = 54.375 us, max = 109.243 us, min = 22.755 us, total = 6.525 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 42.780 us, total = 5.134 ms, Queueing time: mean = 45.317 us, max = 112.811 us, min = 11.632 us, total = 5.438 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.200 us, total = 98.200 us, Queueing time: mean = 5.715 us, max = 5.715 us, min = 5.715 us, total = 5.715 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,538 I 660933 661964] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 52.278 us, max = 543.656 us, min = 5.461 us, total = 135.453 ms
Execution time:  mean = 75.609 us, total = 195.903 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.368 us, total = 15.054 ms, Queueing time: mean = 58.202 us, max = 160.303 us, min = 13.384 us, total = 104.705 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.297 us, total = 1.854 ms, Queueing time: mean = 52.698 us, max = 122.902 us, min = 16.562 us, total = 9.486 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.694 us, total = 5.345 ms, Queueing time: mean = 45.144 us, max = 102.217 us, min = 12.643 us, total = 8.126 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 175.391 us, total = 31.570 ms, Queueing time: mean = 54.280 us, max = 105.093 us, min = 19.115 us, total = 9.770 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 764.116 us, total = 137.541 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 36.353 us, total = 1.309 ms, Queueing time: mean = 42.291 us, max = 82.305 us, min = 13.198 us, total = 1.522 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.024 us, total = 90.427 us, Queueing time: mean = 74.741 us, max = 543.656 us, min = 15.743 us, total = 1.345 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.329 us, total = 499.302 us, Queueing time: mean = 18.195 us, max = 70.825 us, min = 6.733 us, total = 127.366 us
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 468.218 us, total = 1.405 ms, Queueing time: mean = 52.004 us, max = 87.000 us, min = 69.011 us, total = 156.011 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 379.688 us, total = 379.688 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 28.891 us, total = 28.891 us, Queueing time: mean = 8.161 us, max = 8.161 us, min = 8.161 us, total = 8.161 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.752 us, total = 309.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 343.685 us, total = 343.685 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.922 us, total = 123.922 us, Queueing time: mean = 5.461 us, max = 5.461 us, min = 5.461 us, total = 5.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 49.904 us, total = 49.904 us, Queueing time: mean = 200.910 us, max = 200.910 us, min = 200.910 us, total = 200.910 us

-----------------
Task execution event stats:

Global stats: 17888 total (1 active)
Queueing time: mean = 53.149 us, max = 1.985 ms, min = -0.000 s, total = 950.729 ms
Execution time:  mean = 15.285 us, total = 273.418 ms
Event stats:
	CoreWorker.CheckSignal - 17887 total (1 active), Execution time: mean = 15.285 us, total = 273.411 ms, Queueing time: mean = 53.152 us, max = 1.985 ms, min = -0.000 s, total = 950.726 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.830 us, total = 7.830 us, Queueing time: mean = 2.811 us, max = 2.811 us, min = 2.811 us, total = 2.811 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 33.728 us, max = 113.709 us, min = 5.715 us, total = 18.247 ms
Execution time:  mean = 349.453 us, total = 189.054 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 792.093 us, total = 142.577 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 215.665 us, total = 38.820 ms, Queueing time: mean = 54.117 us, max = 113.709 us, min = 22.755 us, total = 9.741 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 41.995 us, total = 7.559 ms, Queueing time: mean = 47.224 us, max = 112.811 us, min = 11.632 us, total = 8.500 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.200 us, total = 98.200 us, Queueing time: mean = 5.715 us, max = 5.715 us, min = 5.715 us, total = 5.715 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,538 I 660933 661964] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 51.685 us, max = 543.656 us, min = -0.000 s, total = 178.314 ms
Execution time:  mean = 76.310 us, total = 263.269 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.407 us, total = 20.168 ms, Queueing time: mean = 57.260 us, max = 160.303 us, min = -0.000 s, total = 137.368 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.510 us, total = 2.522 ms, Queueing time: mean = 51.847 us, max = 122.902 us, min = 16.562 us, total = 12.443 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 30.343 us, total = 7.282 ms, Queueing time: mean = 45.714 us, max = 106.000 us, min = 12.643 us, total = 10.971 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 178.654 us, total = 42.877 ms, Queueing time: mean = 53.549 us, max = 105.093 us, min = 19.115 us, total = 12.852 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 769.874 us, total = 184.770 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 36.193 us, total = 1.737 ms, Queueing time: mean = 43.716 us, max = 82.305 us, min = 13.198 us, total = 2.098 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.234 us, total = 125.605 us, Queueing time: mean = 84.986 us, max = 543.656 us, min = 15.743 us, total = 2.040 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.329 us, total = 499.302 us, Queueing time: mean = 18.195 us, max = 70.825 us, min = 6.733 us, total = 127.366 us
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 512.975 us, total = 2.052 ms, Queueing time: mean = 49.953 us, max = 87.000 us, min = 43.801 us, total = 199.812 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 379.688 us, total = 379.688 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 28.891 us, total = 28.891 us, Queueing time: mean = 8.161 us, max = 8.161 us, min = 8.161 us, total = 8.161 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.752 us, total = 309.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 343.685 us, total = 343.685 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.922 us, total = 123.922 us, Queueing time: mean = 5.461 us, max = 5.461 us, min = 5.461 us, total = 5.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 49.904 us, total = 49.904 us, Queueing time: mean = 200.910 us, max = 200.910 us, min = 200.910 us, total = 200.910 us

-----------------
Task execution event stats:

Global stats: 23847 total (1 active)
Queueing time: mean = 54.636 us, max = 1.985 ms, min = -0.000 s, total = 1.303 s
Execution time:  mean = 15.265 us, total = 364.021 ms
Event stats:
	CoreWorker.CheckSignal - 23846 total (1 active), Execution time: mean = 15.265 us, total = 364.014 ms, Queueing time: mean = 54.638 us, max = 1.985 ms, min = -0.000 s, total = 1.303 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.830 us, total = 7.830 us, Queueing time: mean = 2.811 us, max = 2.811 us, min = 2.811 us, total = 2.811 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 34.109 us, max = 113.709 us, min = 5.715 us, total = 24.593 ms
Execution time:  mean = 349.975 us, total = 252.332 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 796.153 us, total = 191.077 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 213.449 us, total = 51.228 ms, Queueing time: mean = 55.036 us, max = 113.709 us, min = 22.755 us, total = 13.209 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 41.373 us, total = 9.930 ms, Queueing time: mean = 47.409 us, max = 112.811 us, min = 11.632 us, total = 11.378 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.200 us, total = 98.200 us, Queueing time: mean = 5.715 us, max = 5.715 us, min = 5.715 us, total = 5.715 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,539 I 660933 661964] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 52.285 us, max = 543.656 us, min = -0.000 s, total = 225.348 ms
Execution time:  mean = 76.091 us, total = 327.952 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.338 us, total = 25.006 ms, Queueing time: mean = 57.774 us, max = 160.303 us, min = -0.000 s, total = 173.263 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.470 us, total = 3.141 ms, Queueing time: mean = 53.446 us, max = 122.902 us, min = 16.562 us, total = 16.034 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.104 us, total = 9.031 ms, Queueing time: mean = 45.159 us, max = 106.000 us, min = 12.643 us, total = 13.548 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 177.845 us, total = 53.354 ms, Queueing time: mean = 54.373 us, max = 105.987 us, min = 16.170 us, total = 16.312 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 768.852 us, total = 230.656 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.618 us, total = 2.137 ms, Queueing time: mean = 45.967 us, max = 92.768 us, min = 13.198 us, total = 2.758 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.196 us, total = 155.888 us, Queueing time: mean = 92.854 us, max = 543.656 us, min = 15.743 us, total = 2.786 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.329 us, total = 499.302 us, Queueing time: mean = 18.195 us, max = 70.825 us, min = 6.733 us, total = 127.366 us
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 540.654 us, total = 2.703 ms, Queueing time: mean = 53.594 us, max = 87.000 us, min = 43.801 us, total = 267.970 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.264 us, total = 32.527 us, Queueing time: mean = 19.047 us, max = 38.094 us, min = 38.094 us, total = 38.094 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 379.688 us, total = 379.688 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 28.891 us, total = 28.891 us, Queueing time: mean = 8.161 us, max = 8.161 us, min = 8.161 us, total = 8.161 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.752 us, total = 309.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 343.685 us, total = 343.685 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.922 us, total = 123.922 us, Queueing time: mean = 5.461 us, max = 5.461 us, min = 5.461 us, total = 5.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 49.904 us, total = 49.904 us, Queueing time: mean = 200.910 us, max = 200.910 us, min = 200.910 us, total = 200.910 us

-----------------
Task execution event stats:

Global stats: 29806 total (1 active)
Queueing time: mean = 55.458 us, max = 1.985 ms, min = -0.000 s, total = 1.653 s
Execution time:  mean = 15.233 us, total = 454.027 ms
Event stats:
	CoreWorker.CheckSignal - 29805 total (1 active), Execution time: mean = 15.233 us, total = 454.019 ms, Queueing time: mean = 55.460 us, max = 1.985 ms, min = -0.000 s, total = 1.653 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.830 us, total = 7.830 us, Queueing time: mean = 2.811 us, max = 2.811 us, min = 2.811 us, total = 2.811 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 34.651 us, max = 130.697 us, min = 5.715 us, total = 31.220 ms
Execution time:  mean = 349.659 us, total = 315.042 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 794.569 us, total = 238.371 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 214.307 us, total = 64.292 ms, Queueing time: mean = 56.579 us, max = 130.697 us, min = 22.755 us, total = 16.974 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 40.938 us, total = 12.281 ms, Queueing time: mean = 47.470 us, max = 112.811 us, min = 11.632 us, total = 14.241 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.200 us, total = 98.200 us, Queueing time: mean = 5.715 us, max = 5.715 us, min = 5.715 us, total = 5.715 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,540 I 660933 661964] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 52.364 us, max = 543.656 us, min = -0.000 s, total = 270.616 ms
Execution time:  mean = 76.237 us, total = 393.993 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.416 us, total = 30.280 ms, Queueing time: mean = 57.605 us, max = 160.303 us, min = -0.000 s, total = 207.261 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.778 us, total = 3.880 ms, Queueing time: mean = 54.456 us, max = 122.902 us, min = 12.798 us, total = 19.604 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.408 us, total = 10.947 ms, Queueing time: mean = 44.951 us, max = 106.000 us, min = 12.643 us, total = 16.182 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 177.747 us, total = 63.989 ms, Queueing time: mean = 54.970 us, max = 105.987 us, min = 16.170 us, total = 19.789 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 769.587 us, total = 277.051 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.178 us, total = 2.533 ms, Queueing time: mean = 47.862 us, max = 92.768 us, min = 13.198 us, total = 3.446 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.277 us, total = 189.962 us, Queueing time: mean = 100.093 us, max = 543.656 us, min = 15.743 us, total = 3.603 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 71.329 us, total = 499.302 us, Queueing time: mean = 18.195 us, max = 70.825 us, min = 6.733 us, total = 127.366 us
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 559.189 us, total = 3.355 ms, Queueing time: mean = 58.346 us, max = 87.000 us, min = 43.801 us, total = 350.073 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.264 us, total = 32.527 us, Queueing time: mean = 19.047 us, max = 38.094 us, min = 38.094 us, total = 38.094 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 379.688 us, total = 379.688 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 28.891 us, total = 28.891 us, Queueing time: mean = 8.161 us, max = 8.161 us, min = 8.161 us, total = 8.161 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.752 us, total = 309.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 343.685 us, total = 343.685 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.922 us, total = 123.922 us, Queueing time: mean = 5.461 us, max = 5.461 us, min = 5.461 us, total = 5.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 49.904 us, total = 49.904 us, Queueing time: mean = 200.910 us, max = 200.910 us, min = 200.910 us, total = 200.910 us

-----------------
Task execution event stats:

Global stats: 35767 total (1 active)
Queueing time: mean = 55.599 us, max = 1.985 ms, min = -0.000 s, total = 1.989 s
Execution time:  mean = 15.204 us, total = 543.794 ms
Event stats:
	CoreWorker.CheckSignal - 35766 total (1 active), Execution time: mean = 15.204 us, total = 543.786 ms, Queueing time: mean = 55.601 us, max = 1.985 ms, min = -0.000 s, total = 1.989 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.830 us, total = 7.830 us, Queueing time: mean = 2.811 us, max = 2.811 us, min = 2.811 us, total = 2.811 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 34.523 us, max = 130.697 us, min = 5.715 us, total = 37.320 ms
Execution time:  mean = 348.490 us, total = 376.718 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 792.857 us, total = 285.428 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 212.974 us, total = 76.670 ms, Queueing time: mean = 56.583 us, max = 130.697 us, min = 11.758 us, total = 20.370 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 40.335 us, total = 14.521 ms, Queueing time: mean = 47.067 us, max = 112.811 us, min = 11.632 us, total = 16.944 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.200 us, total = 98.200 us, Queueing time: mean = 5.715 us, max = 5.715 us, min = 5.715 us, total = 5.715 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660933 661964] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660933 661964] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660933 661964] core_worker.cc:5107: Number of alive nodes:0
