[2025-07-05 18:42:59,005 I 660616 660616] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660616
[2025-07-05 18:42:59,009 I 660616 660616] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,728 I 660616 660616] grpc_server.cc:141: driver server started, listening on port 33441.
[2025-07-05 18:42:59,730 I 660616 660616] core_worker.cc:542: Initializing worker at address: ***********:33441 worker_id=01000000ffffffffffffffffffffffffffffffffffffffffffffffff node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,731 I 660616 660616] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-07-05 18:42:59,732 I 660616 660925] core_worker.cc:902: Event stats:


Global stats: 13 total (8 active)
Queueing time: mean = 1.881 us, max = 11.272 us, min = 4.877 us, total = 24.447 us
Execution time:  mean = 77.870 us, total = 1.012 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 6 total (5 active, 1 running), Execution time: mean = 2.022 us, total = 12.130 us, Queueing time: mean = 812.833 ns, max = 4.877 us, min = 4.877 us, total = 4.877 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.812 us, total = 27.812 us, Queueing time: mean = 11.272 us, max = 11.272 us, min = 11.272 us, total = 11.272 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 392.169 us, total = 392.169 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 466.640 us, total = 466.640 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.557 us, total = 113.557 us, Queueing time: mean = 8.298 us, max = 8.298 us, min = 8.298 us, total = 8.298 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.833 us, max = 8.951 us, min = 6.380 us, total = 15.331 us
Execution time:  mean = 143.136 us, total = 572.543 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 449.013 us, total = 449.013 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.923 us, total = 103.923 us, Queueing time: mean = 6.380 us, max = 6.380 us, min = 6.380 us, total = 6.380 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.607 us, total = 19.607 us, Queueing time: mean = 8.951 us, max = 8.951 us, min = 8.951 us, total = 8.951 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 1
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,732 I 660616 660925] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,732 I 660616 660925] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,732 I 660616 660616] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,732 I 660616 660616] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,732 I 660616 660616] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,852 I 660616 660616] actor_task_submitter.cc:73: Set actor max pending calls to -1 actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:42:59,864 I 660616 660616] actor_task_submitter.cc:73: Set actor max pending calls to -1 actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:42:59,868 I 660616 660925] actor_manager.cc:218: received notification on actor, state: PENDING_CREATION, ip address: ***********, port: 37723, num_restarts: 0, death context type=CONTEXT_NOT_SET actor_id=c4dbba14b481690e7680c72d01000000 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:43:09,021 W 660616 660920] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,732 I 660616 660925] core_worker.cc:902: Event stats:


Global stats: 831 total (10 active)
Queueing time: mean = 49.824 us, max = 1.046 ms, min = 4.877 us, total = 41.404 ms
Execution time:  mean = 159.691 us, total = 132.703 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.894 us, total = 5.336 ms, Queueing time: mean = 52.239 us, max = 119.170 us, min = 16.637 us, total = 31.343 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 29.973 us, total = 1.798 ms, Queueing time: mean = 42.405 us, max = 77.227 us, min = 8.627 us, total = 2.544 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 178.514 us, total = 10.711 ms, Queueing time: mean = 67.819 us, max = 1.046 ms, min = 20.822 us, total = 4.069 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 745.349 us, total = 44.721 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 188.213 us, total = 2.259 ms, Queueing time: mean = 42.689 us, max = 76.088 us, min = 16.430 us, total = 512.268 us
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.646 us, total = 27.874 us, Queueing time: mean = 47.392 us, max = 76.078 us, min = 21.675 us, total = 284.352 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 83.778 us, total = 502.669 us, Queueing time: mean = 315.486 us, max = 579.340 us, min = 4.877 us, total = 1.893 ms
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 2 total (0 active), Execution time: mean = 277.954 us, total = 555.907 us, Queueing time: mean = 75.145 us, max = 100.564 us, min = 49.727 us, total = 150.291 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 2 total (0 active), Execution time: mean = 1.501 ms, total = 3.002 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorCreator.AsyncRegisterActor - 2 total (0 active), Execution time: mean = 283.777 us, total = 567.554 us, Queueing time: mean = 54.983 us, max = 57.921 us, min = 52.046 us, total = 109.967 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 642.680 us, total = 1.285 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.WaitForActorRefDeleted.HandleRequestImpl - 2 total (0 active), Execution time: mean = 22.317 us, total = 44.634 us, Queueing time: mean = 129.512 us, max = 238.558 us, min = 20.467 us, total = 259.025 us
	CoreWorkerService.grpc_server.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 171.709 us, total = 343.419 us, Queueing time: mean = 32.203 us, max = 56.107 us, min = 8.298 us, total = 64.405 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 2 total (1 active), Execution time: mean = 29.035 ms, total = 58.071 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 53.710 us, total = 53.710 us, Queueing time: mean = 11.428 us, max = 11.428 us, min = 11.428 us, total = 11.428 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 392.169 us, total = 392.169 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.812 us, total = 27.812 us, Queueing time: mean = 11.272 us, max = 11.272 us, min = 11.272 us, total = 11.272 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 94.427 us, total = 94.427 us, Queueing time: mean = 52.624 us, max = 52.624 us, min = 52.624 us, total = 52.624 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.402 ms, total = 1.402 ms, Queueing time: mean = 54.881 us, max = 54.881 us, min = 54.881 us, total = 54.881 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 862.659 us, total = 862.659 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorTaskSubmitter::SubmitTask - 1 total (0 active), Execution time: mean = 12.800 us, total = 12.800 us, Queueing time: mean = 43.442 us, max = 43.442 us, min = 43.442 us, total = 43.442 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 633.494 us, total = 633.494 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 34.291 us, max = 94.852 us, min = 6.380 us, total = 6.207 ms
Execution time:  mean = 346.211 us, total = 62.664 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 221.709 us, total = 13.303 ms, Queueing time: mean = 62.744 us, max = 94.852 us, min = 26.080 us, total = 3.765 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 782.856 us, total = 46.971 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 38.106 us, total = 2.286 ms, Queueing time: mean = 40.594 us, max = 61.834 us, min = 8.951 us, total = 2.436 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.923 us, total = 103.923 us, Queueing time: mean = 6.380 us, max = 6.380 us, min = 6.380 us, total = 6.380 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000904083 MiB
	total number of task attempts sent: 4
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,733 I 660616 660925] core_worker.cc:902: Event stats:


Global stats: 1630 total (10 active)
Queueing time: mean = 47.917 us, max = 1.046 ms, min = 4.877 us, total = 78.104 ms
Execution time:  mean = 122.425 us, total = 199.553 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.753 us, total = 10.504 ms, Queueing time: mean = 51.030 us, max = 134.487 us, min = 14.173 us, total = 61.236 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 30.532 us, total = 3.664 ms, Queueing time: mean = 42.549 us, max = 95.817 us, min = 8.627 us, total = 5.106 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 181.362 us, total = 21.763 ms, Queueing time: mean = 58.796 us, max = 1.046 ms, min = 16.685 us, total = 7.055 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 767.686 us, total = 92.122 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 114.519 us, total = 2.748 ms, Queueing time: mean = 48.065 us, max = 76.088 us, min = 16.430 us, total = 1.154 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.976 us, total = 59.709 us, Queueing time: mean = 72.467 us, max = 316.760 us, min = 21.675 us, total = 869.604 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 83.778 us, total = 502.669 us, Queueing time: mean = 315.486 us, max = 579.340 us, min = 4.877 us, total = 1.893 ms
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 2 total (0 active), Execution time: mean = 277.954 us, total = 555.907 us, Queueing time: mean = 75.145 us, max = 100.564 us, min = 49.727 us, total = 150.291 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 2 total (0 active), Execution time: mean = 1.501 ms, total = 3.002 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorCreator.AsyncRegisterActor - 2 total (0 active), Execution time: mean = 283.777 us, total = 567.554 us, Queueing time: mean = 54.983 us, max = 57.921 us, min = 52.046 us, total = 109.967 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 420.724 us, total = 841.449 us, Queueing time: mean = 17.059 us, max = 34.119 us, min = 34.119 us, total = 34.119 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 642.680 us, total = 1.285 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.WaitForActorRefDeleted.HandleRequestImpl - 2 total (0 active), Execution time: mean = 22.317 us, total = 44.634 us, Queueing time: mean = 129.512 us, max = 238.558 us, min = 20.467 us, total = 259.025 us
	CoreWorkerService.grpc_server.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 171.709 us, total = 343.419 us, Queueing time: mean = 32.203 us, max = 56.107 us, min = 8.298 us, total = 64.405 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 2 total (1 active), Execution time: mean = 29.035 ms, total = 58.071 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 392.169 us, total = 392.169 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 53.710 us, total = 53.710 us, Queueing time: mean = 11.428 us, max = 11.428 us, min = 11.428 us, total = 11.428 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.812 us, total = 27.812 us, Queueing time: mean = 11.272 us, max = 11.272 us, min = 11.272 us, total = 11.272 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 94.427 us, total = 94.427 us, Queueing time: mean = 52.624 us, max = 52.624 us, min = 52.624 us, total = 52.624 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.402 ms, total = 1.402 ms, Queueing time: mean = 54.881 us, max = 54.881 us, min = 54.881 us, total = 54.881 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 862.659 us, total = 862.659 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorTaskSubmitter::SubmitTask - 1 total (0 active), Execution time: mean = 12.800 us, total = 12.800 us, Queueing time: mean = 43.442 us, max = 43.442 us, min = 43.442 us, total = 43.442 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 633.494 us, total = 633.494 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 34.201 us, max = 102.058 us, min = 6.380 us, total = 12.347 ms
Execution time:  mean = 345.244 us, total = 124.633 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 218.867 us, total = 26.264 ms, Queueing time: mean = 63.259 us, max = 102.058 us, min = 23.172 us, total = 7.591 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 780.402 us, total = 93.648 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 38.474 us, total = 4.617 ms, Queueing time: mean = 39.577 us, max = 64.573 us, min = 8.951 us, total = 4.749 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.923 us, total = 103.923 us, Queueing time: mean = 6.380 us, max = 6.380 us, min = 6.380 us, total = 6.380 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000904083 MiB
	total number of task attempts sent: 4
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,734 I 660616 660925] core_worker.cc:902: Event stats:


Global stats: 2429 total (10 active)
Queueing time: mean = 46.496 us, max = 1.046 ms, min = 4.877 us, total = 112.938 ms
Execution time:  mean = 109.625 us, total = 266.278 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.793 us, total = 15.827 ms, Queueing time: mean = 49.516 us, max = 134.487 us, min = 10.922 us, total = 89.129 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 31.068 us, total = 5.592 ms, Queueing time: mean = 42.183 us, max = 105.597 us, min = 8.627 us, total = 7.593 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 182.620 us, total = 32.872 ms, Queueing time: mean = 55.020 us, max = 1.046 ms, min = 16.685 us, total = 9.904 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 773.047 us, total = 139.148 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 89.871 us, total = 3.235 ms, Queueing time: mean = 51.162 us, max = 84.293 us, min = 16.430 us, total = 1.842 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.119 us, total = 92.139 us, Queueing time: mean = 95.350 us, max = 595.306 us, min = 21.675 us, total = 1.716 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 83.778 us, total = 502.669 us, Queueing time: mean = 315.486 us, max = 579.340 us, min = 4.877 us, total = 1.893 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 553.538 us, total = 1.661 ms, Queueing time: mean = 34.784 us, max = 70.234 us, min = 34.119 us, total = 104.353 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 2 total (0 active), Execution time: mean = 277.954 us, total = 555.907 us, Queueing time: mean = 75.145 us, max = 100.564 us, min = 49.727 us, total = 150.291 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 2 total (0 active), Execution time: mean = 1.501 ms, total = 3.002 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorCreator.AsyncRegisterActor - 2 total (0 active), Execution time: mean = 283.777 us, total = 567.554 us, Queueing time: mean = 54.983 us, max = 57.921 us, min = 52.046 us, total = 109.967 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 642.680 us, total = 1.285 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.WaitForActorRefDeleted.HandleRequestImpl - 2 total (0 active), Execution time: mean = 22.317 us, total = 44.634 us, Queueing time: mean = 129.512 us, max = 238.558 us, min = 20.467 us, total = 259.025 us
	CoreWorkerService.grpc_server.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 171.709 us, total = 343.419 us, Queueing time: mean = 32.203 us, max = 56.107 us, min = 8.298 us, total = 64.405 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 2 total (1 active), Execution time: mean = 29.035 ms, total = 58.071 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 392.169 us, total = 392.169 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 53.710 us, total = 53.710 us, Queueing time: mean = 11.428 us, max = 11.428 us, min = 11.428 us, total = 11.428 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.812 us, total = 27.812 us, Queueing time: mean = 11.272 us, max = 11.272 us, min = 11.272 us, total = 11.272 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 94.427 us, total = 94.427 us, Queueing time: mean = 52.624 us, max = 52.624 us, min = 52.624 us, total = 52.624 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.402 ms, total = 1.402 ms, Queueing time: mean = 54.881 us, max = 54.881 us, min = 54.881 us, total = 54.881 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 862.659 us, total = 862.659 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorTaskSubmitter::SubmitTask - 1 total (0 active), Execution time: mean = 12.800 us, total = 12.800 us, Queueing time: mean = 43.442 us, max = 43.442 us, min = 43.442 us, total = 43.442 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 633.494 us, total = 633.494 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 35.185 us, max = 102.058 us, min = 6.380 us, total = 19.035 ms
Execution time:  mean = 344.695 us, total = 186.480 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 217.287 us, total = 39.112 ms, Queueing time: mean = 64.374 us, max = 102.058 us, min = 23.172 us, total = 11.587 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 779.772 us, total = 140.359 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 38.364 us, total = 6.905 ms, Queueing time: mean = 41.340 us, max = 64.573 us, min = 8.951 us, total = 7.441 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.923 us, total = 103.923 us, Queueing time: mean = 6.380 us, max = 6.380 us, min = 6.380 us, total = 6.380 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000904083 MiB
	total number of task attempts sent: 4
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,735 I 660616 660925] core_worker.cc:902: Event stats:


Global stats: 3227 total (10 active)
Queueing time: mean = 46.739 us, max = 1.046 ms, min = 4.877 us, total = 150.826 ms
Execution time:  mean = 103.176 us, total = 332.948 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.760 us, total = 21.016 ms, Queueing time: mean = 49.967 us, max = 134.487 us, min = 10.922 us, total = 119.870 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 31.339 us, total = 7.521 ms, Queueing time: mean = 42.197 us, max = 105.597 us, min = 8.627 us, total = 10.127 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 182.548 us, total = 43.811 ms, Queueing time: mean = 54.591 us, max = 1.046 ms, min = 16.685 us, total = 13.102 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 776.760 us, total = 186.422 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 77.327 us, total = 3.712 ms, Queueing time: mean = 49.434 us, max = 84.293 us, min = 16.430 us, total = 2.373 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.430 us, total = 130.315 us, Queueing time: mean = 105.446 us, max = 614.359 us, min = 18.263 us, total = 2.531 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 83.778 us, total = 502.669 us, Queueing time: mean = 315.486 us, max = 579.340 us, min = 4.877 us, total = 1.893 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 621.234 us, total = 2.485 ms, Queueing time: mean = 43.231 us, max = 70.234 us, min = 34.119 us, total = 172.925 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 2 total (0 active), Execution time: mean = 277.954 us, total = 555.907 us, Queueing time: mean = 75.145 us, max = 100.564 us, min = 49.727 us, total = 150.291 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 2 total (0 active), Execution time: mean = 1.501 ms, total = 3.002 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorCreator.AsyncRegisterActor - 2 total (0 active), Execution time: mean = 283.777 us, total = 567.554 us, Queueing time: mean = 54.983 us, max = 57.921 us, min = 52.046 us, total = 109.967 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 642.680 us, total = 1.285 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.WaitForActorRefDeleted.HandleRequestImpl - 2 total (0 active), Execution time: mean = 22.317 us, total = 44.634 us, Queueing time: mean = 129.512 us, max = 238.558 us, min = 20.467 us, total = 259.025 us
	CoreWorkerService.grpc_server.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 171.709 us, total = 343.419 us, Queueing time: mean = 32.203 us, max = 56.107 us, min = 8.298 us, total = 64.405 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 2 total (1 active), Execution time: mean = 29.035 ms, total = 58.071 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 392.169 us, total = 392.169 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 53.710 us, total = 53.710 us, Queueing time: mean = 11.428 us, max = 11.428 us, min = 11.428 us, total = 11.428 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.812 us, total = 27.812 us, Queueing time: mean = 11.272 us, max = 11.272 us, min = 11.272 us, total = 11.272 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 94.427 us, total = 94.427 us, Queueing time: mean = 52.624 us, max = 52.624 us, min = 52.624 us, total = 52.624 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.402 ms, total = 1.402 ms, Queueing time: mean = 54.881 us, max = 54.881 us, min = 54.881 us, total = 54.881 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 862.659 us, total = 862.659 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorTaskSubmitter::SubmitTask - 1 total (0 active), Execution time: mean = 12.800 us, total = 12.800 us, Queueing time: mean = 43.442 us, max = 43.442 us, min = 43.442 us, total = 43.442 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 633.494 us, total = 633.494 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 35.399 us, max = 113.997 us, min = 6.380 us, total = 25.523 ms
Execution time:  mean = 345.883 us, total = 249.381 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 216.107 us, total = 51.866 ms, Queueing time: mean = 64.875 us, max = 113.997 us, min = 23.172 us, total = 15.570 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 784.052 us, total = 188.172 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 38.498 us, total = 9.239 ms, Queueing time: mean = 41.444 us, max = 64.573 us, min = 8.951 us, total = 9.947 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.923 us, total = 103.923 us, Queueing time: mean = 6.380 us, max = 6.380 us, min = 6.380 us, total = 6.380 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000904083 MiB
	total number of task attempts sent: 4
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,736 I 660616 660925] core_worker.cc:902: Event stats:


Global stats: 4027 total (10 active)
Queueing time: mean = 46.969 us, max = 1.046 ms, min = 4.877 us, total = 189.143 ms
Execution time:  mean = 99.749 us, total = 401.689 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.792 us, total = 26.367 ms, Queueing time: mean = 50.188 us, max = 134.487 us, min = 10.922 us, total = 150.513 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 31.886 us, total = 9.566 ms, Queueing time: mean = 43.422 us, max = 105.597 us, min = 8.627 us, total = 13.026 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 183.672 us, total = 55.102 ms, Queueing time: mean = 54.088 us, max = 1.046 ms, min = 16.685 us, total = 16.226 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 783.724 us, total = 235.117 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 70.094 us, total = 4.206 ms, Queueing time: mean = 49.050 us, max = 84.293 us, min = 16.430 us, total = 2.943 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.420 us, total = 162.606 us, Queueing time: mean = 115.418 us, max = 671.927 us, min = 18.263 us, total = 3.463 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 83.778 us, total = 502.669 us, Queueing time: mean = 315.486 us, max = 579.340 us, min = 4.877 us, total = 1.893 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 662.563 us, total = 3.313 ms, Queueing time: mean = 49.836 us, max = 76.255 us, min = 34.119 us, total = 249.180 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 2 total (0 active), Execution time: mean = 277.954 us, total = 555.907 us, Queueing time: mean = 75.145 us, max = 100.564 us, min = 49.727 us, total = 150.291 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 2 total (0 active), Execution time: mean = 1.501 ms, total = 3.002 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorCreator.AsyncRegisterActor - 2 total (0 active), Execution time: mean = 283.777 us, total = 567.554 us, Queueing time: mean = 54.983 us, max = 57.921 us, min = 52.046 us, total = 109.967 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.958 us, total = 5.917 us, Queueing time: mean = 36.087 us, max = 72.174 us, min = 72.174 us, total = 72.174 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 642.680 us, total = 1.285 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.WaitForActorRefDeleted.HandleRequestImpl - 2 total (0 active), Execution time: mean = 22.317 us, total = 44.634 us, Queueing time: mean = 129.512 us, max = 238.558 us, min = 20.467 us, total = 259.025 us
	CoreWorkerService.grpc_server.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 171.709 us, total = 343.419 us, Queueing time: mean = 32.203 us, max = 56.107 us, min = 8.298 us, total = 64.405 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 2 total (1 active), Execution time: mean = 29.035 ms, total = 58.071 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 392.169 us, total = 392.169 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 53.710 us, total = 53.710 us, Queueing time: mean = 11.428 us, max = 11.428 us, min = 11.428 us, total = 11.428 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 94.427 us, total = 94.427 us, Queueing time: mean = 52.624 us, max = 52.624 us, min = 52.624 us, total = 52.624 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.812 us, total = 27.812 us, Queueing time: mean = 11.272 us, max = 11.272 us, min = 11.272 us, total = 11.272 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.402 ms, total = 1.402 ms, Queueing time: mean = 54.881 us, max = 54.881 us, min = 54.881 us, total = 54.881 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 862.659 us, total = 862.659 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorTaskSubmitter::SubmitTask - 1 total (0 active), Execution time: mean = 12.800 us, total = 12.800 us, Queueing time: mean = 43.442 us, max = 43.442 us, min = 43.442 us, total = 43.442 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 633.494 us, total = 633.494 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 35.482 us, max = 121.032 us, min = 6.380 us, total = 31.970 ms
Execution time:  mean = 347.239 us, total = 312.862 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 215.643 us, total = 64.693 ms, Queueing time: mean = 64.265 us, max = 121.032 us, min = 23.172 us, total = 19.279 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 787.876 us, total = 236.363 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 39.009 us, total = 11.703 ms, Queueing time: mean = 42.279 us, max = 115.581 us, min = 8.951 us, total = 12.684 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.923 us, total = 103.923 us, Queueing time: mean = 6.380 us, max = 6.380 us, min = 6.380 us, total = 6.380 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000904083 MiB
	total number of task attempts sent: 4
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,737 I 660616 660925] core_worker.cc:902: Event stats:


Global stats: 4826 total (10 active)
Queueing time: mean = 48.413 us, max = 1.046 ms, min = 4.877 us, total = 233.643 ms
Execution time:  mean = 97.356 us, total = 469.842 ms
Event stats:
	CoreWorker.RecoverObjects - 3599 total (1 active), Execution time: mean = 8.836 us, total = 31.800 ms, Queueing time: mean = 51.977 us, max = 134.487 us, min = 10.922 us, total = 187.064 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 40.695 us, total = 14.650 ms, Queueing time: mean = 43.816 us, max = 105.597 us, min = 8.627 us, total = 15.774 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 183.108 us, total = 65.919 ms, Queueing time: mean = 54.192 us, max = 1.046 ms, min = 16.685 us, total = 19.509 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 779.197 us, total = 280.511 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 65.754 us, total = 4.734 ms, Queueing time: mean = 52.556 us, max = 100.875 us, min = 16.430 us, total = 3.784 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.451 us, total = 196.223 us, Queueing time: mean = 124.211 us, max = 671.927 us, min = 18.263 us, total = 4.472 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 695.718 us, total = 4.174 ms, Queueing time: mean = 52.979 us, max = 76.255 us, min = 34.119 us, total = 317.876 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 83.778 us, total = 502.669 us, Queueing time: mean = 315.486 us, max = 579.340 us, min = 4.877 us, total = 1.893 ms
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 2 total (0 active), Execution time: mean = 277.954 us, total = 555.907 us, Queueing time: mean = 75.145 us, max = 100.564 us, min = 49.727 us, total = 150.291 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 2 total (0 active), Execution time: mean = 1.501 ms, total = 3.002 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorCreator.AsyncRegisterActor - 2 total (0 active), Execution time: mean = 283.777 us, total = 567.554 us, Queueing time: mean = 54.983 us, max = 57.921 us, min = 52.046 us, total = 109.967 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 2.958 us, total = 5.917 us, Queueing time: mean = 36.087 us, max = 72.174 us, min = 72.174 us, total = 72.174 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 642.680 us, total = 1.285 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.WaitForActorRefDeleted.HandleRequestImpl - 2 total (0 active), Execution time: mean = 22.317 us, total = 44.634 us, Queueing time: mean = 129.512 us, max = 238.558 us, min = 20.467 us, total = 259.025 us
	CoreWorkerService.grpc_server.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 171.709 us, total = 343.419 us, Queueing time: mean = 32.203 us, max = 56.107 us, min = 8.298 us, total = 64.405 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 2 total (1 active), Execution time: mean = 29.035 ms, total = 58.071 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 392.169 us, total = 392.169 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 53.710 us, total = 53.710 us, Queueing time: mean = 11.428 us, max = 11.428 us, min = 11.428 us, total = 11.428 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 94.427 us, total = 94.427 us, Queueing time: mean = 52.624 us, max = 52.624 us, min = 52.624 us, total = 52.624 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.812 us, total = 27.812 us, Queueing time: mean = 11.272 us, max = 11.272 us, min = 11.272 us, total = 11.272 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.402 ms, total = 1.402 ms, Queueing time: mean = 54.881 us, max = 54.881 us, min = 54.881 us, total = 54.881 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 862.659 us, total = 862.659 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorTaskSubmitter::SubmitTask - 1 total (0 active), Execution time: mean = 12.800 us, total = 12.800 us, Queueing time: mean = 43.442 us, max = 43.442 us, min = 43.442 us, total = 43.442 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 633.494 us, total = 633.494 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 35.676 us, max = 121.032 us, min = 6.380 us, total = 38.566 ms
Execution time:  mean = 346.448 us, total = 374.510 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 215.706 us, total = 77.654 ms, Queueing time: mean = 63.837 us, max = 121.032 us, min = 23.172 us, total = 22.981 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 785.105 us, total = 282.638 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 39.207 us, total = 14.115 ms, Queueing time: mean = 43.272 us, max = 115.581 us, min = 8.951 us, total = 15.578 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.923 us, total = 103.923 us, Queueing time: mean = 6.380 us, max = 6.380 us, min = 6.380 us, total = 6.380 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000904083 MiB
	total number of task attempts sent: 4
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:07,817 I 660616 660925] actor_manager.cc:218: received notification on actor, state: ALIVE, ip address: ***********, port: 37723, num_restarts: 0, death context type=CONTEXT_NOT_SET actor_id=c4dbba14b481690e7680c72d01000000 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:07,825 I 660616 660925] actor_manager.cc:218: received notification on actor, state: ALIVE, ip address: ***********, port: 41221, num_restarts: 0, death context type=CONTEXT_NOT_SET actor_id=3e5877903265ed7cd75303e201000000 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,659 I 660616 660616] core_worker.cc:1088: Sending disconnect message to the local raylet.
[2025-07-05 18:49:08,659 I 660616 660616] raylet_client.cc:73: RayletClient::Disconnect, exit_type=INTENDED_USER_EXIT, exit_detail=Shutdown by ray.shutdown()., has creation_task_exception_pb_bytes=0
[2025-07-05 18:49:08,660 I 660616 660616] core_worker.cc:1094: Disconnected from the local raylet.
[2025-07-05 18:49:08,660 I 660616 660616] core_worker.cc:1006: Shutting down.
[2025-07-05 18:49:08,660 I 660616 660616] task_event_buffer.cc:298: Shutting down TaskEventBuffer.
[2025-07-05 18:49:08,660 I 660616 662309] task_event_buffer.cc:266: Task event buffer io service stopped.
[2025-07-05 18:49:08,660 I 660616 660616] core_worker.cc:1028: Waiting for joining a core worker io thread. If it hangs here, there might be deadlock or a high load in the core worker io service.
[2025-07-05 18:49:08,660 I 660616 660925] core_worker.cc:1280: Core worker main io service stopped.
[2025-07-05 18:49:08,664 I 660616 660616] core_worker.cc:1040: Disconnecting a GCS client.
[2025-07-05 18:49:08,664 I 660616 660616] core_worker.cc:1047: Core worker ready to be deallocated.
[2025-07-05 18:49:08,664 I 660616 660616] core_worker.cc:997: Core worker is destructed
[2025-07-05 18:49:08,664 I 660616 660616] task_event_buffer.cc:298: Shutting down TaskEventBuffer.
[2025-07-05 18:49:08,666 I 660616 660616] core_worker_process.cc:237: Destructing CoreWorkerProcessImpl. pid: 660616
[2025-07-05 18:49:08,666 I 660616 660616] io_service_pool.cc:48: IOServicePool is stopped.
[2025-07-05 18:49:08,781 I 660616 660616] stats.h:120: Stats module has shutdown.
