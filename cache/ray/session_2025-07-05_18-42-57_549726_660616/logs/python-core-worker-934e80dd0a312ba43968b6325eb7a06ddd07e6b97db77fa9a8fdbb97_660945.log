[2025-07-05 18:42:59,696 I 660945 660945] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660945
[2025-07-05 18:42:59,699 I 660945 660945] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,701 I 660945 660945] grpc_server.cc:141: worker server started, listening on port 34865.
[2025-07-05 18:42:59,703 I 660945 660945] core_worker.cc:542: Initializing worker at address: ***********:34865 worker_id=934e80dd0a312ba43968b6325eb7a06ddd07e6b97db77fa9a8fdbb97 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,704 I 660945 660945] task_event_buffer.cc:287: Reporting task events to <PERSON><PERSON> every 1000ms.
[2025-07-05 18:42:59,704 I 660945 660945] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,704 I 660945 660945] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,704 I 660945 662150] core_worker.cc:902: Event stats:


Global stats: 9 total (5 active)
Queueing time: mean = 11.804 us, max = 78.073 us, min = 11.155 us, total = 106.240 us
Execution time:  mean = 53.569 us, total = 482.125 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 7.418 us, total = 22.254 us, Queueing time: mean = 31.695 us, max = 78.073 us, min = 17.012 us, total = 95.085 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 439.752 us, total = 439.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.119 us, total = 20.119 us, Queueing time: mean = 11.155 us, max = 11.155 us, min = 11.155 us, total = 11.155 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.761 us, max = 9.727 us, min = 5.319 us, total = 15.046 us
Execution time:  mean = 159.077 us, total = 636.307 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 102.020 us, total = 102.020 us, Queueing time: mean = 5.319 us, max = 5.319 us, min = 5.319 us, total = 5.319 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 518.502 us, total = 518.502 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.785 us, total = 15.785 us, Queueing time: mean = 9.727 us, max = 9.727 us, min = 9.727 us, total = 9.727 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,704 I 660945 660945] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,704 I 660945 660945] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,705 I 660945 662150] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,705 I 660945 662150] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,711 W 660945 662143] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,705 I 660945 662150] core_worker.cc:902: Event stats:


Global stats: 874 total (8 active)
Queueing time: mean = 54.405 us, max = 474.702 us, min = 7.082 us, total = 47.550 ms
Execution time:  mean = 75.839 us, total = 66.283 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.713 us, total = 5.228 ms, Queueing time: mean = 58.194 us, max = 104.451 us, min = 16.623 us, total = 34.916 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 174.063 us, total = 10.444 ms, Queueing time: mean = 57.223 us, max = 87.820 us, min = 20.832 us, total = 3.433 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 29.462 us, total = 1.768 ms, Queueing time: mean = 43.416 us, max = 66.447 us, min = 8.588 us, total = 2.605 ms
	CoreWorker.ExitIfParentRayletDies - 60 total (1 active), Execution time: mean = 10.422 us, total = 625.313 us, Queueing time: mean = 63.482 us, max = 99.783 us, min = 22.834 us, total = 3.809 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 764.899 us, total = 45.894 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 36.632 us, total = 439.587 us, Queueing time: mean = 42.235 us, max = 76.738 us, min = 15.707 us, total = 506.820 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.035 us, total = 504.244 us, Queueing time: mean = 206.949 us, max = 474.702 us, min = 7.082 us, total = 1.449 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.253 us, total = 31.518 us, Queueing time: mean = 58.219 us, max = 75.250 us, min = 60.527 us, total = 349.314 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 278.472 us, total = 278.472 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.243 us, total = 104.243 us, Queueing time: mean = 462.343 us, max = 462.343 us, min = 462.343 us, total = 462.343 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 439.752 us, total = 439.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 452.355 us, total = 452.355 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.540 us, total = 54.540 us, Queueing time: mean = 8.193 us, max = 8.193 us, min = 8.193 us, total = 8.193 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.119 us, total = 20.119 us, Queueing time: mean = 11.155 us, max = 11.155 us, min = 11.155 us, total = 11.155 us

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.005 us, max = 3.866 ms, min = 2.594 us, total = 322.031 ms
Execution time:  mean = 15.622 us, total = 93.156 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 15.624 us, total = 93.148 ms, Queueing time: mean = 54.014 us, max = 3.866 ms, min = 8.884 us, total = 322.029 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.599 us, total = 8.599 us, Queueing time: mean = 2.594 us, max = 2.594 us, min = 2.594 us, total = 2.594 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 34.371 us, max = 104.306 us, min = 5.319 us, total = 6.221 ms
Execution time:  mean = 327.195 us, total = 59.222 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 752.992 us, total = 45.180 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 33.914 us, total = 2.035 ms, Queueing time: mean = 42.588 us, max = 62.830 us, min = 9.727 us, total = 2.555 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 198.434 us, total = 11.906 ms, Queueing time: mean = 61.009 us, max = 104.306 us, min = 23.739 us, total = 3.661 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 102.020 us, total = 102.020 us, Queueing time: mean = 5.319 us, max = 5.319 us, min = 5.319 us, total = 5.319 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,706 I 660945 662150] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 51.348 us, max = 775.023 us, min = 7.082 us, total = 88.985 ms
Execution time:  mean = 77.691 us, total = 134.639 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.837 us, total = 10.604 ms, Queueing time: mean = 54.590 us, max = 153.062 us, min = 15.345 us, total = 65.508 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 183.436 us, total = 22.012 ms, Queueing time: mean = 57.130 us, max = 108.485 us, min = 20.441 us, total = 6.856 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.284 us, total = 3.514 ms, Queueing time: mean = 42.014 us, max = 87.244 us, min = 8.588 us, total = 5.042 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 780.004 us, total = 93.600 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.732 us, total = 1.288 ms, Queueing time: mean = 65.010 us, max = 775.023 us, min = 12.939 us, total = 7.801 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 40.677 us, total = 976.256 us, Queueing time: mean = 41.539 us, max = 92.400 us, min = 15.707 us, total = 996.948 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.337 us, total = 64.038 us, Queueing time: mean = 65.220 us, max = 164.385 us, min = 40.528 us, total = 782.645 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.035 us, total = 504.244 us, Queueing time: mean = 206.949 us, max = 474.702 us, min = 7.082 us, total = 1.449 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 362.995 us, total = 725.991 us, Queueing time: mean = 34.453 us, max = 68.905 us, min = 68.905 us, total = 68.905 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 278.472 us, total = 278.472 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.243 us, total = 104.243 us, Queueing time: mean = 462.343 us, max = 462.343 us, min = 462.343 us, total = 462.343 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 439.752 us, total = 439.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 452.355 us, total = 452.355 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.540 us, total = 54.540 us, Queueing time: mean = 8.193 us, max = 8.193 us, min = 8.193 us, total = 8.193 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.119 us, total = 20.119 us, Queueing time: mean = 11.155 us, max = 11.155 us, min = 11.155 us, total = 11.155 us

-----------------
Task execution event stats:

Global stats: 11925 total (1 active)
Queueing time: mean = 53.812 us, max = 3.866 ms, min = -0.000 s, total = 641.705 ms
Execution time:  mean = 15.831 us, total = 188.784 ms
Event stats:
	CoreWorker.CheckSignal - 11924 total (1 active), Execution time: mean = 15.832 us, total = 188.775 ms, Queueing time: mean = 53.816 us, max = 3.866 ms, min = -0.000 s, total = 641.702 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.599 us, total = 8.599 us, Queueing time: mean = 2.594 us, max = 2.594 us, min = 2.594 us, total = 2.594 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 34.384 us, max = 110.223 us, min = 5.319 us, total = 12.413 ms
Execution time:  mean = 329.204 us, total = 118.843 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 756.220 us, total = 90.746 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 33.934 us, total = 4.072 ms, Queueing time: mean = 43.433 us, max = 72.720 us, min = 9.727 us, total = 5.212 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 199.350 us, total = 23.922 ms, Queueing time: mean = 59.962 us, max = 110.223 us, min = 21.799 us, total = 7.195 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 102.020 us, total = 102.020 us, Queueing time: mean = 5.319 us, max = 5.319 us, min = 5.319 us, total = 5.319 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,707 I 660945 662150] core_worker.cc:902: Event stats:


Global stats: 2592 total (8 active)
Queueing time: mean = 48.910 us, max = 775.023 us, min = -0.000 s, total = 126.774 ms
Execution time:  mean = 77.678 us, total = 201.341 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.847 us, total = 15.924 ms, Queueing time: mean = 52.059 us, max = 153.062 us, min = -0.000 s, total = 93.705 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 183.822 us, total = 33.088 ms, Queueing time: mean = 54.568 us, max = 108.485 us, min = 20.441 us, total = 9.822 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.348 us, total = 5.283 ms, Queueing time: mean = 41.603 us, max = 87.244 us, min = 8.588 us, total = 7.489 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 778.216 us, total = 140.079 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 11.037 us, total = 1.987 ms, Queueing time: mean = 58.950 us, max = 775.023 us, min = 12.939 us, total = 10.611 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 40.130 us, total = 1.445 ms, Queueing time: mean = 39.690 us, max = 92.400 us, min = 15.707 us, total = 1.429 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.757 us, total = 103.627 us, Queueing time: mean = 91.905 us, max = 613.690 us, min = 37.804 us, total = 1.654 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.035 us, total = 504.244 us, Queueing time: mean = 206.949 us, max = 474.702 us, min = 7.082 us, total = 1.449 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 526.190 us, total = 1.579 ms, Queueing time: mean = 44.527 us, max = 68.905 us, min = 64.675 us, total = 133.580 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 278.472 us, total = 278.472 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.243 us, total = 104.243 us, Queueing time: mean = 462.343 us, max = 462.343 us, min = 462.343 us, total = 462.343 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 439.752 us, total = 439.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 452.355 us, total = 452.355 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.540 us, total = 54.540 us, Queueing time: mean = 8.193 us, max = 8.193 us, min = 8.193 us, total = 8.193 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.119 us, total = 20.119 us, Queueing time: mean = 11.155 us, max = 11.155 us, min = 11.155 us, total = 11.155 us

-----------------
Task execution event stats:

Global stats: 17885 total (1 active)
Queueing time: mean = 54.362 us, max = 3.866 ms, min = -0.000 s, total = 972.258 ms
Execution time:  mean = 15.922 us, total = 284.767 ms
Event stats:
	CoreWorker.CheckSignal - 17884 total (1 active), Execution time: mean = 15.923 us, total = 284.759 ms, Queueing time: mean = 54.365 us, max = 3.866 ms, min = -0.000 s, total = 972.256 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.599 us, total = 8.599 us, Queueing time: mean = 2.594 us, max = 2.594 us, min = 2.594 us, total = 2.594 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 34.556 us, max = 110.223 us, min = 5.319 us, total = 18.695 ms
Execution time:  mean = 329.460 us, total = 178.238 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 756.681 us, total = 136.203 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 34.155 us, total = 6.148 ms, Queueing time: mean = 43.209 us, max = 72.720 us, min = 9.727 us, total = 7.778 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 198.807 us, total = 35.785 ms, Queueing time: mean = 60.623 us, max = 110.223 us, min = 21.799 us, total = 10.912 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 102.020 us, total = 102.020 us, Queueing time: mean = 5.319 us, max = 5.319 us, min = 5.319 us, total = 5.319 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,707 I 660945 662150] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 48.482 us, max = 775.023 us, min = -0.000 s, total = 167.263 ms
Execution time:  mean = 77.551 us, total = 267.552 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.774 us, total = 21.049 ms, Queueing time: mean = 51.503 us, max = 153.062 us, min = -0.000 s, total = 123.556 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 183.678 us, total = 44.083 ms, Queueing time: mean = 55.791 us, max = 113.374 us, min = 20.441 us, total = 13.390 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.230 us, total = 7.015 ms, Queueing time: mean = 42.323 us, max = 87.244 us, min = 8.588 us, total = 10.157 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 777.326 us, total = 186.558 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 11.015 us, total = 2.644 ms, Queueing time: mean = 57.195 us, max = 775.023 us, min = 12.939 us, total = 13.727 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 39.223 us, total = 1.883 ms, Queueing time: mean = 42.231 us, max = 92.400 us, min = 15.707 us, total = 2.027 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.749 us, total = 137.969 us, Queueing time: mean = 96.754 us, max = 613.690 us, min = 24.530 us, total = 2.322 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.035 us, total = 504.244 us, Queueing time: mean = 206.949 us, max = 474.702 us, min = 7.082 us, total = 1.449 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 582.216 us, total = 2.329 ms, Queueing time: mean = 38.368 us, max = 68.905 us, min = 19.894 us, total = 153.474 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 278.472 us, total = 278.472 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.243 us, total = 104.243 us, Queueing time: mean = 462.343 us, max = 462.343 us, min = 462.343 us, total = 462.343 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 439.752 us, total = 439.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 452.355 us, total = 452.355 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.540 us, total = 54.540 us, Queueing time: mean = 8.193 us, max = 8.193 us, min = 8.193 us, total = 8.193 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.119 us, total = 20.119 us, Queueing time: mean = 11.155 us, max = 11.155 us, min = 11.155 us, total = 11.155 us

-----------------
Task execution event stats:

Global stats: 23844 total (1 active)
Queueing time: mean = 55.680 us, max = 3.866 ms, min = -0.000 s, total = 1.328 s
Execution time:  mean = 15.842 us, total = 377.727 ms
Event stats:
	CoreWorker.CheckSignal - 23843 total (1 active), Execution time: mean = 15.842 us, total = 377.718 ms, Queueing time: mean = 55.683 us, max = 3.866 ms, min = -0.000 s, total = 1.328 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.599 us, total = 8.599 us, Queueing time: mean = 2.594 us, max = 2.594 us, min = 2.594 us, total = 2.594 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 35.091 us, max = 110.223 us, min = 5.319 us, total = 25.300 ms
Execution time:  mean = 330.830 us, total = 238.529 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 761.114 us, total = 182.667 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 34.432 us, total = 8.264 ms, Queueing time: mean = 44.834 us, max = 72.720 us, min = 9.727 us, total = 10.760 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 197.898 us, total = 47.495 ms, Queueing time: mean = 60.563 us, max = 110.223 us, min = 21.799 us, total = 14.535 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 102.020 us, total = 102.020 us, Queueing time: mean = 5.319 us, max = 5.319 us, min = 5.319 us, total = 5.319 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,708 I 660945 662150] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 48.738 us, max = 775.023 us, min = -0.000 s, total = 210.060 ms
Execution time:  mean = 78.122 us, total = 336.707 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.810 us, total = 26.421 ms, Queueing time: mean = 51.809 us, max = 153.062 us, min = -0.000 s, total = 155.375 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 185.804 us, total = 55.741 ms, Queueing time: mean = 55.234 us, max = 113.374 us, min = 20.441 us, total = 16.570 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.957 us, total = 8.987 ms, Queueing time: mean = 44.168 us, max = 87.244 us, min = 8.588 us, total = 13.250 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 782.403 us, total = 234.721 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.205 us, total = 3.361 ms, Queueing time: mean = 56.626 us, max = 775.023 us, min = 12.939 us, total = 16.988 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 39.359 us, total = 2.362 ms, Queueing time: mean = 43.407 us, max = 92.400 us, min = 15.707 us, total = 2.604 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.761 us, total = 172.827 us, Queueing time: mean = 103.770 us, max = 613.690 us, min = 24.530 us, total = 3.113 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.035 us, total = 504.244 us, Queueing time: mean = 206.949 us, max = 474.702 us, min = 7.082 us, total = 1.449 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 610.991 us, total = 3.055 ms, Queueing time: mean = 38.087 us, max = 68.905 us, min = 19.894 us, total = 190.434 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.366 us, total = 32.733 us, Queueing time: mean = 19.009 us, max = 38.017 us, min = 38.017 us, total = 38.017 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 278.472 us, total = 278.472 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.243 us, total = 104.243 us, Queueing time: mean = 462.343 us, max = 462.343 us, min = 462.343 us, total = 462.343 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 439.752 us, total = 439.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 452.355 us, total = 452.355 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.540 us, total = 54.540 us, Queueing time: mean = 8.193 us, max = 8.193 us, min = 8.193 us, total = 8.193 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.119 us, total = 20.119 us, Queueing time: mean = 11.155 us, max = 11.155 us, min = 11.155 us, total = 11.155 us

-----------------
Task execution event stats:

Global stats: 29803 total (1 active)
Queueing time: mean = 56.112 us, max = 3.866 ms, min = -0.000 s, total = 1.672 s
Execution time:  mean = 15.756 us, total = 469.568 ms
Event stats:
	CoreWorker.CheckSignal - 29802 total (1 active), Execution time: mean = 15.756 us, total = 469.559 ms, Queueing time: mean = 56.114 us, max = 3.866 ms, min = -0.000 s, total = 1.672 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.599 us, total = 8.599 us, Queueing time: mean = 2.594 us, max = 2.594 us, min = 2.594 us, total = 2.594 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 35.519 us, max = 114.674 us, min = 5.319 us, total = 32.003 ms
Execution time:  mean = 331.129 us, total = 298.347 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 762.410 us, total = 228.723 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 34.563 us, total = 10.369 ms, Queueing time: mean = 45.200 us, max = 72.720 us, min = 9.727 us, total = 13.560 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 197.178 us, total = 59.153 ms, Queueing time: mean = 61.458 us, max = 114.674 us, min = 21.799 us, total = 18.437 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 102.020 us, total = 102.020 us, Queueing time: mean = 5.319 us, max = 5.319 us, min = 5.319 us, total = 5.319 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,709 I 660945 662150] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 49.560 us, max = 775.023 us, min = -0.000 s, total = 256.128 ms
Execution time:  mean = 78.176 us, total = 404.014 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.858 us, total = 31.871 ms, Queueing time: mean = 52.760 us, max = 253.821 us, min = -0.000 s, total = 189.830 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 186.103 us, total = 66.997 ms, Queueing time: mean = 55.651 us, max = 113.374 us, min = 17.665 us, total = 20.034 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.010 us, total = 10.804 ms, Queueing time: mean = 44.958 us, max = 87.244 us, min = 8.588 us, total = 16.185 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 781.986 us, total = 281.515 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.432 us, total = 4.115 ms, Queueing time: mean = 57.417 us, max = 775.023 us, min = 12.939 us, total = 20.670 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 40.029 us, total = 2.882 ms, Queueing time: mean = 46.155 us, max = 92.400 us, min = 15.707 us, total = 3.323 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.719 us, total = 205.867 us, Queueing time: mean = 108.284 us, max = 613.690 us, min = 24.530 us, total = 3.898 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.035 us, total = 504.244 us, Queueing time: mean = 206.949 us, max = 474.702 us, min = 7.082 us, total = 1.449 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 622.908 us, total = 3.737 ms, Queueing time: mean = 36.434 us, max = 68.905 us, min = 19.894 us, total = 218.606 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.366 us, total = 32.733 us, Queueing time: mean = 19.009 us, max = 38.017 us, min = 38.017 us, total = 38.017 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 278.472 us, total = 278.472 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.243 us, total = 104.243 us, Queueing time: mean = 462.343 us, max = 462.343 us, min = 462.343 us, total = 462.343 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 439.752 us, total = 439.752 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 452.355 us, total = 452.355 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.540 us, total = 54.540 us, Queueing time: mean = 8.193 us, max = 8.193 us, min = 8.193 us, total = 8.193 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.119 us, total = 20.119 us, Queueing time: mean = 11.155 us, max = 11.155 us, min = 11.155 us, total = 11.155 us

-----------------
Task execution event stats:

Global stats: 35762 total (1 active)
Queueing time: mean = 56.632 us, max = 3.866 ms, min = -0.000 s, total = 2.025 s
Execution time:  mean = 15.718 us, total = 562.095 ms
Event stats:
	CoreWorker.CheckSignal - 35761 total (1 active), Execution time: mean = 15.718 us, total = 562.087 ms, Queueing time: mean = 56.634 us, max = 3.866 ms, min = -0.000 s, total = 2.025 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.599 us, total = 8.599 us, Queueing time: mean = 2.594 us, max = 2.594 us, min = 2.594 us, total = 2.594 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 35.426 us, max = 114.674 us, min = 5.319 us, total = 38.295 ms
Execution time:  mean = 331.909 us, total = 358.794 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 764.958 us, total = 275.385 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 34.718 us, total = 12.498 ms, Queueing time: mean = 44.849 us, max = 72.720 us, min = 9.727 us, total = 16.146 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 196.690 us, total = 70.808 ms, Queueing time: mean = 61.512 us, max = 114.674 us, min = 21.799 us, total = 22.144 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 102.020 us, total = 102.020 us, Queueing time: mean = 5.319 us, max = 5.319 us, min = 5.319 us, total = 5.319 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660945 662150] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660945 662150] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660945 662150] core_worker.cc:5107: Number of alive nodes:0
[2025-07-05 18:49:08,879 I 660945 662150] raylet_client.cc:281: Error reporting task backlog information: RpcError: RPC Error message: Cancelling all calls; RPC Error details:  rpc_code: 14
