Gcs Debug state:

GcsNodeManager: 
- RegisterNode request count: 1
- DrainNode request count: 0
- GetAllNodeInfo request count: 53

GcsActorManager: 
- RegisterActor request count: 2
- CreateActor request count: 2
- GetActorInfo request count: 1
- GetNamedActorInfo request count: 0
- GetAllActorInfo request count: 0
- KillActor request count: 0
- ListNamedActors request count: 0
- Registered actors count: 2
- Destroyed actors count: 0
- Named actors count: 0
- Unresolved actors count: 0
- Pending actors count: 0
- Created actors count: 1
- owners_: 1
- actor_to_register_callbacks_: 0
- actor_to_restart_for_lineage_reconstruction_callbacks_: 0
- actor_to_create_callbacks_: 1
- sorted_destroyed_actor_list_: 0

GcsResourceManager: 
- GetAllAvailableResources request count: 0
- GetAllTotalResources request count: 0
- GetAllResourceUsage request count: 74

GcsPlacementGroupManager: 
- CreatePlacementGroup request count: 0
- RemovePlacementGroup request count: 0
- GetPlacementGroup request count: 0
- GetAllPlacementGroup request count: 0
- Wait<PERSON>lacementGroupUntilReady request count: 0
- GetNamedPlacementGroup request count: 0
- Scheduling pending placement group count: 0
- Registered placement groups count: 0
- Named placement group count: 0
- Pending placement groups count: 0
- Infeasible placement groups count: 0

Publisher:
GCS_NODE_INFO_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 589
- current buffered bytes: 0
RAY_LOG_CHANNEL
- cumulative published messages: 117
- cumulative published bytes: 80001
- current buffered bytes: 891
GCS_JOB_CHANNEL
- cumulative published messages: 1
- cumulative published bytes: 288
- current buffered bytes: 288
GCS_ACTOR_CHANNEL
- cumulative published messages: 5
- cumulative published bytes: 1591
- current buffered bytes: 0

[runtime env manager] ID to URIs table:
[runtime env manager] URIs reference table:

GcsTaskManager: 
-Total num task events reported: 7
-Total num status task events dropped: 0
-Total num profile events dropped: 0
-Current num of task events stored: 4
-Total num of actor creation tasks: 2
-Total num of actor tasks: 1
-Total num of normal tasks: 0
-Total num of driver tasks: 1

GcsAutoscalerStateManager: 
- last_seen_autoscaler_state_version_: 0
- last_cluster_resource_state_version_: 74
- pending demands:





Global stats: 5006 total (8 active)
Queueing time: mean = 168.333 us, max = 77.518 ms, min = 110.000 ns, total = 842.673 ms
Execution time:  mean = 211.655 us, total = 1.060 s
Event stats:
	event_loop_lag_probe - 1482 total (0 active), Execution time: mean = 17.518 us, total = 25.962 ms, Queueing time: mean = 10.704 us, max = 6.320 ms, min = 2.282 us, total = 15.864 ms
	GcsInMemoryStore.Put - 652 total (0 active), Execution time: mean = 198.085 us, total = 129.152 ms, Queueing time: mean = 874.191 us, max = 77.318 ms, min = 1.430 us, total = 569.973 ms
	RayletLoadPulled - 370 total (1 active), Execution time: mean = 187.804 us, total = 69.488 ms, Queueing time: mean = 55.103 us, max = 121.038 us, min = 2.435 us, total = 20.388 ms
	NodeManagerService.grpc_client.GetResourceLoad.OnReplyReceived - 368 total (0 active), Execution time: mean = 57.162 us, total = 21.036 ms, Queueing time: mean = 44.111 us, max = 77.883 us, min = 15.223 us, total = 16.233 ms
	NodeManagerService.grpc_client.GetResourceLoad - 368 total (0 active), Execution time: mean = 899.244 us, total = 330.922 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut - 165 total (0 active), Execution time: mean = 310.730 us, total = 51.270 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVPut.HandleRequestImpl - 165 total (0 active), Execution time: mean = 32.995 us, total = 5.444 ms, Queueing time: mean = 44.775 us, max = 82.161 us, min = 6.258 us, total = 7.388 ms
	GcsInMemoryStore.Get - 142 total (0 active), Execution time: mean = 12.972 us, total = 1.842 ms, Queueing time: mean = 6.982 us, max = 33.098 us, min = 1.323 us, total = 991.387 us
	InternalKVGcsService.grpc_server.InternalKVGet - 141 total (0 active), Execution time: mean = 306.049 us, total = 43.153 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	InternalKVGcsService.grpc_server.InternalKVGet.HandleRequestImpl - 141 total (0 active), Execution time: mean = 28.833 us, total = 4.065 ms, Queueing time: mean = 44.681 us, max = 660.398 us, min = 5.120 us, total = 6.300 ms
	ClusterResourceManager.ResetRemoteNodeView - 124 total (1 active), Execution time: mean = 7.322 us, total = 907.988 us, Queueing time: mean = 76.627 us, max = 1.050 ms, min = 18.891 us, total = 9.502 ms
	HealthCheck - 122 total (0 active), Execution time: mean = 5.982 us, total = 729.863 us, Queueing time: mean = 43.110 us, max = 58.394 us, min = 15.854 us, total = 5.259 ms
	NodeInfoGcsService.grpc_server.CheckAlive.HandleRequestImpl - 81 total (0 active), Execution time: mean = 15.133 us, total = 1.226 ms, Queueing time: mean = 47.432 us, max = 72.578 us, min = 18.610 us, total = 3.842 ms
	NodeInfoGcsService.grpc_server.CheckAlive - 81 total (0 active), Execution time: mean = 300.971 us, total = 24.379 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage - 74 total (0 active), Execution time: mean = 372.934 us, total = 27.597 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	AutoscalerStateService.grpc_server.GetClusterResourceState.HandleRequestImpl - 74 total (0 active), Execution time: mean = 37.885 us, total = 2.803 ms, Queueing time: mean = 47.653 us, max = 67.350 us, min = 19.390 us, total = 3.526 ms
	NodeResourceInfoGcsService.grpc_server.GetAllResourceUsage.HandleRequestImpl - 74 total (0 active), Execution time: mean = 54.729 us, total = 4.050 ms, Queueing time: mean = 50.629 us, max = 70.464 us, min = 19.944 us, total = 3.747 ms
	AutoscalerStateService.grpc_server.GetClusterResourceState - 74 total (0 active), Execution time: mean = 325.790 us, total = 24.108 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo - 53 total (0 active), Execution time: mean = 295.580 us, total = 15.666 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetAllNodeInfo.HandleRequestImpl - 53 total (0 active), Execution time: mean = 16.928 us, total = 897.205 us, Queueing time: mean = 33.278 us, max = 284.738 us, min = 5.185 us, total = 1.764 ms
	GCSServer.deadline_timer.debug_state_dump - 37 total (1 active, 1 running), Execution time: mean = 1.393 ms, total = 51.530 ms, Queueing time: mean = 54.362 us, max = 76.729 us, min = 24.113 us, total = 2.011 ms
	NodeInfoGcsService.grpc_server.GetClusterId - 29 total (0 active), Execution time: mean = 188.320 us, total = 5.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	NodeInfoGcsService.grpc_server.GetClusterId.HandleRequestImpl - 29 total (0 active), Execution time: mean = 15.296 us, total = 443.594 us, Queueing time: mean = 23.785 us, max = 56.294 us, min = 7.831 us, total = 689.753 us
	WorkerInfoGcsService.grpc_server.AddWorkerInfo - 25 total (0 active), Execution time: mean = 273.568 us, total = 6.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	WorkerInfoGcsService.grpc_server.AddWorkerInfo.HandleRequestImpl - 25 total (0 active), Execution time: mean = 11.905 us, total = 297.631 us, Queueing time: mean = 67.096 us, max = 637.610 us, min = 7.348 us, total = 1.677 ms
	GCSServer.deadline_timer.debug_state_event_stats_print - 7 total (1 active), Execution time: mean = 1.278 ms, total = 8.944 ms, Queueing time: mean = 60.062 us, max = 92.629 us, min = 49.340 us, total = 420.432 us
	GcsInMemoryStore.GetAll - 6 total (0 active), Execution time: mean = 14.959 us, total = 89.752 us, Queueing time: mean = 36.963 us, max = 46.807 us, min = 6.503 us, total = 221.777 us
	PeriodicalRunner.RunFnPeriodically - 4 total (0 active), Execution time: mean = 157.890 us, total = 631.559 us, Queueing time: mean = 38.864 ms, max = 77.518 ms, min = 31.166 us, total = 155.456 ms
	CoreWorkerService.grpc_client.WaitForActorRefDeleted - 2 total (2 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.RegisterActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 597.033 us, total = 1.194 ms, Queueing time: mean = 53.142 us, max = 57.937 us, min = 48.347 us, total = 106.284 us
	NodeManagerService.grpc_client.RequestWorkerLease - 2 total (0 active), Execution time: mean = 914.984 us, total = 1.830 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsHealthCheckManager::MarkNodeHealthy - 2 total (0 active), Execution time: mean = 1.360 us, total = 2.720 us, Queueing time: mean = 4.211 ms, max = 8.372 ms, min = 50.984 us, total = 8.423 ms
	ActorInfoGcsService.grpc_server.RegisterActor - 2 total (0 active), Execution time: mean = 934.552 us, total = 1.869 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.CreateActor.HandleRequestImpl - 2 total (0 active), Execution time: mean = 383.161 us, total = 766.322 us, Queueing time: mean = 19.713 us, max = 21.036 us, min = 18.390 us, total = 39.426 us
	ActorInfoGcsService.grpc_server.CreateActor - 2 total (1 active), Execution time: mean = 28.808 ms, total = 57.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 2 total (1 active), Execution time: mean = 27.784 ms, total = 55.568 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsResourceManager::Update - 2 total (0 active), Execution time: mean = 59.562 us, total = 119.123 us, Queueing time: mean = 4.199 ms, max = 8.366 ms, min = 32.414 us, total = 8.398 ms
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 2 total (0 active), Execution time: mean = 302.106 us, total = 604.212 us, Queueing time: mean = 49.912 us, max = 52.127 us, min = 47.697 us, total = 99.824 us
	InternalKVGcsService.grpc_server.GetInternalConfig.HandleRequestImpl - 1 total (0 active), Execution time: mean = 51.973 us, total = 51.973 us, Queueing time: mean = 57.801 us, max = 57.801 us, min = 57.801 us, total = 57.801 us
	InternalKVGcsService.grpc_server.GetInternalConfig - 1 total (0 active), Execution time: mean = 449.486 us, total = 449.486 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.GetNextJobID - 1 total (0 active), Execution time: mean = 217.427 us, total = 217.427 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInitData::AsyncLoad - 1 total (0 active), Execution time: mean = 28.302 us, total = 28.302 us, Queueing time: mean = 190.000 ns, max = 190.000 ns, min = 190.000 ns, total = 190.000 ns
	JobInfoGcsService.grpc_server.GetNextJobID.HandleRequestImpl - 1 total (0 active), Execution time: mean = 7.157 us, total = 7.157 us, Queueing time: mean = 53.538 us, max = 53.538 us, min = 53.538 us, total = 53.538 us
	JobInfoGcsService.grpc_server.AddJob - 1 total (0 active), Execution time: mean = 179.464 us, total = 179.464 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	JobInfoGcsService.grpc_server.AddJob.HandleRequestImpl - 1 total (0 active), Execution time: mean = 62.233 us, total = 62.233 us, Queueing time: mean = 5.687 us, max = 5.687 us, min = 5.687 us, total = 5.687 us
	NodeInfoGcsService.grpc_server.RegisterNode - 1 total (0 active), Execution time: mean = 816.646 us, total = 816.646 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsInMemoryStore.Delete - 1 total (0 active), Execution time: mean = 9.483 us, total = 9.483 us, Queueing time: mean = 7.193 us, max = 7.193 us, min = 7.193 us, total = 7.193 us
	JobInfoGcsService.grpc_server.GetAllJobInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 15.633 us, total = 15.633 us, Queueing time: mean = 54.801 us, max = 54.801 us, min = 54.801 us, total = 54.801 us
	GcsHealthCheckManager::AddNode - 1 total (0 active), Execution time: mean = 12.710 us, total = 12.710 us, Queueing time: mean = 554.000 ns, max = 554.000 ns, min = 554.000 ns, total = 554.000 ns
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 1 total (0 active), Execution time: mean = 337.099 us, total = 337.099 us, Queueing time: mean = 53.254 us, max = 53.254 us, min = 53.254 us, total = 53.254 us
	ActorInfoGcsService.grpc_server.GetActorInfo - 1 total (0 active), Execution time: mean = 340.669 us, total = 340.669 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	ActorInfoGcsService.grpc_server.GetActorInfo.HandleRequestImpl - 1 total (0 active), Execution time: mean = 21.827 us, total = 21.827 us, Queueing time: mean = 15.907 us, max = 15.907 us, min = 15.907 us, total = 15.907 us
	JobInfoGcsService.grpc_server.GetAllJobInfo - 1 total (0 active), Execution time: mean = 455.736 us, total = 455.736 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s
	GcsServer.GetOrGenerateClusterId.continuation - 1 total (0 active), Execution time: mean = 77.520 ms, total = 77.520 ms, Queueing time: mean = 110.000 ns, max = 110.000 ns, min = 110.000 ns, total = 110.000 ns
	InternalKVGcsService.grpc_server.InternalKVDel.HandleRequestImpl - 1 total (0 active), Execution time: mean = 50.454 us, total = 50.454 us, Queueing time: mean = 46.858 us, max = 46.858 us, min = 46.858 us, total = 46.858 us
	NodeInfoGcsService.grpc_server.RegisterNode.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.004 us, total = 112.004 us, Queueing time: mean = 50.963 us, max = 50.963 us, min = 50.963 us, total = 50.963 us
	GcsInMemoryStore.GetNextJobID - 1 total (0 active), Execution time: mean = 8.613 us, total = 8.613 us, Queueing time: mean = 6.080 us, max = 6.080 us, min = 6.080 us, total = 6.080 us
	InternalKVGcsService.grpc_server.InternalKVDel - 1 total (0 active), Execution time: mean = 340.770 us, total = 340.770 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = **********.855 s, total = 0.000 s