[2025-07-05 18:42:59,467 I 660939 660939] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660939
[2025-07-05 18:42:59,472 I 660939 660939] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,475 I 660939 660939] grpc_server.cc:141: worker server started, listening on port 37723.
[2025-07-05 18:42:59,476 I 660939 660939] core_worker.cc:542: Initializing worker at address: ***********:37723 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,477 I 660939 660939] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,478 I 660939 661427] core_worker.cc:902: Event stats:


Global stats: 8 total (6 active)
Queueing time: mean = 13.132 us, max = 91.201 us, min = 13.856 us, total = 105.057 us
Execution time:  mean = 3.738 us, total = 29.905 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 9.968 us, total = 29.905 us, Queueing time: mean = 35.019 us, max = 91.201 us, min = 13.856 us, total = 105.057 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3 total (2 active)
Queueing time: mean = 1.827 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Execution time:  mean = 45.455 us, total = 136.364 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 136.364 us, total = 136.364 us, Queueing time: mean = 5.482 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Other Stats:
	grpc_in_progress:1
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,478 I 660939 660939] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,479 I 660939 660939] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,479 I 660939 660939] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,479 I 660939 660939] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,479 I 660939 661427] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,479 I 660939 661427] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,858 I 660939 660939] actor_task_submitter.cc:73: Set actor max pending calls to -1 actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:42:59,858 I 660939 660939] core_worker.cc:3370: Creating actor actor_id=c4dbba14b481690e7680c72d01000000
[2025-07-05 18:43:09,484 W 660939 661418] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,479 I 660939 661427] core_worker.cc:902: Event stats:


Global stats: 877 total (9 active)
Queueing time: mean = 49.572 us, max = 1.114 ms, min = 6.582 us, total = 43.475 ms
Execution time:  mean = 90.609 us, total = 79.464 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.907 us, total = 5.344 ms, Queueing time: mean = 53.992 us, max = 338.702 us, min = 11.951 us, total = 32.395 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 13.013 us, total = 793.816 us, Queueing time: mean = 60.478 us, max = 114.505 us, min = 29.332 us, total = 3.689 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 864.038 us, total = 51.842 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 32.502 us, total = 1.950 ms, Queueing time: mean = 37.716 us, max = 60.290 us, min = 9.912 us, total = 2.263 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 203.213 us, total = 12.193 ms, Queueing time: mean = 64.017 us, max = 1.114 ms, min = 12.207 us, total = 3.841 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 237.482 us, total = 2.850 ms, Queueing time: mean = 45.123 us, max = 119.421 us, min = 17.682 us, total = 541.477 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.157 us, total = 512.099 us, Queueing time: mean = 21.213 us, max = 91.201 us, min = 6.582 us, total = 148.488 us
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.264 us, total = 31.582 us, Queueing time: mean = 32.278 us, max = 49.827 us, min = 31.009 us, total = 193.666 us
	CoreWorkerService.grpc_server.PushTask - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.188 ms, total = 1.188 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 100.698 us, total = 100.698 us, Queueing time: mean = 65.027 us, max = 65.027 us, min = 65.027 us, total = 65.027 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 125.242 us, total = 125.242 us, Queueing time: mean = 106.053 us, max = 106.053 us, min = 106.053 us, total = 106.053 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.747 ms, total = 1.747 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 61.676 us, total = 61.676 us, Queueing time: mean = 11.189 us, max = 11.189 us, min = 11.189 us, total = 11.189 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.879 us, total = 13.879 us, Queueing time: mean = 220.411 us, max = 220.411 us, min = 220.411 us, total = 220.411 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 711.816 us, total = 711.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 40 total (2 active)
Queueing time: mean = 39.774 us, max = 533.440 us, min = 2.545 us, total = 1.591 ms
Execution time:  mean = 7.421 us, total = 296.837 us
Event stats:
	CoreWorker.CheckSignal - 38 total (1 active), Execution time: mean = 7.593 us, total = 288.532 us, Queueing time: mean = 41.801 us, max = 533.440 us, min = 11.145 us, total = 1.588 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.305 us, total = 8.305 us, Queueing time: mean = 2.545 us, max = 2.545 us, min = 2.545 us, total = 2.545 us
	CoreWorker.HandlePushTask - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 31.429 us, max = 139.553 us, min = 5.482 us, total = 5.689 ms
Execution time:  mean = 377.685 us, total = 68.361 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 859.599 us, total = 51.576 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 43.048 us, total = 2.583 ms, Queueing time: mean = 38.836 us, max = 68.984 us, min = 11.184 us, total = 2.330 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 234.429 us, total = 14.066 ms, Queueing time: mean = 55.882 us, max = 139.553 us, min = 26.383 us, total = 3.353 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 136.364 us, total = 136.364 us, Queueing time: mean = 5.482 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00018692 MiB
	total number of task attempts sent: 2
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,480 I 660939 661427] core_worker.cc:902: Event stats:


Global stats: 1735 total (9 active)
Queueing time: mean = 48.969 us, max = 1.114 ms, min = 6.582 us, total = 84.962 ms
Execution time:  mean = 89.801 us, total = 155.805 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 9.024 us, total = 10.828 ms, Queueing time: mean = 53.349 us, max = 338.702 us, min = 11.951 us, total = 64.019 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 13.054 us, total = 1.566 ms, Queueing time: mean = 55.962 us, max = 114.505 us, min = 11.258 us, total = 6.715 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 866.025 us, total = 103.923 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.496 us, total = 4.140 ms, Queueing time: mean = 39.584 us, max = 100.660 us, min = 9.912 us, total = 4.750 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 212.315 us, total = 25.478 ms, Queueing time: mean = 57.245 us, max = 1.114 ms, min = 12.207 us, total = 6.869 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 179.452 us, total = 4.307 ms, Queueing time: mean = 45.338 us, max = 119.421 us, min = 7.578 us, total = 1.088 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 6.080 us, total = 72.961 us, Queueing time: mean = 76.529 us, max = 514.285 us, min = 19.113 us, total = 918.349 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.157 us, total = 512.099 us, Queueing time: mean = 21.213 us, max = 91.201 us, min = 6.582 us, total = 148.488 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 514.975 us, total = 1.030 ms, Queueing time: mean = 24.922 us, max = 49.843 us, min = 49.843 us, total = 49.843 us
	CoreWorkerService.grpc_server.PushTask - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.188 ms, total = 1.188 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 100.698 us, total = 100.698 us, Queueing time: mean = 65.027 us, max = 65.027 us, min = 65.027 us, total = 65.027 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 125.242 us, total = 125.242 us, Queueing time: mean = 106.053 us, max = 106.053 us, min = 106.053 us, total = 106.053 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.747 ms, total = 1.747 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 61.676 us, total = 61.676 us, Queueing time: mean = 11.189 us, max = 11.189 us, min = 11.189 us, total = 11.189 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.879 us, total = 13.879 us, Queueing time: mean = 220.411 us, max = 220.411 us, min = 220.411 us, total = 220.411 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 711.816 us, total = 711.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 40 total (2 active)
Queueing time: mean = 39.774 us, max = 533.440 us, min = 2.545 us, total = 1.591 ms
Execution time:  mean = 7.421 us, total = 296.837 us
Event stats:
	CoreWorker.CheckSignal - 38 total (1 active), Execution time: mean = 7.593 us, total = 288.532 us, Queueing time: mean = 41.801 us, max = 533.440 us, min = 11.145 us, total = 1.588 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.305 us, total = 8.305 us, Queueing time: mean = 2.545 us, max = 2.545 us, min = 2.545 us, total = 2.545 us
	CoreWorker.HandlePushTask - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 32.586 us, max = 139.553 us, min = 5.482 us, total = 11.764 ms
Execution time:  mean = 371.621 us, total = 134.155 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 842.744 us, total = 101.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 42.602 us, total = 5.112 ms, Queueing time: mean = 39.852 us, max = 127.391 us, min = 11.184 us, total = 4.782 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 231.476 us, total = 27.777 ms, Queueing time: mean = 58.132 us, max = 139.553 us, min = 25.756 us, total = 6.976 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 136.364 us, total = 136.364 us, Queueing time: mean = 5.482 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00018692 MiB
	total number of task attempts sent: 2
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,481 I 660939 661427] core_worker.cc:902: Event stats:


Global stats: 2593 total (9 active)
Queueing time: mean = 48.823 us, max = 1.114 ms, min = 6.582 us, total = 126.598 ms
Execution time:  mean = 88.350 us, total = 229.092 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.990 us, total = 16.172 ms, Queueing time: mean = 53.026 us, max = 338.702 us, min = 7.519 us, total = 95.394 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 13.073 us, total = 2.353 ms, Queueing time: mean = 54.839 us, max = 114.505 us, min = 11.258 us, total = 9.871 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 851.893 us, total = 153.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 35.272 us, total = 6.349 ms, Queueing time: mean = 39.605 us, max = 100.660 us, min = 9.912 us, total = 7.129 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 214.518 us, total = 38.613 ms, Queueing time: mean = 54.770 us, max = 1.114 ms, min = 12.207 us, total = 9.859 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 157.241 us, total = 5.661 ms, Queueing time: mean = 47.069 us, max = 119.421 us, min = 7.578 us, total = 1.695 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 6.585 us, total = 118.531 us, Queueing time: mean = 111.041 us, max = 798.649 us, min = 19.113 us, total = 1.999 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.157 us, total = 512.099 us, Queueing time: mean = 21.213 us, max = 91.201 us, min = 6.582 us, total = 148.488 us
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 674.976 us, total = 2.025 ms, Queueing time: mean = 33.588 us, max = 50.920 us, min = 49.843 us, total = 100.763 us
	CoreWorkerService.grpc_server.PushTask - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.188 ms, total = 1.188 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 100.698 us, total = 100.698 us, Queueing time: mean = 65.027 us, max = 65.027 us, min = 65.027 us, total = 65.027 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 125.242 us, total = 125.242 us, Queueing time: mean = 106.053 us, max = 106.053 us, min = 106.053 us, total = 106.053 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.747 ms, total = 1.747 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 61.676 us, total = 61.676 us, Queueing time: mean = 11.189 us, max = 11.189 us, min = 11.189 us, total = 11.189 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.879 us, total = 13.879 us, Queueing time: mean = 220.411 us, max = 220.411 us, min = 220.411 us, total = 220.411 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 711.816 us, total = 711.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 40 total (2 active)
Queueing time: mean = 39.774 us, max = 533.440 us, min = 2.545 us, total = 1.591 ms
Execution time:  mean = 7.421 us, total = 296.837 us
Event stats:
	CoreWorker.CheckSignal - 38 total (1 active), Execution time: mean = 7.593 us, total = 288.532 us, Queueing time: mean = 41.801 us, max = 533.440 us, min = 11.145 us, total = 1.588 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.305 us, total = 8.305 us, Queueing time: mean = 2.545 us, max = 2.545 us, min = 2.545 us, total = 2.545 us
	CoreWorker.HandlePushTask - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 32.360 us, max = 139.553 us, min = 5.482 us, total = 17.507 ms
Execution time:  mean = 371.481 us, total = 200.971 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 844.188 us, total = 151.954 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 43.303 us, total = 7.794 ms, Queueing time: mean = 41.132 us, max = 127.391 us, min = 11.184 us, total = 7.404 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 228.258 us, total = 41.087 ms, Queueing time: mean = 56.099 us, max = 139.553 us, min = 25.756 us, total = 10.098 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 136.364 us, total = 136.364 us, Queueing time: mean = 5.482 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00018692 MiB
	total number of task attempts sent: 2
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,482 I 660939 661427] core_worker.cc:902: Event stats:


Global stats: 3452 total (9 active)
Queueing time: mean = 50.645 us, max = 1.114 ms, min = 6.582 us, total = 174.826 ms
Execution time:  mean = 85.505 us, total = 295.162 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.869 us, total = 21.278 ms, Queueing time: mean = 55.063 us, max = 807.155 us, min = 7.519 us, total = 132.097 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 12.813 us, total = 3.075 ms, Queueing time: mean = 56.648 us, max = 163.086 us, min = 11.258 us, total = 13.596 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 825.137 us, total = 198.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 34.774 us, total = 8.346 ms, Queueing time: mean = 41.116 us, max = 100.660 us, min = 9.912 us, total = 9.868 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 208.010 us, total = 49.922 ms, Queueing time: mean = 54.320 us, max = 1.114 ms, min = 12.207 us, total = 13.037 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 144.004 us, total = 6.912 ms, Queueing time: mean = 50.619 us, max = 119.421 us, min = 7.578 us, total = 2.430 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 6.369 us, total = 152.856 us, Queueing time: mean = 127.775 us, max = 798.649 us, min = 19.113 us, total = 3.067 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.157 us, total = 512.099 us, Queueing time: mean = 21.213 us, max = 91.201 us, min = 6.582 us, total = 148.488 us
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 745.861 us, total = 2.983 ms, Queueing time: mean = 45.404 us, max = 80.853 us, min = 49.843 us, total = 181.616 us
	CoreWorkerService.grpc_server.PushTask - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.188 ms, total = 1.188 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 100.698 us, total = 100.698 us, Queueing time: mean = 65.027 us, max = 65.027 us, min = 65.027 us, total = 65.027 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 125.242 us, total = 125.242 us, Queueing time: mean = 106.053 us, max = 106.053 us, min = 106.053 us, total = 106.053 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.747 ms, total = 1.747 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 61.676 us, total = 61.676 us, Queueing time: mean = 11.189 us, max = 11.189 us, min = 11.189 us, total = 11.189 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.879 us, total = 13.879 us, Queueing time: mean = 220.411 us, max = 220.411 us, min = 220.411 us, total = 220.411 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 711.816 us, total = 711.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 40 total (2 active)
Queueing time: mean = 39.774 us, max = 533.440 us, min = 2.545 us, total = 1.591 ms
Execution time:  mean = 7.421 us, total = 296.837 us
Event stats:
	CoreWorker.CheckSignal - 38 total (1 active), Execution time: mean = 7.593 us, total = 288.532 us, Queueing time: mean = 41.801 us, max = 533.440 us, min = 11.145 us, total = 1.588 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.305 us, total = 8.305 us, Queueing time: mean = 2.545 us, max = 2.545 us, min = 2.545 us, total = 2.545 us
	CoreWorker.HandlePushTask - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 31.511 us, max = 139.553 us, min = 5.482 us, total = 22.719 ms
Execution time:  mean = 368.813 us, total = 265.914 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 837.463 us, total = 200.991 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 43.236 us, total = 10.377 ms, Queueing time: mean = 40.205 us, max = 127.391 us, min = 11.184 us, total = 9.649 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 226.709 us, total = 54.410 ms, Queueing time: mean = 54.436 us, max = 139.553 us, min = 25.756 us, total = 13.065 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 136.364 us, total = 136.364 us, Queueing time: mean = 5.482 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00018692 MiB
	total number of task attempts sent: 2
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,483 I 660939 661427] core_worker.cc:902: Event stats:


Global stats: 4312 total (9 active)
Queueing time: mean = 50.632 us, max = 1.114 ms, min = 6.582 us, total = 218.326 ms
Execution time:  mean = 84.196 us, total = 363.054 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.881 us, total = 26.633 ms, Queueing time: mean = 54.884 us, max = 807.155 us, min = 7.519 us, total = 164.597 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 12.961 us, total = 3.888 ms, Queueing time: mean = 56.139 us, max = 163.086 us, min = 11.258 us, total = 16.842 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 810.973 us, total = 243.292 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 35.063 us, total = 10.519 ms, Queueing time: mean = 41.309 us, max = 100.660 us, min = 9.912 us, total = 12.393 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 206.533 us, total = 61.960 ms, Queueing time: mean = 55.188 us, max = 1.114 ms, min = 12.207 us, total = 16.556 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 136.231 us, total = 8.174 ms, Queueing time: mean = 52.229 us, max = 119.421 us, min = 7.578 us, total = 3.134 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 6.495 us, total = 194.839 us, Queueing time: mean = 131.031 us, max = 798.649 us, min = 19.113 us, total = 3.931 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.157 us, total = 512.099 us, Queueing time: mean = 21.213 us, max = 91.201 us, min = 6.582 us, total = 148.488 us
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 782.802 us, total = 3.914 ms, Queueing time: mean = 50.623 us, max = 80.853 us, min = 49.843 us, total = 253.116 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 9.424 us, total = 18.847 us, Queueing time: mean = 34.548 us, max = 69.096 us, min = 69.096 us, total = 69.096 us
	CoreWorkerService.grpc_server.PushTask - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.188 ms, total = 1.188 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 100.698 us, total = 100.698 us, Queueing time: mean = 65.027 us, max = 65.027 us, min = 65.027 us, total = 65.027 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 125.242 us, total = 125.242 us, Queueing time: mean = 106.053 us, max = 106.053 us, min = 106.053 us, total = 106.053 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.747 ms, total = 1.747 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 61.676 us, total = 61.676 us, Queueing time: mean = 11.189 us, max = 11.189 us, min = 11.189 us, total = 11.189 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.879 us, total = 13.879 us, Queueing time: mean = 220.411 us, max = 220.411 us, min = 220.411 us, total = 220.411 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 711.816 us, total = 711.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 40 total (2 active)
Queueing time: mean = 39.774 us, max = 533.440 us, min = 2.545 us, total = 1.591 ms
Execution time:  mean = 7.421 us, total = 296.837 us
Event stats:
	CoreWorker.CheckSignal - 38 total (1 active), Execution time: mean = 7.593 us, total = 288.532 us, Queueing time: mean = 41.801 us, max = 533.440 us, min = 11.145 us, total = 1.588 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.305 us, total = 8.305 us, Queueing time: mean = 2.545 us, max = 2.545 us, min = 2.545 us, total = 2.545 us
	CoreWorker.HandlePushTask - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 31.313 us, max = 139.553 us, min = 5.482 us, total = 28.213 ms
Execution time:  mean = 369.827 us, total = 333.214 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 839.315 us, total = 251.794 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 43.978 us, total = 13.193 ms, Queueing time: mean = 40.908 us, max = 127.391 us, min = 11.184 us, total = 12.272 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 226.966 us, total = 68.090 ms, Queueing time: mean = 53.118 us, max = 139.553 us, min = 25.756 us, total = 15.935 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 136.364 us, total = 136.364 us, Queueing time: mean = 5.482 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00018692 MiB
	total number of task attempts sent: 2
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,484 I 660939 661427] core_worker.cc:902: Event stats:


Global stats: 5170 total (9 active)
Queueing time: mean = 50.748 us, max = 1.114 ms, min = 6.582 us, total = 262.365 ms
Execution time:  mean = 82.518 us, total = 426.617 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.973 us, total = 32.285 ms, Queueing time: mean = 55.090 us, max = 807.155 us, min = 7.519 us, total = 198.215 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 12.854 us, total = 4.628 ms, Queueing time: mean = 56.426 us, max = 163.086 us, min = 11.258 us, total = 20.314 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 792.498 us, total = 285.299 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 34.512 us, total = 12.424 ms, Queueing time: mean = 40.293 us, max = 100.660 us, min = 9.912 us, total = 14.505 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 202.776 us, total = 72.999 ms, Queueing time: mean = 54.627 us, max = 1.114 ms, min = 12.207 us, total = 19.666 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 130.544 us, total = 9.399 ms, Queueing time: mean = 51.718 us, max = 119.421 us, min = 7.578 us, total = 3.724 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 6.364 us, total = 229.105 us, Queueing time: mean = 138.857 us, max = 798.649 us, min = 19.113 us, total = 4.999 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.157 us, total = 512.099 us, Queueing time: mean = 21.213 us, max = 91.201 us, min = 6.582 us, total = 148.488 us
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 812.344 us, total = 4.874 ms, Queueing time: mean = 53.748 us, max = 80.853 us, min = 49.843 us, total = 322.489 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 9.424 us, total = 18.847 us, Queueing time: mean = 34.548 us, max = 69.096 us, min = 69.096 us, total = 69.096 us
	CoreWorkerService.grpc_server.PushTask - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.188 ms, total = 1.188 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 100.698 us, total = 100.698 us, Queueing time: mean = 65.027 us, max = 65.027 us, min = 65.027 us, total = 65.027 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 125.242 us, total = 125.242 us, Queueing time: mean = 106.053 us, max = 106.053 us, min = 106.053 us, total = 106.053 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.747 ms, total = 1.747 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 61.676 us, total = 61.676 us, Queueing time: mean = 11.189 us, max = 11.189 us, min = 11.189 us, total = 11.189 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.879 us, total = 13.879 us, Queueing time: mean = 220.411 us, max = 220.411 us, min = 220.411 us, total = 220.411 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 711.816 us, total = 711.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 40 total (2 active)
Queueing time: mean = 39.774 us, max = 533.440 us, min = 2.545 us, total = 1.591 ms
Execution time:  mean = 7.421 us, total = 296.837 us
Event stats:
	CoreWorker.CheckSignal - 38 total (1 active), Execution time: mean = 7.593 us, total = 288.532 us, Queueing time: mean = 41.801 us, max = 533.440 us, min = 11.145 us, total = 1.588 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.305 us, total = 8.305 us, Queueing time: mean = 2.545 us, max = 2.545 us, min = 2.545 us, total = 2.545 us
	CoreWorker.HandlePushTask - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 31.894 us, max = 139.553 us, min = 5.482 us, total = 34.477 ms
Execution time:  mean = 365.846 us, total = 395.479 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 830.023 us, total = 298.808 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 43.278 us, total = 15.580 ms, Queueing time: mean = 41.240 us, max = 127.391 us, min = 11.184 us, total = 14.846 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 224.875 us, total = 80.955 ms, Queueing time: mean = 54.514 us, max = 139.553 us, min = 24.758 us, total = 19.625 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 136.364 us, total = 136.364 us, Queueing time: mean = 5.482 us, max = 5.482 us, min = 5.482 us, total = 5.482 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00018692 MiB
	total number of task attempts sent: 2
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:07,815 I 660939 660939] task_receiver.cc:175: Actor creation task finished, task_id: ffffffffffffffffc4dbba14b481690e7680c72d01000000, actor_id: c4dbba14b481690e7680c72d01000000, actor_repr_name: 
[2025-07-05 18:49:07,819 I 660939 660939] out_of_order_actor_scheduling_queue.cc:48: Setting actor as asyncio with max_concurrency=1000, and defined concurrency groups are:

[2025-07-05 18:49:08,664 I 660939 661427] core_worker.cc:4542: Force kill actor request has received. exiting immediately... The actor is dead because its owner has died. Owner Id: 01000000ffffffffffffffffffffffffffffffffffffffffffffffff Owner Ip address: *********** Owner worker exit type: INTENDED_USER_EXIT Worker exit detail: Owner's worker process has crashed.
[2025-07-05 18:49:08,664 W 660939 661427] core_worker.cc:1253: Force exit the process.  Details: Worker exits because the actor is killed. The actor is dead because its owner has died. Owner Id: 01000000ffffffffffffffffffffffffffffffffffffffffffffffff Owner Ip address: *********** Owner worker exit type: INTENDED_USER_EXIT Worker exit detail: Owner's worker process has crashed.
[2025-07-05 18:49:08,672 I 660939 660939] core_worker.cc:1163: Exit signal received, this process will exit after all outstanding tasks have finished, exit_type=SYSTEM_ERROR, detail=Worker exits unexpectedly by a signal. SystemExit is raised (sys.exit is called). Exit code: 1. The process receives a SIGTERM.
[2025-07-05 18:49:08,672 I 660939 660939] core_worker.cc:1206: Wait for currently executing tasks in the underlying thread pools to finish.
[2025-07-05 18:49:08,672 I 660939 660939] concurrency_group_manager.cc:99: Default executor is joining. If the 'Default executor is joined.' message is not printed after this, the worker is probably hanging because the actor task is running an infinite loop.
[2025-07-05 18:49:08,672 I 660939 660939] concurrency_group_manager.cc:103: Default executor is joined.
[2025-07-05 18:49:08,672 I 660939 660939] core_worker.cc:1240: Not draining reference counter since this is an actor worker.
[2025-07-05 18:49:08,680 I 660939 661427] core_worker.cc:1134: Try killing all child processes of this worker as it exits. Child process pids: 
[2025-07-05 18:49:08,682 I 660939 661427] core_worker.cc:1088: Sending disconnect message to the local raylet.
[2025-07-05 18:49:08,682 I 660939 661427] raylet_client.cc:73: RayletClient::Disconnect, exit_type=INTENDED_SYSTEM_EXIT, exit_detail=Worker exits because the actor is killed. The actor is dead because its owner has died. Owner Id: 01000000ffffffffffffffffffffffffffffffffffffffffffffffff Owner Ip address: *********** Owner worker exit type: INTENDED_USER_EXIT Worker exit detail: Owner's worker process has crashed., has creation_task_exception_pb_bytes=0
[2025-07-05 18:49:08,682 I 660939 661427] core_worker.cc:1094: Disconnected from the local raylet.
