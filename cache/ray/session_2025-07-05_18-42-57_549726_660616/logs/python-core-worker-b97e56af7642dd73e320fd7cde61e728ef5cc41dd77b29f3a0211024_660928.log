[2025-07-05 18:42:59,518 I 660928 660928] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660928
[2025-07-05 18:42:59,520 I 660928 660928] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,522 I 660928 660928] grpc_server.cc:141: worker server started, listening on port 42567.
[2025-07-05 18:42:59,523 I 660928 660928] core_worker.cc:542: Initializing worker at address: ***********:42567 worker_id=b97e56af7642dd73e320fd7cde61e728ef5cc41dd77b29f3a0211024 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,524 I 660928 660928] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-07-05 18:42:59,525 I 660928 660928] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,525 I 660928 661862] core_worker.cc:902: Event stats:


Global stats: 12 total (5 active)
Queueing time: mean = 122.886 us, max = 672.900 us, min = 80.863 us, total = 1.475 ms
Execution time:  mean = 111.763 us, total = 1.341 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 9.167 us, total = 27.502 us, Queueing time: mean = 212.400 us, max = 556.336 us, min = 80.863 us, total = 637.199 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 400.574 us, total = 400.574 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 319.122 us, total = 319.122 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 368.540 us, total = 368.540 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.437 us, total = 22.437 us, Queueing time: mean = 672.900 us, max = 672.900 us, min = 672.900 us, total = 672.900 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 202.980 us, total = 202.980 us, Queueing time: mean = 164.539 us, max = 164.539 us, min = 164.539 us, total = 164.539 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.897 us, max = 8.455 us, min = 7.134 us, total = 15.589 us
Execution time:  mean = 154.882 us, total = 619.529 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.699 us, total = 103.699 us, Queueing time: mean = 7.134 us, max = 7.134 us, min = 7.134 us, total = 7.134 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 496.306 us, total = 496.306 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.524 us, total = 19.524 us, Queueing time: mean = 8.455 us, max = 8.455 us, min = 8.455 us, total = 8.455 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,525 I 660928 661862] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,525 I 660928 661862] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,526 I 660928 660928] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,526 I 660928 660928] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,526 I 660928 660928] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,531 W 660928 661857] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,526 I 660928 661862] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 49.678 us, max = 676.724 us, min = 8.093 us, total = 43.468 ms
Execution time:  mean = 74.321 us, total = 65.031 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.663 us, total = 5.198 ms, Queueing time: mean = 51.633 us, max = 169.691 us, min = 13.644 us, total = 30.980 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.905 us, total = 665.178 us, Queueing time: mean = 53.268 us, max = 77.016 us, min = 21.509 us, total = 3.249 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 745.158 us, total = 44.709 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 172.954 us, total = 10.377 ms, Queueing time: mean = 45.896 us, max = 92.961 us, min = 11.542 us, total = 2.754 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.337 us, total = 1.640 ms, Queueing time: mean = 38.305 us, max = 69.250 us, min = 9.154 us, total = 2.298 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 31.656 us, total = 379.870 us, Queueing time: mean = 28.979 us, max = 68.664 us, min = 12.760 us, total = 347.749 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 96.342 us, total = 674.394 us, Queueing time: mean = 361.977 us, max = 676.724 us, min = 8.093 us, total = 2.534 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.205 us, total = 31.227 us, Queueing time: mean = 38.484 us, max = 78.746 us, min = 23.668 us, total = 230.904 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.437 us, total = 22.437 us, Queueing time: mean = 672.900 us, max = 672.900 us, min = 672.900 us, total = 672.900 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 400.574 us, total = 400.574 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 202.980 us, total = 202.980 us, Queueing time: mean = 164.539 us, max = 164.539 us, min = 164.539 us, total = 164.539 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 319.122 us, total = 319.122 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 41.541 us, total = 41.541 us, Queueing time: mean = 237.352 us, max = 237.352 us, min = 237.352 us, total = 237.352 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 368.540 us, total = 368.540 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5961 total (1 active)
Queueing time: mean = 58.130 us, max = 1.425 ms, min = 2.627 us, total = 346.514 ms
Execution time:  mean = 14.946 us, total = 89.092 ms
Event stats:
	CoreWorker.CheckSignal - 5960 total (1 active), Execution time: mean = 14.947 us, total = 89.083 ms, Queueing time: mean = 58.140 us, max = 1.425 ms, min = 9.143 us, total = 346.512 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.227 us, total = 8.227 us, Queueing time: mean = 2.627 us, max = 2.627 us, min = 2.627 us, total = 2.627 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 30.900 us, max = 100.663 us, min = 7.134 us, total = 5.593 ms
Execution time:  mean = 332.032 us, total = 60.098 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 205.755 us, total = 12.345 ms, Queueing time: mean = 53.557 us, max = 100.663 us, min = 17.261 us, total = 3.213 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 756.993 us, total = 45.420 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 37.154 us, total = 2.229 ms, Queueing time: mean = 39.539 us, max = 67.284 us, min = 8.455 us, total = 2.372 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.699 us, total = 103.699 us, Queueing time: mean = 7.134 us, max = 7.134 us, min = 7.134 us, total = 7.134 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,527 I 660928 661862] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 52.228 us, max = 676.724 us, min = 8.093 us, total = 90.511 ms
Execution time:  mean = 75.577 us, total = 130.974 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.507 us, total = 10.209 ms, Queueing time: mean = 55.831 us, max = 398.343 us, min = 13.644 us, total = 66.997 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 763.033 us, total = 91.564 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 174.004 us, total = 20.881 ms, Queueing time: mean = 54.559 us, max = 92.961 us, min = 11.542 us, total = 6.547 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.722 us, total = 1.287 ms, Queueing time: mean = 55.516 us, max = 84.972 us, min = 15.995 us, total = 6.662 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.505 us, total = 3.421 ms, Queueing time: mean = 43.261 us, max = 109.834 us, min = 9.154 us, total = 5.191 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 35.560 us, total = 853.449 us, Queueing time: mean = 39.306 us, max = 75.538 us, min = 12.760 us, total = 943.351 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.164 us, total = 61.972 us, Queueing time: mean = 44.293 us, max = 78.746 us, min = 23.668 us, total = 531.519 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 96.342 us, total = 674.394 us, Queueing time: mean = 361.977 us, max = 676.724 us, min = 8.093 us, total = 2.534 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 334.514 us, total = 669.028 us, Queueing time: mean = 15.432 us, max = 30.865 us, min = 30.865 us, total = 30.865 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.437 us, total = 22.437 us, Queueing time: mean = 672.900 us, max = 672.900 us, min = 672.900 us, total = 672.900 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 400.574 us, total = 400.574 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 202.980 us, total = 202.980 us, Queueing time: mean = 164.539 us, max = 164.539 us, min = 164.539 us, total = 164.539 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 319.122 us, total = 319.122 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 41.541 us, total = 41.541 us, Queueing time: mean = 237.352 us, max = 237.352 us, min = 237.352 us, total = 237.352 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 368.540 us, total = 368.540 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11920 total (1 active)
Queueing time: mean = 58.633 us, max = 1.604 ms, min = 2.627 us, total = 698.902 ms
Execution time:  mean = 15.083 us, total = 179.787 ms
Event stats:
	CoreWorker.CheckSignal - 11919 total (1 active), Execution time: mean = 15.083 us, total = 179.779 ms, Queueing time: mean = 58.637 us, max = 1.604 ms, min = 9.143 us, total = 698.899 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.227 us, total = 8.227 us, Queueing time: mean = 2.627 us, max = 2.627 us, min = 2.627 us, total = 2.627 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 31.590 us, max = 141.868 us, min = 7.134 us, total = 11.404 ms
Execution time:  mean = 339.552 us, total = 122.578 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 215.926 us, total = 25.911 ms, Queueing time: mean = 55.037 us, max = 141.868 us, min = 17.261 us, total = 6.604 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 766.082 us, total = 91.930 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 38.614 us, total = 4.634 ms, Queueing time: mean = 39.936 us, max = 71.090 us, min = 8.455 us, total = 4.792 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.699 us, total = 103.699 us, Queueing time: mean = 7.134 us, max = 7.134 us, min = 7.134 us, total = 7.134 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,528 I 660928 661862] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 52.209 us, max = 676.724 us, min = -0.000 s, total = 135.274 ms
Execution time:  mean = 76.161 us, total = 197.334 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.615 us, total = 15.498 ms, Queueing time: mean = 56.500 us, max = 398.343 us, min = -0.000 s, total = 101.643 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 766.637 us, total = 137.995 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 176.893 us, total = 31.841 ms, Queueing time: mean = 54.651 us, max = 92.961 us, min = 11.542 us, total = 9.837 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.900 us, total = 1.962 ms, Queueing time: mean = 53.527 us, max = 84.972 us, min = 14.014 us, total = 9.635 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 28.830 us, total = 5.189 ms, Queueing time: mean = 41.996 us, max = 109.834 us, min = 9.154 us, total = 7.559 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.698 us, total = 1.285 ms, Queueing time: mean = 44.446 us, max = 75.538 us, min = 12.760 us, total = 1.600 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.199 us, total = 93.580 us, Queueing time: mean = 71.515 us, max = 521.370 us, min = 23.668 us, total = 1.287 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 96.342 us, total = 674.394 us, Queueing time: mean = 361.977 us, max = 676.724 us, min = 8.093 us, total = 2.534 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 480.124 us, total = 1.440 ms, Queueing time: mean = 34.679 us, max = 73.172 us, min = 30.865 us, total = 104.037 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.437 us, total = 22.437 us, Queueing time: mean = 672.900 us, max = 672.900 us, min = 672.900 us, total = 672.900 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 400.574 us, total = 400.574 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 202.980 us, total = 202.980 us, Queueing time: mean = 164.539 us, max = 164.539 us, min = 164.539 us, total = 164.539 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 319.122 us, total = 319.122 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 41.541 us, total = 41.541 us, Queueing time: mean = 237.352 us, max = 237.352 us, min = 237.352 us, total = 237.352 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 368.540 us, total = 368.540 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17883 total (1 active)
Queueing time: mean = 56.026 us, max = 1.604 ms, min = 2.627 us, total = 1.002 s
Execution time:  mean = 15.119 us, total = 270.371 ms
Event stats:
	CoreWorker.CheckSignal - 17882 total (1 active), Execution time: mean = 15.119 us, total = 270.363 ms, Queueing time: mean = 56.029 us, max = 1.604 ms, min = 9.143 us, total = 1.002 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.227 us, total = 8.227 us, Queueing time: mean = 2.627 us, max = 2.627 us, min = 2.627 us, total = 2.627 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 30.218 us, max = 141.868 us, min = 7.134 us, total = 16.348 ms
Execution time:  mean = 329.319 us, total = 178.162 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 210.086 us, total = 37.815 ms, Queueing time: mean = 54.202 us, max = 141.868 us, min = 17.261 us, total = 9.756 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 741.795 us, total = 133.523 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 37.330 us, total = 6.719 ms, Queueing time: mean = 36.580 us, max = 71.090 us, min = 8.455 us, total = 6.584 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.699 us, total = 103.699 us, Queueing time: mean = 7.134 us, max = 7.134 us, min = 7.134 us, total = 7.134 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,528 I 660928 661862] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 50.611 us, max = 676.724 us, min = -0.000 s, total = 174.606 ms
Execution time:  mean = 76.665 us, total = 264.494 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.652 us, total = 20.755 ms, Queueing time: mean = 54.809 us, max = 398.343 us, min = -0.000 s, total = 131.488 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 770.617 us, total = 184.948 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 180.037 us, total = 43.209 ms, Queueing time: mean = 53.481 us, max = 92.961 us, min = 11.542 us, total = 12.835 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 11.012 us, total = 2.643 ms, Queueing time: mean = 52.057 us, max = 89.696 us, min = 14.014 us, total = 12.494 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.011 us, total = 6.963 ms, Queueing time: mean = 41.103 us, max = 109.834 us, min = 9.154 us, total = 9.865 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.932 us, total = 1.725 ms, Queueing time: mean = 46.742 us, max = 99.015 us, min = 12.760 us, total = 2.244 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.315 us, total = 127.549 us, Queueing time: mean = 80.622 us, max = 521.370 us, min = 23.668 us, total = 1.935 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 96.342 us, total = 674.394 us, Queueing time: mean = 361.977 us, max = 676.724 us, min = 8.093 us, total = 2.534 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 523.765 us, total = 2.095 ms, Queueing time: mean = 34.476 us, max = 73.172 us, min = 30.865 us, total = 137.905 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.437 us, total = 22.437 us, Queueing time: mean = 672.900 us, max = 672.900 us, min = 672.900 us, total = 672.900 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 400.574 us, total = 400.574 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 202.980 us, total = 202.980 us, Queueing time: mean = 164.539 us, max = 164.539 us, min = 164.539 us, total = 164.539 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 319.122 us, total = 319.122 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 41.541 us, total = 41.541 us, Queueing time: mean = 237.352 us, max = 237.352 us, min = 237.352 us, total = 237.352 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 368.540 us, total = 368.540 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23843 total (1 active)
Queueing time: mean = 56.420 us, max = 1.604 ms, min = 2.627 us, total = 1.345 s
Execution time:  mean = 15.131 us, total = 360.767 ms
Event stats:
	CoreWorker.CheckSignal - 23842 total (1 active), Execution time: mean = 15.131 us, total = 360.759 ms, Queueing time: mean = 56.422 us, max = 1.604 ms, min = 2.703 us, total = 1.345 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.227 us, total = 8.227 us, Queueing time: mean = 2.627 us, max = 2.627 us, min = 2.627 us, total = 2.627 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 30.667 us, max = 141.868 us, min = 7.134 us, total = 22.111 ms
Execution time:  mean = 325.826 us, total = 234.921 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 208.726 us, total = 50.094 ms, Queueing time: mean = 54.174 us, max = 141.868 us, min = 17.261 us, total = 13.002 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 732.795 us, total = 175.871 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.882 us, total = 8.852 ms, Queueing time: mean = 37.926 us, max = 71.090 us, min = 8.455 us, total = 9.102 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.699 us, total = 103.699 us, Queueing time: mean = 7.134 us, max = 7.134 us, min = 7.134 us, total = 7.134 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,529 I 660928 661862] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 50.749 us, max = 676.724 us, min = -0.000 s, total = 218.729 ms
Execution time:  mean = 76.026 us, total = 327.671 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.596 us, total = 25.779 ms, Queueing time: mean = 54.982 us, max = 398.343 us, min = -0.000 s, total = 164.891 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 761.891 us, total = 228.567 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 180.075 us, total = 54.023 ms, Queueing time: mean = 53.554 us, max = 119.456 us, min = 11.542 us, total = 16.066 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.025 us, total = 3.307 ms, Queueing time: mean = 52.953 us, max = 163.489 us, min = 14.014 us, total = 15.886 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.366 us, total = 8.810 ms, Queueing time: mean = 41.320 us, max = 109.834 us, min = 9.154 us, total = 12.396 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 36.532 us, total = 2.192 ms, Queueing time: mean = 46.149 us, max = 99.015 us, min = 12.760 us, total = 2.769 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.306 us, total = 159.176 us, Queueing time: mean = 93.897 us, max = 530.463 us, min = 23.668 us, total = 2.817 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 96.342 us, total = 674.394 us, Queueing time: mean = 361.977 us, max = 676.724 us, min = 8.093 us, total = 2.534 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 559.049 us, total = 2.795 ms, Queueing time: mean = 43.283 us, max = 78.512 us, min = 30.865 us, total = 216.417 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.527 us, total = 9.053 us, Queueing time: mean = 39.321 us, max = 78.641 us, min = 78.641 us, total = 78.641 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.437 us, total = 22.437 us, Queueing time: mean = 672.900 us, max = 672.900 us, min = 672.900 us, total = 672.900 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 400.574 us, total = 400.574 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 202.980 us, total = 202.980 us, Queueing time: mean = 164.539 us, max = 164.539 us, min = 164.539 us, total = 164.539 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 319.122 us, total = 319.122 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 41.541 us, total = 41.541 us, Queueing time: mean = 237.352 us, max = 237.352 us, min = 237.352 us, total = 237.352 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 368.540 us, total = 368.540 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29802 total (1 active)
Queueing time: mean = 56.900 us, max = 1.604 ms, min = 2.627 us, total = 1.696 s
Execution time:  mean = 15.104 us, total = 450.133 ms
Event stats:
	CoreWorker.CheckSignal - 29801 total (1 active), Execution time: mean = 15.104 us, total = 450.125 ms, Queueing time: mean = 56.902 us, max = 1.604 ms, min = 2.703 us, total = 1.696 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.227 us, total = 8.227 us, Queueing time: mean = 2.627 us, max = 2.627 us, min = 2.627 us, total = 2.627 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 31.409 us, max = 141.868 us, min = 7.134 us, total = 28.299 ms
Execution time:  mean = 323.319 us, total = 291.311 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 207.156 us, total = 62.147 ms, Queueing time: mean = 56.115 us, max = 141.868 us, min = 17.261 us, total = 16.835 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 727.007 us, total = 218.102 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.527 us, total = 10.958 ms, Queueing time: mean = 38.192 us, max = 71.090 us, min = 8.455 us, total = 11.457 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.699 us, total = 103.699 us, Queueing time: mean = 7.134 us, max = 7.134 us, min = 7.134 us, total = 7.134 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,530 I 660928 661862] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 51.177 us, max = 676.724 us, min = -0.000 s, total = 264.481 ms
Execution time:  mean = 74.688 us, total = 385.990 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.577 us, total = 30.861 ms, Queueing time: mean = 55.491 us, max = 398.343 us, min = -0.000 s, total = 199.656 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 744.322 us, total = 267.956 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 178.693 us, total = 64.329 ms, Queueing time: mean = 54.774 us, max = 119.456 us, min = 11.542 us, total = 19.719 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.324 us, total = 4.077 ms, Queueing time: mean = 54.807 us, max = 163.489 us, min = 14.014 us, total = 19.731 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.056 us, total = 10.460 ms, Queueing time: mean = 40.278 us, max = 109.834 us, min = 9.154 us, total = 14.500 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 36.525 us, total = 2.630 ms, Queueing time: mean = 46.812 us, max = 99.015 us, min = 12.760 us, total = 3.370 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.348 us, total = 192.510 us, Queueing time: mean = 97.636 us, max = 530.463 us, min = 23.668 us, total = 3.515 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 96.342 us, total = 674.394 us, Queueing time: mean = 361.977 us, max = 676.724 us, min = 8.093 us, total = 2.534 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 574.326 us, total = 3.446 ms, Queueing time: mean = 50.412 us, max = 86.056 us, min = 30.865 us, total = 302.473 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.527 us, total = 9.053 us, Queueing time: mean = 39.321 us, max = 78.641 us, min = 78.641 us, total = 78.641 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.437 us, total = 22.437 us, Queueing time: mean = 672.900 us, max = 672.900 us, min = 672.900 us, total = 672.900 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 400.574 us, total = 400.574 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 202.980 us, total = 202.980 us, Queueing time: mean = 164.539 us, max = 164.539 us, min = 164.539 us, total = 164.539 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 319.122 us, total = 319.122 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 41.541 us, total = 41.541 us, Queueing time: mean = 237.352 us, max = 237.352 us, min = 237.352 us, total = 237.352 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 368.540 us, total = 368.540 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35763 total (1 active)
Queueing time: mean = 56.580 us, max = 1.604 ms, min = 2.627 us, total = 2.023 s
Execution time:  mean = 15.123 us, total = 540.839 ms
Event stats:
	CoreWorker.CheckSignal - 35762 total (1 active), Execution time: mean = 15.123 us, total = 540.831 ms, Queueing time: mean = 56.582 us, max = 1.604 ms, min = 2.703 us, total = 2.023 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.227 us, total = 8.227 us, Queueing time: mean = 2.627 us, max = 2.627 us, min = 2.627 us, total = 2.627 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 31.760 us, max = 141.868 us, min = 7.134 us, total = 34.333 ms
Execution time:  mean = 321.533 us, total = 347.577 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 205.158 us, total = 73.857 ms, Queueing time: mean = 56.367 us, max = 141.868 us, min = 17.261 us, total = 20.292 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 723.662 us, total = 260.518 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.385 us, total = 13.099 ms, Queueing time: mean = 38.983 us, max = 71.090 us, min = 8.455 us, total = 14.034 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 103.699 us, total = 103.699 us, Queueing time: mean = 7.134 us, max = 7.134 us, min = 7.134 us, total = 7.134 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660928 661862] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660928 661862] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660928 661862] core_worker.cc:5107: Number of alive nodes:0
