[2025-07-05 18:42:58,852 I 660838 660838] (raylet) main.cc:226: Setting cluster ID to: 8770274bf56d098bb9c6d6a10e6d5bf0b6b2a670f53ca772dc307c48
[2025-07-05 18:42:58,859 I 660838 660838] (raylet) main.cc:341: Ray<PERSON> is not set to kill unknown children.
[2025-07-05 18:42:58,859 I 660838 660838] (raylet) io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:58,860 I 660838 660838] (raylet) main.cc:469: Setting node ID node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:58,860 I 660838 660838] (raylet) store_runner.cc:50: Allowing the Plasma store to use up to 79.3075GB of memory.
[2025-07-05 18:42:58,860 I 660838 660838] (raylet) store_runner.cc:66: Starting object store with directory /dev/shm, fallback /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616, and huge page support disabled
[2025-07-05 18:42:58,860 I 660838 660867] (raylet) dlmalloc.cc:324: Setting dlmalloc config: plasma_directory=/dev/shm, fallback_directory=/home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616, hugepage_enabled=0, fallback_enabled=1
[2025-07-05 18:42:58,860 I 660838 660867] (raylet) dlmalloc.cc:153: create_and_mmap_buffer(79307472904, /dev/shm/plasmaXXXXXX)
[2025-07-05 18:42:58,861 E 660838 660867] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2932 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:42:58,861 I 660838 660867] (raylet) store.cc:576: Plasma store debug dump: 
Current usage: 0 / 79.3075 GB
- num bytes created total: 0
0 pending objects of total size 0MB
- objects spillable: 0
- bytes spillable: 0
- objects unsealed: 0
- bytes unsealed: 0
- objects in use: 0
- bytes in use: 0
- objects evictable: 0
- bytes evictable: 0

- objects created by worker: 0
- bytes created by worker: 0
- objects restored: 0
- bytes restored: 0
- objects received: 0
- bytes received: 0
- objects errored: 0
- bytes errored: 0

[2025-07-05 18:42:58,863 I 660838 660838] (raylet) grpc_server.cc:141: ObjectManager server started, listening on port 33695.
[2025-07-05 18:42:58,865 I 660838 660838] (raylet) worker_killing_policy.cc:107: Running GroupByOwner policy.
[2025-07-05 18:42:58,865 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:42:58,866 I 660838 660838] (raylet) memory_monitor.cc:48: MemoryMonitor initialized with usage threshold at 256812630016 bytes (0.95 system memory), total system memory bytes: 270329081856
[2025-07-05 18:42:58,866 I 660838 660838] (raylet) node_manager.cc:227: Initializing NodeManager node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:58,866 I 660838 660838] (raylet) grpc_server.cc:141: NodeManager server started, listening on port 36275.
[2025-07-05 18:42:58,874 I 660838 660905] (raylet) agent_manager.cc:80: Monitor agent process with name dashboard_agent
[2025-07-05 18:42:58,874 I 660838 660838] (raylet) event.cc:500: Ray Event initialized for RAYLET
[2025-07-05 18:42:58,874 I 660838 660907] (raylet) agent_manager.cc:80: Monitor agent process with name runtime_env_agent
[2025-07-05 18:42:58,874 I 660838 660838] (raylet) event.cc:331: Set ray event level to warning
[2025-07-05 18:42:58,876 I 660838 660838] (raylet) raylet.cc:260: Raylet of id, 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d started. Raylet consists of node_manager and object_manager. node_manager address: ***********:36275 object_manager address: ***********:33695 hostname: GPU008-D03-203-G01-17
[2025-07-05 18:42:58,878 I 660838 660838] (raylet) node_manager.cc:450: [state-dump] NodeManager:
[state-dump] Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[state-dump] Node name: ***********
[state-dump] InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:***********: 1, memory: 1.85051e+11}
[state-dump] ClusterTaskManager:
[state-dump] ========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
[state-dump] Infeasible queue length: 0
[state-dump] Schedule queue length: 0
[state-dump] Dispatch queue length: 0
[state-dump] num_waiting_for_resource: 0
[state-dump] num_waiting_for_plasma_memory: 0
[state-dump] num_waiting_for_remote_node_resources: 0
[state-dump] num_worker_not_started_by_job_config_not_exist: 0
[state-dump] num_worker_not_started_by_registration_timeout: 0
[state-dump] num_tasks_waiting_for_workers: 0
[state-dump] num_cancelled_tasks: 0
[state-dump] cluster_resource_scheduler state: 
[state-dump] Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "available": {CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 1 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, CPU: 240000, node:__internal_head__: 10000, GPU: 40000, object_store_memory: 793074573310000, node:***********: 10000, memory: 1850507337730000}}, "available": {object_store_memory: 793074573310000, CPU: 240000, accelerator_type:G: 10000, GPU: 40000, node:***********: 10000, node:__internal_head__: 10000, memory: 1850507337730000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
[state-dump] Waiting tasks size: 0
[state-dump] Number of executing tasks: 0
[state-dump] Number of pinned task arguments: 0
[state-dump] Number of total spilled tasks: 0
[state-dump] Number of spilled waiting tasks: 0
[state-dump] Number of spilled unschedulable tasks: 0
[state-dump] Resource usage {
[state-dump] }
[state-dump] Backlog Size per scheduling descriptor :{workerId: num backlogs}:
[state-dump] 
[state-dump] Running tasks by scheduling class:
[state-dump] ==================================================
[state-dump] 
[state-dump] ClusterResources:
[state-dump] LocalObjectManager:
[state-dump] - num pinned objects: 0
[state-dump] - pinned objects size: 0
[state-dump] - num objects pending restore: 0
[state-dump] - num objects pending spill: 0
[state-dump] - num bytes pending spill: 0
[state-dump] - num bytes currently spilled: 0
[state-dump] - cumulative spill requests: 0
[state-dump] - cumulative restore requests: 0
[state-dump] - spilled objects pending delete: 0
[state-dump] 
[state-dump] ObjectManager:
[state-dump] - num local objects: 0
[state-dump] - num unfulfilled push requests: 0
[state-dump] - num object pull requests: 0
[state-dump] - num chunks received total: 0
[state-dump] - num chunks received failed (all): 0
[state-dump] - num chunks received failed / cancelled: 0
[state-dump] - num chunks received failed / plasma error: 0
[state-dump] Event stats:
[state-dump] Global stats: 0 total (0 active)
[state-dump] Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] Execution time:  mean = -nan s, total = 0.000 s
[state-dump] Event stats:
[state-dump] PushManager:
[state-dump] - num pushes in flight: 0
[state-dump] - num chunks in flight: 0
[state-dump] - num chunks remaining: 0
[state-dump] - max chunks allowed: 409
[state-dump] OwnershipBasedObjectDirectory:
[state-dump] - num listeners: 0
[state-dump] - cumulative location updates: 0
[state-dump] - num location updates per second: 0.000
[state-dump] - num location lookups per second: 0.000
[state-dump] - num locations added per second: 0.000
[state-dump] - num locations removed per second: 0.000
[state-dump] BufferPool:
[state-dump] - create buffer state map size: 0
[state-dump] PullManager:
[state-dump] - num bytes available for pulled objects: 79307457331
[state-dump] - num bytes being pulled (all): 0
[state-dump] - num bytes being pulled / pinned: 0
[state-dump] - get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - first get request bundle: N/A
[state-dump] - first wait request bundle: N/A
[state-dump] - first task request bundle: N/A
[state-dump] - num objects queued: 0
[state-dump] - num objects actively pulled (all): 0
[state-dump] - num objects actively pulled / pinned: 0
[state-dump] - num bundles being pulled: 0
[state-dump] - num pull retries: 0
[state-dump] - max timeout seconds: 0
[state-dump] - max timeout request is already processed. No entry.
[state-dump] 
[state-dump] WorkerPool:
[state-dump] - registered jobs: 0
[state-dump] - process_failed_job_config_missing: 0
[state-dump] - process_failed_rate_limited: 0
[state-dump] - process_failed_pending_registration: 0
[state-dump] - process_failed_runtime_env_setup_failed: 0
[state-dump] - num PYTHON workers: 0
[state-dump] - num PYTHON drivers: 0
[state-dump] - num PYTHON pending start requests: 0
[state-dump] - num PYTHON pending registration requests: 0
[state-dump] - num object spill callbacks queued: 0
[state-dump] - num object restore queued: 0
[state-dump] - num util functions queued: 0
[state-dump] - num idle workers: 0
[state-dump] TaskDependencyManager:
[state-dump] - task deps map size: 0
[state-dump] - get req map size: 0
[state-dump] - wait req map size: 0
[state-dump] - local objects map size: 0
[state-dump] WaitManager:
[state-dump] - num active wait requests: 0
[state-dump] Subscriber:
[state-dump] Channel WORKER_OBJECT_EVICTION
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_REF_REMOVED_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_OBJECT_LOCATIONS_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] num async plasma notifications: 0
[state-dump] Event stats:
[state-dump] Global stats: 29 total (14 active)
[state-dump] Queueing time: mean = 1.512 ms, max = 9.264 ms, min = 12.432 us, total = 43.853 ms
[state-dump] Execution time:  mean = 749.299 us, total = 21.730 ms
[state-dump] Event stats:
[state-dump] 	PeriodicalRunner.RunFnPeriodically - 12 total (2 active, 1 running), Execution time: mean = 169.439 us, total = 2.033 ms, Queueing time: mean = 3.650 ms, max = 9.264 ms, min = 28.791 us, total = 43.802 ms
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.deadline_timer.spill_objects_when_over_threshold - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.deadline_timer.flush_free_objects - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.ScheduleAndDispatchTasks - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckForUnexpectedWorkerDisconnects - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.100 ms, total = 1.100 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ClusterResourceManager.ResetRemoteNodeView - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.deadline_timer.record_metrics - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.deadline_timer.debug_state_dump - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	RayletWorkerPool.deadline_timer.kill_idle_workers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
[state-dump] DebugString() time ms: 0
[state-dump] 
[state-dump] 
[2025-07-05 18:42:58,880 I 660838 660838] (raylet) accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,013 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660926, the token is 0
[2025-07-05 18:42:59,015 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660927, the token is 1
[2025-07-05 18:42:59,017 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660928, the token is 2
[2025-07-05 18:42:59,020 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660929, the token is 3
[2025-07-05 18:42:59,022 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660930, the token is 4
[2025-07-05 18:42:59,024 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660931, the token is 5
[2025-07-05 18:42:59,026 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660932, the token is 6
[2025-07-05 18:42:59,028 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660933, the token is 7
[2025-07-05 18:42:59,030 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660934, the token is 8
[2025-07-05 18:42:59,032 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660935, the token is 9
[2025-07-05 18:42:59,034 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660936, the token is 10
[2025-07-05 18:42:59,036 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660937, the token is 11
[2025-07-05 18:42:59,039 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660938, the token is 12
[2025-07-05 18:42:59,041 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660939, the token is 13
[2025-07-05 18:42:59,043 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660940, the token is 14
[2025-07-05 18:42:59,045 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660941, the token is 15
[2025-07-05 18:42:59,047 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660942, the token is 16
[2025-07-05 18:42:59,049 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660943, the token is 17
[2025-07-05 18:42:59,051 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660944, the token is 18
[2025-07-05 18:42:59,097 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660945, the token is 19
[2025-07-05 18:42:59,099 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660946, the token is 20
[2025-07-05 18:42:59,101 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660947, the token is 21
[2025-07-05 18:42:59,103 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660948, the token is 22
[2025-07-05 18:42:59,106 I 660838 660838] (raylet) worker_pool.cc:527: Started worker process with pid 660949, the token is 23
[2025-07-05 18:42:59,478 I 660838 660867] (raylet) object_store.cc:38: Object store current usage 8e-09 / 79.3075 GB.
[2025-07-05 18:42:59,732 I 660838 660838] (raylet) worker_pool.cc:724: Job 01000000 already started in worker pool.
[2025-07-05 18:43:03,879 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:08,871 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2927 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:43:08,872 W 660838 660861] (raylet) metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:08,884 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:13,889 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:18,882 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2927 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:43:18,893 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:23,898 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:28,892 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.3028 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:43:28,903 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:33,908 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:38,903 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.3026 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:43:38,913 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:43,917 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:48,915 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.3024 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:43:48,922 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:53,927 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:43:58,861 I 660838 660867] (raylet) store.cc:576: Plasma store debug dump: 
Current usage: 0 / 79.3075 GB
- num bytes created total: 200
0 pending objects of total size 0MB
- objects spillable: 0
- bytes spillable: 0
- objects unsealed: 0
- bytes unsealed: 0
- objects in use: 0
- bytes in use: 0
- objects evictable: 0
- bytes evictable: 0

- objects created by worker: 0
- bytes created by worker: 0
- objects restored: 0
- bytes restored: 0
- objects received: 0
- bytes received: 0
- objects errored: 0
- bytes errored: 0

[2025-07-05 18:43:58,880 I 660838 660838] (raylet) node_manager.cc:450: [state-dump] NodeManager:
[state-dump] Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[state-dump] Node name: ***********
[state-dump] InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:***********: 1, memory: 1.85051e+11}
[state-dump] ClusterTaskManager:
[state-dump] ========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
[state-dump] Infeasible queue length: 0
[state-dump] Schedule queue length: 0
[state-dump] Dispatch queue length: 0
[state-dump] num_waiting_for_resource: 0
[state-dump] num_waiting_for_plasma_memory: 0
[state-dump] num_waiting_for_remote_node_resources: 0
[state-dump] num_worker_not_started_by_job_config_not_exist: 0
[state-dump] num_worker_not_started_by_registration_timeout: 0
[state-dump] num_tasks_waiting_for_workers: 0
[state-dump] num_cancelled_tasks: 0
[state-dump] cluster_resource_scheduler state: 
[state-dump] Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "available": {CPU: [230000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [0, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 0 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, node:***********: 10000, GPU: 40000, memory: 1850507337730000, CPU: 240000, object_store_memory: 793074573310000, node:__internal_head__: 10000}}, "available": {object_store_memory: 793074573310000, node:***********: 10000, node:__internal_head__: 10000, memory: 1850507337730000, CPU: 230000, accelerator_type:G: 10000, GPU: 30000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
[state-dump] Waiting tasks size: 0
[state-dump] Number of executing tasks: 1
[state-dump] Number of pinned task arguments: 0
[state-dump] Number of total spilled tasks: 0
[state-dump] Number of spilled waiting tasks: 0
[state-dump] Number of spilled unschedulable tasks: 0
[state-dump] Resource usage {
[state-dump]     - (language=PYTHON actor_or_task=ResultCollector.__init__ pid=660927 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385): {}
[state-dump]     - (language=PYTHON actor_or_task=LLMEngine.__init__ pid=660939 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0): {GPU: 1, CPU: 1}
[state-dump] }
[state-dump] Backlog Size per scheduling descriptor :{workerId: num backlogs}:
[state-dump] 
[state-dump] Running tasks by scheduling class:
[state-dump]     - {depth=1 function_descriptor={type=PythonFunctionDescriptor, module_name=Ayo.engines.llm, class_name=LLMEngine, function_name=__init__, function_hash=e2f5af0ea8e345998125e6a621a10aec} scheduling_strategy=default_scheduling_strategy {
[state-dump] }
[state-dump]  resource_set={CPU : 1, GPU : 1, }}: 1/24
[state-dump] ==================================================
[state-dump] 
[state-dump] ClusterResources:
[state-dump] LocalObjectManager:
[state-dump] - num pinned objects: 0
[state-dump] - pinned objects size: 0
[state-dump] - num objects pending restore: 0
[state-dump] - num objects pending spill: 0
[state-dump] - num bytes pending spill: 0
[state-dump] - num bytes currently spilled: 0
[state-dump] - cumulative spill requests: 0
[state-dump] - cumulative restore requests: 0
[state-dump] - spilled objects pending delete: 0
[state-dump] 
[state-dump] ObjectManager:
[state-dump] - num local objects: 0
[state-dump] - num unfulfilled push requests: 0
[state-dump] - num object pull requests: 0
[state-dump] - num chunks received total: 0
[state-dump] - num chunks received failed (all): 0
[state-dump] - num chunks received failed / cancelled: 0
[state-dump] - num chunks received failed / plasma error: 0
[state-dump] Event stats:
[state-dump] Global stats: 0 total (0 active)
[state-dump] Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] Execution time:  mean = -nan s, total = 0.000 s
[state-dump] Event stats:
[state-dump] PushManager:
[state-dump] - num pushes in flight: 0
[state-dump] - num chunks in flight: 0
[state-dump] - num chunks remaining: 0
[state-dump] - max chunks allowed: 409
[state-dump] OwnershipBasedObjectDirectory:
[state-dump] - num listeners: 0
[state-dump] - cumulative location updates: 0
[state-dump] - num location updates per second: 0.000
[state-dump] - num location lookups per second: 0.000
[state-dump] - num locations added per second: 0.000
[state-dump] - num locations removed per second: 0.000
[state-dump] BufferPool:
[state-dump] - create buffer state map size: 0
[state-dump] PullManager:
[state-dump] - num bytes available for pulled objects: 79307457331
[state-dump] - num bytes being pulled (all): 0
[state-dump] - num bytes being pulled / pinned: 0
[state-dump] - get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - first get request bundle: N/A
[state-dump] - first wait request bundle: N/A
[state-dump] - first task request bundle: N/A
[state-dump] - num objects queued: 0
[state-dump] - num objects actively pulled (all): 0
[state-dump] - num objects actively pulled / pinned: 0
[state-dump] - num bundles being pulled: 0
[state-dump] - num pull retries: 0
[state-dump] - max timeout seconds: 0
[state-dump] - max timeout request is already processed. No entry.
[state-dump] 
[state-dump] WorkerPool:
[state-dump] - registered jobs: 1
[state-dump] - process_failed_job_config_missing: 0
[state-dump] - process_failed_rate_limited: 0
[state-dump] - process_failed_pending_registration: 0
[state-dump] - process_failed_runtime_env_setup_failed: 0
[state-dump] - num PYTHON workers: 24
[state-dump] - num PYTHON drivers: 1
[state-dump] - num PYTHON pending start requests: 0
[state-dump] - num PYTHON pending registration requests: 0
[state-dump] - num object spill callbacks queued: 0
[state-dump] - num object restore queued: 0
[state-dump] - num util functions queued: 0
[state-dump] - num idle workers: 22
[state-dump] TaskDependencyManager:
[state-dump] - task deps map size: 0
[state-dump] - get req map size: 0
[state-dump] - wait req map size: 0
[state-dump] - local objects map size: 0
[state-dump] WaitManager:
[state-dump] - num active wait requests: 0
[state-dump] Subscriber:
[state-dump] Channel WORKER_OBJECT_EVICTION
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_REF_REMOVED_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_OBJECT_LOCATIONS_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] num async plasma notifications: 0
[state-dump] Event stats:
[state-dump] Global stats: 6094 total (40 active)
[state-dump] Queueing time: mean = 320.710 us, max = 625.620 ms, min = 48.000 ns, total = 1.954 s
[state-dump] Execution time:  mean = 249.894 us, total = 1.523 s
[state-dump] Event stats:
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog.HandleRequestImpl - 1500 total (0 active), Execution time: mean = 25.825 us, total = 38.737 ms, Queueing time: mean = 38.268 us, max = 659.005 us, min = 4.063 us, total = 57.402 ms
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog - 1500 total (0 active), Execution time: mean = 253.484 us, total = 380.227 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	RaySyncer.OnDemandBroadcasting - 600 total (1 active), Execution time: mean = 6.384 us, total = 3.831 ms, Queueing time: mean = 101.405 us, max = 25.814 ms, min = 3.808 us, total = 60.843 ms
[state-dump] 	NodeManager.CheckGC - 600 total (1 active), Execution time: mean = 1.921 us, total = 1.153 ms, Queueing time: mean = 105.426 us, max = 25.832 ms, min = 7.559 us, total = 63.255 ms
[state-dump] 	ObjectManager.UpdateAvailableMemory - 599 total (0 active), Execution time: mean = 2.908 us, total = 1.742 ms, Queueing time: mean = 18.637 us, max = 212.831 us, min = 2.792 us, total = 11.163 ms
[state-dump] 	RayletWorkerPool.deadline_timer.kill_idle_workers - 300 total (1 active), Execution time: mean = 10.391 us, total = 3.117 ms, Queueing time: mean = 157.472 us, max = 30.900 ms, min = 12.787 us, total = 47.242 ms
[state-dump] 	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 240 total (1 active), Execution time: mean = 185.543 us, total = 44.530 ms, Queueing time: mean = 52.513 us, max = 107.812 us, min = 11.417 us, total = 12.603 ms
[state-dump] 	ClientConnection.async_read.ProcessMessageHeader - 103 total (25 active), Execution time: mean = 2.777 us, total = 286.060 us, Queueing time: mean = 15.460 ms, max = 625.620 ms, min = 12.580 us, total = 1.592 s
[state-dump] 	ClientConnection.async_read.ProcessMessage - 78 total (0 active), Execution time: mean = 1.299 ms, total = 101.318 ms, Queueing time: mean = 12.499 us, max = 146.915 us, min = 2.738 us, total = 974.893 us
[state-dump] 	NodeManager.deadline_timer.spill_objects_when_over_threshold - 60 total (1 active), Execution time: mean = 2.117 us, total = 127.006 us, Queueing time: mean = 275.912 us, max = 8.796 ms, min = 15.710 us, total = 16.555 ms
[state-dump] 	NodeManager.CheckForUnexpectedWorkerDisconnects - 60 total (1 active), Execution time: mean = 44.573 us, total = 2.674 ms, Queueing time: mean = 21.278 us, max = 76.374 us, min = 6.906 us, total = 1.277 ms
[state-dump] 	NodeManager.ScheduleAndDispatchTasks - 60 total (1 active), Execution time: mean = 11.291 us, total = 677.457 us, Queueing time: mean = 54.449 us, max = 80.447 us, min = 15.308 us, total = 3.267 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad - 60 total (0 active), Execution time: mean = 362.499 us, total = 21.750 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad.HandleRequestImpl - 60 total (0 active), Execution time: mean = 80.489 us, total = 4.829 ms, Queueing time: mean = 46.780 us, max = 82.354 us, min = 7.863 us, total = 2.807 ms
[state-dump] 	NodeManager.deadline_timer.flush_free_objects - 60 total (1 active), Execution time: mean = 7.222 us, total = 433.339 us, Queueing time: mean = 271.681 us, max = 8.799 ms, min = 15.861 us, total = 16.301 ms
[state-dump] 	ClientConnection.async_write.DoAsyncWrites - 26 total (0 active), Execution time: mean = 525.308 ns, total = 13.658 us, Queueing time: mean = 16.479 us, max = 51.598 us, min = 8.590 us, total = 428.462 us
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig - 25 total (0 active), Execution time: mean = 361.343 us, total = 9.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ObjectManager.ObjectDeleted - 25 total (0 active), Execution time: mean = 9.360 us, total = 234.005 us, Queueing time: mean = 44.687 us, max = 285.967 us, min = 18.537 us, total = 1.117 ms
[state-dump] 	ObjectManager.ObjectAdded - 25 total (0 active), Execution time: mean = 6.450 us, total = 161.244 us, Queueing time: mean = 54.017 us, max = 364.526 us, min = 8.081 us, total = 1.350 ms
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig.HandleRequestImpl - 25 total (0 active), Execution time: mean = 29.747 us, total = 743.681 us, Queueing time: mean = 142.844 us, max = 2.629 ms, min = 7.599 us, total = 3.571 ms
[state-dump] 	ClusterResourceManager.ResetRemoteNodeView - 21 total (1 active), Execution time: mean = 6.362 us, total = 133.597 us, Queueing time: mean = 54.911 us, max = 73.859 us, min = 23.730 us, total = 1.153 ms
[state-dump] 	PeriodicalRunner.RunFnPeriodically - 14 total (0 active), Execution time: mean = 197.858 us, total = 2.770 ms, Queueing time: mean = 3.470 ms, max = 9.264 ms, min = 28.791 us, total = 48.577 ms
[state-dump] 	NodeManager.deadline_timer.record_metrics - 12 total (1 active), Execution time: mean = 586.002 us, total = 7.032 ms, Queueing time: mean = 791.067 us, max = 8.350 ms, min = 16.353 us, total = 9.493 ms
[state-dump] 	NodeManager.deadline_timer.debug_state_dump - 6 total (1 active), Execution time: mean = 2.569 ms, total = 15.414 ms, Queueing time: mean = 39.742 us, max = 64.938 us, min = 34.028 us, total = 238.450 us
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease - 2 total (0 active), Execution time: mean = 364.964 us, total = 729.928 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 428.071 ms, total = 856.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease.HandleRequestImpl - 2 total (0 active), Execution time: mean = 111.613 us, total = 223.226 us, Queueing time: mean = 31.811 us, max = 50.391 us, min = 13.231 us, total = 63.622 us
[state-dump] 	 - 2 total (0 active), Execution time: mean = 772.000 ns, total = 1.544 us, Queueing time: mean = 45.443 us, max = 71.510 us, min = 19.376 us, total = 90.886 us
[state-dump] 	WorkerPool.PopWorkerCallback - 2 total (0 active), Execution time: mean = 21.812 us, total = 43.624 us, Queueing time: mean = 9.826 us, max = 13.376 us, min = 6.277 us, total = 19.653 us
[state-dump] 	RaySyncerRegister - 2 total (0 active), Execution time: mean = 1.762 us, total = 3.523 us, Queueing time: mean = 147.500 ns, max = 247.000 ns, min = 48.000 ns, total = 295.000 ns
[state-dump] 	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 108.770 us, total = 217.541 us, Queueing time: mean = 200.000 ns, max = 223.000 ns, min = 177.000 ns, total = 400.000 ns
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 1.015 ms, total = 2.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 144.096 us, total = 288.193 us, Queueing time: mean = 929.080 us, max = 1.676 ms, min = 182.285 us, total = 1.858 ms
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive - 1 total (0 active), Execution time: mean = 823.588 us, total = 823.588 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 179.567 us, total = 179.567 us, Queueing time: mean = 9.133 us, max = 9.133 us, min = 9.133 us, total = 9.133 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.434 us, total = 13.434 us, Queueing time: mean = 12.023 us, max = 12.023 us, min = 12.023 us, total = 12.023 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.025 ms, total = 1.025 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GcsCheckAlive - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	Subscriber.HandlePublishedMessage_GCS_JOB_CHANNEL - 1 total (0 active), Execution time: mean = 29.045 us, total = 29.045 us, Queueing time: mean = 141.546 us, max = 141.546 us, min = 141.546 us, total = 141.546 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
[state-dump] 	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.deadline_timer.print_event_loop_stats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo - 1 total (0 active), Execution time: mean = 879.382 us, total = 879.382 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.314 us, total = 22.314 us, Queueing time: mean = 125.550 us, max = 125.550 us, min = 125.550 us, total = 125.550 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.569 us, total = 18.569 us, Queueing time: mean = 49.567 us, max = 49.567 us, min = 49.567 us, total = 49.567 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob - 1 total (0 active), Execution time: mean = 496.434 us, total = 496.434 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.715 us, total = 123.715 us, Queueing time: mean = 8.396 us, max = 8.396 us, min = 8.396 us, total = 8.396 us
[state-dump] DebugString() time ms: 1
[state-dump] 
[state-dump] 
[2025-07-05 18:43:58,925 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.3001 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:43:58,932 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:03,937 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:08,936 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2939 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:44:08,942 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:13,947 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:18,947 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2938 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:44:18,952 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:23,957 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:28,958 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2937 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:44:28,962 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:33,967 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:38,969 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2866 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:44:38,972 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:43,976 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:48,979 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2866 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:44:48,981 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:53,986 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:58,861 I 660838 660867] (raylet) store.cc:576: Plasma store debug dump: 
Current usage: 0 / 79.3075 GB
- num bytes created total: 200
0 pending objects of total size 0MB
- objects spillable: 0
- bytes spillable: 0
- objects unsealed: 0
- bytes unsealed: 0
- objects in use: 0
- bytes in use: 0
- objects evictable: 0
- bytes evictable: 0

- objects created by worker: 0
- bytes created by worker: 0
- objects restored: 0
- bytes restored: 0
- objects received: 0
- bytes received: 0
- objects errored: 0
- bytes errored: 0

[2025-07-05 18:44:58,883 I 660838 660838] (raylet) node_manager.cc:450: [state-dump] NodeManager:
[state-dump] Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[state-dump] Node name: ***********
[state-dump] InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:***********: 1, memory: 1.85051e+11}
[state-dump] ClusterTaskManager:
[state-dump] ========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
[state-dump] Infeasible queue length: 0
[state-dump] Schedule queue length: 0
[state-dump] Dispatch queue length: 0
[state-dump] num_waiting_for_resource: 0
[state-dump] num_waiting_for_plasma_memory: 0
[state-dump] num_waiting_for_remote_node_resources: 0
[state-dump] num_worker_not_started_by_job_config_not_exist: 0
[state-dump] num_worker_not_started_by_registration_timeout: 0
[state-dump] num_tasks_waiting_for_workers: 0
[state-dump] num_cancelled_tasks: 0
[state-dump] cluster_resource_scheduler state: 
[state-dump] Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "available": {CPU: [230000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [0, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 0 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, node:***********: 10000, GPU: 40000, memory: 1850507337730000, CPU: 240000, object_store_memory: 793074573310000, node:__internal_head__: 10000}}, "available": {object_store_memory: 793074573310000, node:***********: 10000, node:__internal_head__: 10000, memory: 1850507337730000, CPU: 230000, accelerator_type:G: 10000, GPU: 30000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
[state-dump] Waiting tasks size: 0
[state-dump] Number of executing tasks: 1
[state-dump] Number of pinned task arguments: 0
[state-dump] Number of total spilled tasks: 0
[state-dump] Number of spilled waiting tasks: 0
[state-dump] Number of spilled unschedulable tasks: 0
[state-dump] Resource usage {
[state-dump]     - (language=PYTHON actor_or_task=ResultCollector.__init__ pid=660927 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385): {}
[state-dump]     - (language=PYTHON actor_or_task=LLMEngine.__init__ pid=660939 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0): {GPU: 1, CPU: 1}
[state-dump] }
[state-dump] Backlog Size per scheduling descriptor :{workerId: num backlogs}:
[state-dump] 
[state-dump] Running tasks by scheduling class:
[state-dump]     - {depth=1 function_descriptor={type=PythonFunctionDescriptor, module_name=Ayo.engines.llm, class_name=LLMEngine, function_name=__init__, function_hash=e2f5af0ea8e345998125e6a621a10aec} scheduling_strategy=default_scheduling_strategy {
[state-dump] }
[state-dump]  resource_set={GPU : 1, CPU : 1, }}: 1/24
[state-dump] ==================================================
[state-dump] 
[state-dump] ClusterResources:
[state-dump] LocalObjectManager:
[state-dump] - num pinned objects: 0
[state-dump] - pinned objects size: 0
[state-dump] - num objects pending restore: 0
[state-dump] - num objects pending spill: 0
[state-dump] - num bytes pending spill: 0
[state-dump] - num bytes currently spilled: 0
[state-dump] - cumulative spill requests: 0
[state-dump] - cumulative restore requests: 0
[state-dump] - spilled objects pending delete: 0
[state-dump] 
[state-dump] ObjectManager:
[state-dump] - num local objects: 0
[state-dump] - num unfulfilled push requests: 0
[state-dump] - num object pull requests: 0
[state-dump] - num chunks received total: 0
[state-dump] - num chunks received failed (all): 0
[state-dump] - num chunks received failed / cancelled: 0
[state-dump] - num chunks received failed / plasma error: 0
[state-dump] Event stats:
[state-dump] Global stats: 0 total (0 active)
[state-dump] Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] Execution time:  mean = -nan s, total = 0.000 s
[state-dump] Event stats:
[state-dump] PushManager:
[state-dump] - num pushes in flight: 0
[state-dump] - num chunks in flight: 0
[state-dump] - num chunks remaining: 0
[state-dump] - max chunks allowed: 409
[state-dump] OwnershipBasedObjectDirectory:
[state-dump] - num listeners: 0
[state-dump] - cumulative location updates: 0
[state-dump] - num location updates per second: 0.000
[state-dump] - num location lookups per second: 0.000
[state-dump] - num locations added per second: 0.000
[state-dump] - num locations removed per second: 0.000
[state-dump] BufferPool:
[state-dump] - create buffer state map size: 0
[state-dump] PullManager:
[state-dump] - num bytes available for pulled objects: 79307457331
[state-dump] - num bytes being pulled (all): 0
[state-dump] - num bytes being pulled / pinned: 0
[state-dump] - get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - first get request bundle: N/A
[state-dump] - first wait request bundle: N/A
[state-dump] - first task request bundle: N/A
[state-dump] - num objects queued: 0
[state-dump] - num objects actively pulled (all): 0
[state-dump] - num objects actively pulled / pinned: 0
[state-dump] - num bundles being pulled: 0
[state-dump] - num pull retries: 0
[state-dump] - max timeout seconds: 0
[state-dump] - max timeout request is already processed. No entry.
[state-dump] 
[state-dump] WorkerPool:
[state-dump] - registered jobs: 1
[state-dump] - process_failed_job_config_missing: 0
[state-dump] - process_failed_rate_limited: 0
[state-dump] - process_failed_pending_registration: 0
[state-dump] - process_failed_runtime_env_setup_failed: 0
[state-dump] - num PYTHON workers: 24
[state-dump] - num PYTHON drivers: 1
[state-dump] - num PYTHON pending start requests: 0
[state-dump] - num PYTHON pending registration requests: 0
[state-dump] - num object spill callbacks queued: 0
[state-dump] - num object restore queued: 0
[state-dump] - num util functions queued: 0
[state-dump] - num idle workers: 22
[state-dump] TaskDependencyManager:
[state-dump] - task deps map size: 0
[state-dump] - get req map size: 0
[state-dump] - wait req map size: 0
[state-dump] - local objects map size: 0
[state-dump] WaitManager:
[state-dump] - num active wait requests: 0
[state-dump] Subscriber:
[state-dump] Channel WORKER_OBJECT_EVICTION
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_REF_REMOVED_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_OBJECT_LOCATIONS_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] num async plasma notifications: 0
[state-dump] Event stats:
[state-dump] Global stats: 11833 total (40 active)
[state-dump] Queueing time: mean = 184.331 us, max = 625.620 ms, min = 48.000 ns, total = 2.181 s
[state-dump] Execution time:  mean = 175.749 us, total = 2.080 s
[state-dump] Event stats:
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog.HandleRequestImpl - 3000 total (0 active), Execution time: mean = 26.076 us, total = 78.227 ms, Queueing time: mean = 39.894 us, max = 659.005 us, min = 4.063 us, total = 119.683 ms
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog - 3000 total (0 active), Execution time: mean = 261.870 us, total = 785.610 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckGC - 1199 total (1 active), Execution time: mean = 1.888 us, total = 2.264 ms, Queueing time: mean = 85.724 us, max = 25.832 ms, min = 7.559 us, total = 102.783 ms
[state-dump] 	RaySyncer.OnDemandBroadcasting - 1199 total (1 active), Execution time: mean = 6.287 us, total = 7.538 ms, Queueing time: mean = 81.752 us, max = 25.814 ms, min = 3.808 us, total = 98.021 ms
[state-dump] 	ObjectManager.UpdateAvailableMemory - 1198 total (0 active), Execution time: mean = 2.893 us, total = 3.466 ms, Queueing time: mean = 18.940 us, max = 212.831 us, min = 2.792 us, total = 22.691 ms
[state-dump] 	RayletWorkerPool.deadline_timer.kill_idle_workers - 600 total (1 active), Execution time: mean = 10.240 us, total = 6.144 ms, Queueing time: mean = 106.043 us, max = 30.900 ms, min = 12.787 us, total = 63.626 ms
[state-dump] 	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 480 total (1 active), Execution time: mean = 188.395 us, total = 90.430 ms, Queueing time: mean = 53.633 us, max = 113.387 us, min = 11.417 us, total = 25.744 ms
[state-dump] 	NodeManager.deadline_timer.spill_objects_when_over_threshold - 120 total (1 active), Execution time: mean = 2.133 us, total = 255.938 us, Queueing time: mean = 249.932 us, max = 8.796 ms, min = 15.710 us, total = 29.992 ms
[state-dump] 	NodeManager.CheckForUnexpectedWorkerDisconnects - 120 total (1 active), Execution time: mean = 46.569 us, total = 5.588 ms, Queueing time: mean = 37.972 us, max = 2.008 ms, min = 6.906 us, total = 4.557 ms
[state-dump] 	NodeManager.ScheduleAndDispatchTasks - 120 total (1 active), Execution time: mean = 11.346 us, total = 1.362 ms, Queueing time: mean = 72.009 us, max = 2.069 ms, min = 15.308 us, total = 8.641 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad - 120 total (0 active), Execution time: mean = 365.710 us, total = 43.885 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad.HandleRequestImpl - 120 total (0 active), Execution time: mean = 82.655 us, total = 9.919 ms, Queueing time: mean = 46.041 us, max = 87.484 us, min = 7.863 us, total = 5.525 ms
[state-dump] 	NodeManager.deadline_timer.flush_free_objects - 120 total (1 active), Execution time: mean = 6.996 us, total = 839.534 us, Queueing time: mean = 245.971 us, max = 8.799 ms, min = 15.861 us, total = 29.516 ms
[state-dump] 	ClientConnection.async_read.ProcessMessageHeader - 103 total (25 active), Execution time: mean = 2.777 us, total = 286.060 us, Queueing time: mean = 15.460 ms, max = 625.620 ms, min = 12.580 us, total = 1.592 s
[state-dump] 	ClientConnection.async_read.ProcessMessage - 78 total (0 active), Execution time: mean = 1.299 ms, total = 101.318 ms, Queueing time: mean = 12.499 us, max = 146.915 us, min = 2.738 us, total = 974.893 us
[state-dump] 	ClusterResourceManager.ResetRemoteNodeView - 41 total (1 active), Execution time: mean = 6.514 us, total = 267.073 us, Queueing time: mean = 54.383 us, max = 76.454 us, min = 23.730 us, total = 2.230 ms
[state-dump] 	ClientConnection.async_write.DoAsyncWrites - 26 total (0 active), Execution time: mean = 525.308 ns, total = 13.658 us, Queueing time: mean = 16.479 us, max = 51.598 us, min = 8.590 us, total = 428.462 us
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig - 25 total (0 active), Execution time: mean = 361.343 us, total = 9.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ObjectManager.ObjectAdded - 25 total (0 active), Execution time: mean = 6.450 us, total = 161.244 us, Queueing time: mean = 54.017 us, max = 364.526 us, min = 8.081 us, total = 1.350 ms
[state-dump] 	ObjectManager.ObjectDeleted - 25 total (0 active), Execution time: mean = 9.360 us, total = 234.005 us, Queueing time: mean = 44.687 us, max = 285.967 us, min = 18.537 us, total = 1.117 ms
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig.HandleRequestImpl - 25 total (0 active), Execution time: mean = 29.747 us, total = 743.681 us, Queueing time: mean = 142.844 us, max = 2.629 ms, min = 7.599 us, total = 3.571 ms
[state-dump] 	NodeManager.deadline_timer.record_metrics - 24 total (1 active), Execution time: mean = 638.691 us, total = 15.329 ms, Queueing time: mean = 612.168 us, max = 8.350 ms, min = 16.353 us, total = 14.692 ms
[state-dump] 	PeriodicalRunner.RunFnPeriodically - 14 total (0 active), Execution time: mean = 197.858 us, total = 2.770 ms, Queueing time: mean = 3.470 ms, max = 9.264 ms, min = 28.791 us, total = 48.577 ms
[state-dump] 	NodeManager.deadline_timer.debug_state_dump - 12 total (1 active), Execution time: mean = 2.407 ms, total = 28.883 ms, Queueing time: mean = 47.902 us, max = 67.134 us, min = 22.784 us, total = 574.824 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 144.096 us, total = 288.193 us, Queueing time: mean = 929.080 us, max = 1.676 ms, min = 182.285 us, total = 1.858 ms
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive.OnReplyReceived - 2 total (0 active), Execution time: mean = 27.919 us, total = 55.838 us, Queueing time: mean = 33.783 us, max = 55.544 us, min = 12.023 us, total = 67.567 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive - 2 total (0 active), Execution time: mean = 825.927 us, total = 1.652 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GcsCheckAlive - 2 total (1 active), Execution time: mean = 124.135 us, total = 248.271 us, Queueing time: mean = 1.002 ms, max = 2.004 ms, min = 2.004 ms, total = 2.004 ms
[state-dump] 	NodeManager.deadline_timer.print_event_loop_stats - 2 total (1 active, 1 running), Execution time: mean = 1.030 ms, total = 2.061 ms, Queueing time: mean = 21.377 us, max = 42.753 us, min = 42.753 us, total = 42.753 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 428.071 ms, total = 856.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease.HandleRequestImpl - 2 total (0 active), Execution time: mean = 111.613 us, total = 223.226 us, Queueing time: mean = 31.811 us, max = 50.391 us, min = 13.231 us, total = 63.622 us
[state-dump] 	 - 2 total (0 active), Execution time: mean = 772.000 ns, total = 1.544 us, Queueing time: mean = 45.443 us, max = 71.510 us, min = 19.376 us, total = 90.886 us
[state-dump] 	WorkerPool.PopWorkerCallback - 2 total (0 active), Execution time: mean = 21.812 us, total = 43.624 us, Queueing time: mean = 9.826 us, max = 13.376 us, min = 6.277 us, total = 19.653 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 1.015 ms, total = 2.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	RaySyncerRegister - 2 total (0 active), Execution time: mean = 1.762 us, total = 3.523 us, Queueing time: mean = 147.500 ns, max = 247.000 ns, min = 48.000 ns, total = 295.000 ns
[state-dump] 	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 108.770 us, total = 217.541 us, Queueing time: mean = 200.000 ns, max = 223.000 ns, min = 177.000 ns, total = 400.000 ns
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease - 2 total (0 active), Execution time: mean = 364.964 us, total = 729.928 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.715 us, total = 123.715 us, Queueing time: mean = 8.396 us, max = 8.396 us, min = 8.396 us, total = 8.396 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.025 ms, total = 1.025 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 179.567 us, total = 179.567 us, Queueing time: mean = 9.133 us, max = 9.133 us, min = 9.133 us, total = 9.133 us
[state-dump] 	Subscriber.HandlePublishedMessage_GCS_JOB_CHANNEL - 1 total (0 active), Execution time: mean = 29.045 us, total = 29.045 us, Queueing time: mean = 141.546 us, max = 141.546 us, min = 141.546 us, total = 141.546 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob - 1 total (0 active), Execution time: mean = 496.434 us, total = 496.434 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.569 us, total = 18.569 us, Queueing time: mean = 49.567 us, max = 49.567 us, min = 49.567 us, total = 49.567 us
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.314 us, total = 22.314 us, Queueing time: mean = 125.550 us, max = 125.550 us, min = 125.550 us, total = 125.550 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo - 1 total (0 active), Execution time: mean = 879.382 us, total = 879.382 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] DebugString() time ms: 1
[state-dump] 
[state-dump] 
[2025-07-05 18:44:58,990 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:44:58,990 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2864 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:45:03,995 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:09,000 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:09,001 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2944 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:45:14,005 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:19,010 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:19,013 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2858 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:45:24,015 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:29,019 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:29,023 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2689 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:45:34,024 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:39,029 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:39,034 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2956 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:45:44,034 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:49,039 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:49,045 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2831 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:45:54,044 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:58,861 I 660838 660867] (raylet) store.cc:576: Plasma store debug dump: 
Current usage: 0 / 79.3075 GB
- num bytes created total: 200
0 pending objects of total size 0MB
- objects spillable: 0
- bytes spillable: 0
- objects unsealed: 0
- bytes unsealed: 0
- objects in use: 0
- bytes in use: 0
- objects evictable: 0
- bytes evictable: 0

- objects created by worker: 0
- bytes created by worker: 0
- objects restored: 0
- bytes restored: 0
- objects received: 0
- bytes received: 0
- objects errored: 0
- bytes errored: 0

[2025-07-05 18:45:58,885 I 660838 660838] (raylet) node_manager.cc:450: [state-dump] NodeManager:
[state-dump] Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[state-dump] Node name: ***********
[state-dump] InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:***********: 1, memory: 1.85051e+11}
[state-dump] ClusterTaskManager:
[state-dump] ========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
[state-dump] Infeasible queue length: 0
[state-dump] Schedule queue length: 0
[state-dump] Dispatch queue length: 0
[state-dump] num_waiting_for_resource: 0
[state-dump] num_waiting_for_plasma_memory: 0
[state-dump] num_waiting_for_remote_node_resources: 0
[state-dump] num_worker_not_started_by_job_config_not_exist: 0
[state-dump] num_worker_not_started_by_registration_timeout: 0
[state-dump] num_tasks_waiting_for_workers: 0
[state-dump] num_cancelled_tasks: 0
[state-dump] cluster_resource_scheduler state: 
[state-dump] Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "available": {CPU: [230000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [0, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 0 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, node:***********: 10000, GPU: 40000, memory: 1850507337730000, CPU: 240000, object_store_memory: 793074573310000, node:__internal_head__: 10000}}, "available": {object_store_memory: 793074573310000, node:***********: 10000, node:__internal_head__: 10000, memory: 1850507337730000, CPU: 230000, accelerator_type:G: 10000, GPU: 30000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
[state-dump] Waiting tasks size: 0
[state-dump] Number of executing tasks: 1
[state-dump] Number of pinned task arguments: 0
[state-dump] Number of total spilled tasks: 0
[state-dump] Number of spilled waiting tasks: 0
[state-dump] Number of spilled unschedulable tasks: 0
[state-dump] Resource usage {
[state-dump]     - (language=PYTHON actor_or_task=ResultCollector.__init__ pid=660927 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385): {}
[state-dump]     - (language=PYTHON actor_or_task=LLMEngine.__init__ pid=660939 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0): {GPU: 1, CPU: 1}
[state-dump] }
[state-dump] Backlog Size per scheduling descriptor :{workerId: num backlogs}:
[state-dump] 
[state-dump] Running tasks by scheduling class:
[state-dump]     - {depth=1 function_descriptor={type=PythonFunctionDescriptor, module_name=Ayo.engines.llm, class_name=LLMEngine, function_name=__init__, function_hash=e2f5af0ea8e345998125e6a621a10aec} scheduling_strategy=default_scheduling_strategy {
[state-dump] }
[state-dump]  resource_set={CPU : 1, GPU : 1, }}: 1/24
[state-dump] ==================================================
[state-dump] 
[state-dump] ClusterResources:
[state-dump] LocalObjectManager:
[state-dump] - num pinned objects: 0
[state-dump] - pinned objects size: 0
[state-dump] - num objects pending restore: 0
[state-dump] - num objects pending spill: 0
[state-dump] - num bytes pending spill: 0
[state-dump] - num bytes currently spilled: 0
[state-dump] - cumulative spill requests: 0
[state-dump] - cumulative restore requests: 0
[state-dump] - spilled objects pending delete: 0
[state-dump] 
[state-dump] ObjectManager:
[state-dump] - num local objects: 0
[state-dump] - num unfulfilled push requests: 0
[state-dump] - num object pull requests: 0
[state-dump] - num chunks received total: 0
[state-dump] - num chunks received failed (all): 0
[state-dump] - num chunks received failed / cancelled: 0
[state-dump] - num chunks received failed / plasma error: 0
[state-dump] Event stats:
[state-dump] Global stats: 0 total (0 active)
[state-dump] Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] Execution time:  mean = -nan s, total = 0.000 s
[state-dump] Event stats:
[state-dump] PushManager:
[state-dump] - num pushes in flight: 0
[state-dump] - num chunks in flight: 0
[state-dump] - num chunks remaining: 0
[state-dump] - max chunks allowed: 409
[state-dump] OwnershipBasedObjectDirectory:
[state-dump] - num listeners: 0
[state-dump] - cumulative location updates: 0
[state-dump] - num location updates per second: 0.000
[state-dump] - num location lookups per second: 0.000
[state-dump] - num locations added per second: 0.000
[state-dump] - num locations removed per second: 0.000
[state-dump] BufferPool:
[state-dump] - create buffer state map size: 0
[state-dump] PullManager:
[state-dump] - num bytes available for pulled objects: 79307457331
[state-dump] - num bytes being pulled (all): 0
[state-dump] - num bytes being pulled / pinned: 0
[state-dump] - get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - first get request bundle: N/A
[state-dump] - first wait request bundle: N/A
[state-dump] - first task request bundle: N/A
[state-dump] - num objects queued: 0
[state-dump] - num objects actively pulled (all): 0
[state-dump] - num objects actively pulled / pinned: 0
[state-dump] - num bundles being pulled: 0
[state-dump] - num pull retries: 0
[state-dump] - max timeout seconds: 0
[state-dump] - max timeout request is already processed. No entry.
[state-dump] 
[state-dump] WorkerPool:
[state-dump] - registered jobs: 1
[state-dump] - process_failed_job_config_missing: 0
[state-dump] - process_failed_rate_limited: 0
[state-dump] - process_failed_pending_registration: 0
[state-dump] - process_failed_runtime_env_setup_failed: 0
[state-dump] - num PYTHON workers: 24
[state-dump] - num PYTHON drivers: 1
[state-dump] - num PYTHON pending start requests: 0
[state-dump] - num PYTHON pending registration requests: 0
[state-dump] - num object spill callbacks queued: 0
[state-dump] - num object restore queued: 0
[state-dump] - num util functions queued: 0
[state-dump] - num idle workers: 22
[state-dump] TaskDependencyManager:
[state-dump] - task deps map size: 0
[state-dump] - get req map size: 0
[state-dump] - wait req map size: 0
[state-dump] - local objects map size: 0
[state-dump] WaitManager:
[state-dump] - num active wait requests: 0
[state-dump] Subscriber:
[state-dump] Channel WORKER_OBJECT_EVICTION
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_REF_REMOVED_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_OBJECT_LOCATIONS_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] num async plasma notifications: 0
[state-dump] Event stats:
[state-dump] Global stats: 17575 total (40 active)
[state-dump] Queueing time: mean = 136.119 us, max = 625.620 ms, min = 48.000 ns, total = 2.392 s
[state-dump] Execution time:  mean = 149.410 us, total = 2.626 s
[state-dump] Event stats:
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog.HandleRequestImpl - 4500 total (0 active), Execution time: mean = 26.290 us, total = 118.304 ms, Queueing time: mean = 39.647 us, max = 659.005 us, min = 4.063 us, total = 178.411 ms
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog - 4500 total (0 active), Execution time: mean = 263.405 us, total = 1.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckGC - 1799 total (1 active), Execution time: mean = 1.889 us, total = 3.398 ms, Queueing time: mean = 80.139 us, max = 25.832 ms, min = 7.559 us, total = 144.171 ms
[state-dump] 	RaySyncer.OnDemandBroadcasting - 1799 total (1 active), Execution time: mean = 6.240 us, total = 11.227 ms, Queueing time: mean = 76.209 us, max = 25.814 ms, min = 3.808 us, total = 137.100 ms
[state-dump] 	ObjectManager.UpdateAvailableMemory - 1798 total (0 active), Execution time: mean = 2.897 us, total = 5.208 ms, Queueing time: mean = 19.306 us, max = 627.487 us, min = 2.792 us, total = 34.712 ms
[state-dump] 	RayletWorkerPool.deadline_timer.kill_idle_workers - 900 total (1 active), Execution time: mean = 10.296 us, total = 9.266 ms, Queueing time: mean = 89.230 us, max = 30.900 ms, min = 12.787 us, total = 80.307 ms
[state-dump] 	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 720 total (1 active), Execution time: mean = 190.185 us, total = 136.933 ms, Queueing time: mean = 53.403 us, max = 114.882 us, min = 11.417 us, total = 38.450 ms
[state-dump] 	NodeManager.ScheduleAndDispatchTasks - 180 total (1 active), Execution time: mean = 11.818 us, total = 2.127 ms, Queueing time: mean = 65.011 us, max = 2.069 ms, min = 15.308 us, total = 11.702 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad - 180 total (0 active), Execution time: mean = 365.312 us, total = 65.756 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckForUnexpectedWorkerDisconnects - 180 total (1 active), Execution time: mean = 47.500 us, total = 8.550 ms, Queueing time: mean = 31.651 us, max = 2.008 ms, min = 6.906 us, total = 5.697 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad.HandleRequestImpl - 180 total (0 active), Execution time: mean = 83.591 us, total = 15.046 ms, Queueing time: mean = 44.660 us, max = 87.484 us, min = 7.863 us, total = 8.039 ms
[state-dump] 	NodeManager.deadline_timer.flush_free_objects - 180 total (1 active), Execution time: mean = 7.338 us, total = 1.321 ms, Queueing time: mean = 213.045 us, max = 8.799 ms, min = 15.861 us, total = 38.348 ms
[state-dump] 	NodeManager.deadline_timer.spill_objects_when_over_threshold - 180 total (1 active), Execution time: mean = 2.181 us, total = 392.556 us, Queueing time: mean = 217.236 us, max = 8.796 ms, min = 15.710 us, total = 39.102 ms
[state-dump] 	ClientConnection.async_read.ProcessMessageHeader - 103 total (25 active), Execution time: mean = 2.777 us, total = 286.060 us, Queueing time: mean = 15.460 ms, max = 625.620 ms, min = 12.580 us, total = 1.592 s
[state-dump] 	ClientConnection.async_read.ProcessMessage - 78 total (0 active), Execution time: mean = 1.299 ms, total = 101.318 ms, Queueing time: mean = 12.499 us, max = 146.915 us, min = 2.738 us, total = 974.893 us
[state-dump] 	ClusterResourceManager.ResetRemoteNodeView - 61 total (1 active), Execution time: mean = 6.753 us, total = 411.905 us, Queueing time: mean = 56.185 us, max = 94.760 us, min = 23.730 us, total = 3.427 ms
[state-dump] 	NodeManager.deadline_timer.record_metrics - 36 total (1 active), Execution time: mean = 616.463 us, total = 22.193 ms, Queueing time: mean = 474.181 us, max = 8.350 ms, min = 16.353 us, total = 17.071 ms
[state-dump] 	ClientConnection.async_write.DoAsyncWrites - 26 total (0 active), Execution time: mean = 525.308 ns, total = 13.658 us, Queueing time: mean = 16.479 us, max = 51.598 us, min = 8.590 us, total = 428.462 us
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig - 25 total (0 active), Execution time: mean = 361.343 us, total = 9.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ObjectManager.ObjectDeleted - 25 total (0 active), Execution time: mean = 9.360 us, total = 234.005 us, Queueing time: mean = 44.687 us, max = 285.967 us, min = 18.537 us, total = 1.117 ms
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig.HandleRequestImpl - 25 total (0 active), Execution time: mean = 29.747 us, total = 743.681 us, Queueing time: mean = 142.844 us, max = 2.629 ms, min = 7.599 us, total = 3.571 ms
[state-dump] 	ObjectManager.ObjectAdded - 25 total (0 active), Execution time: mean = 6.450 us, total = 161.244 us, Queueing time: mean = 54.017 us, max = 364.526 us, min = 8.081 us, total = 1.350 ms
[state-dump] 	NodeManager.deadline_timer.debug_state_dump - 18 total (1 active), Execution time: mean = 2.089 ms, total = 37.607 ms, Queueing time: mean = 46.368 us, max = 75.736 us, min = 21.936 us, total = 834.628 us
[state-dump] 	PeriodicalRunner.RunFnPeriodically - 14 total (0 active), Execution time: mean = 197.858 us, total = 2.770 ms, Queueing time: mean = 3.470 ms, max = 9.264 ms, min = 28.791 us, total = 48.577 ms
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive.OnReplyReceived - 3 total (0 active), Execution time: mean = 31.594 us, total = 94.782 us, Queueing time: mean = 43.382 us, max = 62.580 us, min = 12.023 us, total = 130.147 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive - 3 total (0 active), Execution time: mean = 844.991 us, total = 2.535 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GcsCheckAlive - 3 total (1 active), Execution time: mean = 154.759 us, total = 464.276 us, Queueing time: mean = 1.293 ms, max = 2.004 ms, min = 1.874 ms, total = 3.878 ms
[state-dump] 	NodeManager.deadline_timer.print_event_loop_stats - 3 total (1 active, 1 running), Execution time: mean = 1.374 ms, total = 4.121 ms, Queueing time: mean = 35.688 us, max = 64.311 us, min = 42.753 us, total = 107.064 us
[state-dump] 	RaySyncerRegister - 2 total (0 active), Execution time: mean = 1.762 us, total = 3.523 us, Queueing time: mean = 147.500 ns, max = 247.000 ns, min = 48.000 ns, total = 295.000 ns
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease - 2 total (0 active), Execution time: mean = 364.964 us, total = 729.928 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	WorkerPool.PopWorkerCallback - 2 total (0 active), Execution time: mean = 21.812 us, total = 43.624 us, Queueing time: mean = 9.826 us, max = 13.376 us, min = 6.277 us, total = 19.653 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 144.096 us, total = 288.193 us, Queueing time: mean = 929.080 us, max = 1.676 ms, min = 182.285 us, total = 1.858 ms
[state-dump] 	 - 2 total (0 active), Execution time: mean = 772.000 ns, total = 1.544 us, Queueing time: mean = 45.443 us, max = 71.510 us, min = 19.376 us, total = 90.886 us
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease.HandleRequestImpl - 2 total (0 active), Execution time: mean = 111.613 us, total = 223.226 us, Queueing time: mean = 31.811 us, max = 50.391 us, min = 13.231 us, total = 63.622 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 428.071 ms, total = 856.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 108.770 us, total = 217.541 us, Queueing time: mean = 200.000 ns, max = 223.000 ns, min = 177.000 ns, total = 400.000 ns
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 1.015 ms, total = 2.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.715 us, total = 123.715 us, Queueing time: mean = 8.396 us, max = 8.396 us, min = 8.396 us, total = 8.396 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob - 1 total (0 active), Execution time: mean = 496.434 us, total = 496.434 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.025 ms, total = 1.025 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 179.567 us, total = 179.567 us, Queueing time: mean = 9.133 us, max = 9.133 us, min = 9.133 us, total = 9.133 us
[state-dump] 	Subscriber.HandlePublishedMessage_GCS_JOB_CHANNEL - 1 total (0 active), Execution time: mean = 29.045 us, total = 29.045 us, Queueing time: mean = 141.546 us, max = 141.546 us, min = 141.546 us, total = 141.546 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
[state-dump] 	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo - 1 total (0 active), Execution time: mean = 879.382 us, total = 879.382 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.314 us, total = 22.314 us, Queueing time: mean = 125.550 us, max = 125.550 us, min = 125.550 us, total = 125.550 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.569 us, total = 18.569 us, Queueing time: mean = 49.567 us, max = 49.567 us, min = 49.567 us, total = 49.567 us
[state-dump] DebugString() time ms: 1
[state-dump] 
[state-dump] 
[2025-07-05 18:45:59,049 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:45:59,055 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.3077 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:46:04,054 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:09,059 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:09,066 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2881 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:46:14,064 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:19,069 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:19,077 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.288 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:46:24,074 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:29,078 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:29,088 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2878 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:46:34,083 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:39,088 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:39,099 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.3072 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:46:44,093 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:49,098 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:49,110 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2862 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:46:54,103 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:58,862 I 660838 660867] (raylet) store.cc:576: Plasma store debug dump: 
Current usage: 0 / 79.3075 GB
- num bytes created total: 200
0 pending objects of total size 0MB
- objects spillable: 0
- bytes spillable: 0
- objects unsealed: 0
- bytes unsealed: 0
- objects in use: 0
- bytes in use: 0
- objects evictable: 0
- bytes evictable: 0

- objects created by worker: 0
- bytes created by worker: 0
- objects restored: 0
- bytes restored: 0
- objects received: 0
- bytes received: 0
- objects errored: 0
- bytes errored: 0

[2025-07-05 18:46:58,887 I 660838 660838] (raylet) node_manager.cc:450: [state-dump] NodeManager:
[state-dump] Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[state-dump] Node name: ***********
[state-dump] InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:***********: 1, memory: 1.85051e+11}
[state-dump] ClusterTaskManager:
[state-dump] ========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
[state-dump] Infeasible queue length: 0
[state-dump] Schedule queue length: 0
[state-dump] Dispatch queue length: 0
[state-dump] num_waiting_for_resource: 0
[state-dump] num_waiting_for_plasma_memory: 0
[state-dump] num_waiting_for_remote_node_resources: 0
[state-dump] num_worker_not_started_by_job_config_not_exist: 0
[state-dump] num_worker_not_started_by_registration_timeout: 0
[state-dump] num_tasks_waiting_for_workers: 0
[state-dump] num_cancelled_tasks: 0
[state-dump] cluster_resource_scheduler state: 
[state-dump] Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "available": {CPU: [230000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [0, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 0 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, node:***********: 10000, GPU: 40000, memory: 1850507337730000, CPU: 240000, object_store_memory: 793074573310000, node:__internal_head__: 10000}}, "available": {object_store_memory: 793074573310000, node:***********: 10000, node:__internal_head__: 10000, memory: 1850507337730000, CPU: 230000, accelerator_type:G: 10000, GPU: 30000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
[state-dump] Waiting tasks size: 0
[state-dump] Number of executing tasks: 1
[state-dump] Number of pinned task arguments: 0
[state-dump] Number of total spilled tasks: 0
[state-dump] Number of spilled waiting tasks: 0
[state-dump] Number of spilled unschedulable tasks: 0
[state-dump] Resource usage {
[state-dump]     - (language=PYTHON actor_or_task=ResultCollector.__init__ pid=660927 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385): {}
[state-dump]     - (language=PYTHON actor_or_task=LLMEngine.__init__ pid=660939 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0): {GPU: 1, CPU: 1}
[state-dump] }
[state-dump] Backlog Size per scheduling descriptor :{workerId: num backlogs}:
[state-dump] 
[state-dump] Running tasks by scheduling class:
[state-dump]     - {depth=1 function_descriptor={type=PythonFunctionDescriptor, module_name=Ayo.engines.llm, class_name=LLMEngine, function_name=__init__, function_hash=e2f5af0ea8e345998125e6a621a10aec} scheduling_strategy=default_scheduling_strategy {
[state-dump] }
[state-dump]  resource_set={GPU : 1, CPU : 1, }}: 1/24
[state-dump] ==================================================
[state-dump] 
[state-dump] ClusterResources:
[state-dump] LocalObjectManager:
[state-dump] - num pinned objects: 0
[state-dump] - pinned objects size: 0
[state-dump] - num objects pending restore: 0
[state-dump] - num objects pending spill: 0
[state-dump] - num bytes pending spill: 0
[state-dump] - num bytes currently spilled: 0
[state-dump] - cumulative spill requests: 0
[state-dump] - cumulative restore requests: 0
[state-dump] - spilled objects pending delete: 0
[state-dump] 
[state-dump] ObjectManager:
[state-dump] - num local objects: 0
[state-dump] - num unfulfilled push requests: 0
[state-dump] - num object pull requests: 0
[state-dump] - num chunks received total: 0
[state-dump] - num chunks received failed (all): 0
[state-dump] - num chunks received failed / cancelled: 0
[state-dump] - num chunks received failed / plasma error: 0
[state-dump] Event stats:
[state-dump] Global stats: 0 total (0 active)
[state-dump] Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] Execution time:  mean = -nan s, total = 0.000 s
[state-dump] Event stats:
[state-dump] PushManager:
[state-dump] - num pushes in flight: 0
[state-dump] - num chunks in flight: 0
[state-dump] - num chunks remaining: 0
[state-dump] - max chunks allowed: 409
[state-dump] OwnershipBasedObjectDirectory:
[state-dump] - num listeners: 0
[state-dump] - cumulative location updates: 0
[state-dump] - num location updates per second: 0.000
[state-dump] - num location lookups per second: 0.000
[state-dump] - num locations added per second: 0.000
[state-dump] - num locations removed per second: 0.000
[state-dump] BufferPool:
[state-dump] - create buffer state map size: 0
[state-dump] PullManager:
[state-dump] - num bytes available for pulled objects: 79307457331
[state-dump] - num bytes being pulled (all): 0
[state-dump] - num bytes being pulled / pinned: 0
[state-dump] - get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - first get request bundle: N/A
[state-dump] - first wait request bundle: N/A
[state-dump] - first task request bundle: N/A
[state-dump] - num objects queued: 0
[state-dump] - num objects actively pulled (all): 0
[state-dump] - num objects actively pulled / pinned: 0
[state-dump] - num bundles being pulled: 0
[state-dump] - num pull retries: 0
[state-dump] - max timeout seconds: 0
[state-dump] - max timeout request is already processed. No entry.
[state-dump] 
[state-dump] WorkerPool:
[state-dump] - registered jobs: 1
[state-dump] - process_failed_job_config_missing: 0
[state-dump] - process_failed_rate_limited: 0
[state-dump] - process_failed_pending_registration: 0
[state-dump] - process_failed_runtime_env_setup_failed: 0
[state-dump] - num PYTHON workers: 24
[state-dump] - num PYTHON drivers: 1
[state-dump] - num PYTHON pending start requests: 0
[state-dump] - num PYTHON pending registration requests: 0
[state-dump] - num object spill callbacks queued: 0
[state-dump] - num object restore queued: 0
[state-dump] - num util functions queued: 0
[state-dump] - num idle workers: 22
[state-dump] TaskDependencyManager:
[state-dump] - task deps map size: 0
[state-dump] - get req map size: 0
[state-dump] - wait req map size: 0
[state-dump] - local objects map size: 0
[state-dump] WaitManager:
[state-dump] - num active wait requests: 0
[state-dump] Subscriber:
[state-dump] Channel WORKER_OBJECT_EVICTION
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_REF_REMOVED_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_OBJECT_LOCATIONS_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] num async plasma notifications: 0
[state-dump] Event stats:
[state-dump] Global stats: 23317 total (40 active)
[state-dump] Queueing time: mean = 111.761 us, max = 625.620 ms, min = -0.000 s, total = 2.606 s
[state-dump] Execution time:  mean = 136.299 us, total = 3.178 s
[state-dump] Event stats:
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog.HandleRequestImpl - 6000 total (0 active), Execution time: mean = 26.120 us, total = 156.721 ms, Queueing time: mean = 39.557 us, max = 659.005 us, min = 4.063 us, total = 237.341 ms
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog - 6000 total (0 active), Execution time: mean = 265.570 us, total = 1.593 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckGC - 2399 total (1 active), Execution time: mean = 1.902 us, total = 4.563 ms, Queueing time: mean = 77.537 us, max = 25.832 ms, min = 7.559 us, total = 186.011 ms
[state-dump] 	RaySyncer.OnDemandBroadcasting - 2399 total (1 active), Execution time: mean = 6.229 us, total = 14.944 ms, Queueing time: mean = 73.616 us, max = 25.814 ms, min = 3.808 us, total = 176.604 ms
[state-dump] 	ObjectManager.UpdateAvailableMemory - 2398 total (0 active), Execution time: mean = 2.868 us, total = 6.878 ms, Queueing time: mean = 19.262 us, max = 627.487 us, min = 2.792 us, total = 46.190 ms
[state-dump] 	RayletWorkerPool.deadline_timer.kill_idle_workers - 1200 total (1 active), Execution time: mean = 10.261 us, total = 12.313 ms, Queueing time: mean = 80.922 us, max = 30.900 ms, min = 12.787 us, total = 97.106 ms
[state-dump] 	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 960 total (1 active), Execution time: mean = 190.110 us, total = 182.506 ms, Queueing time: mean = 54.326 us, max = 115.114 us, min = -0.000 s, total = 52.153 ms
[state-dump] 	NodeManager.ScheduleAndDispatchTasks - 240 total (1 active), Execution time: mean = 11.722 us, total = 2.813 ms, Queueing time: mean = 63.090 us, max = 2.069 ms, min = 15.308 us, total = 15.142 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad - 240 total (0 active), Execution time: mean = 368.750 us, total = 88.500 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckForUnexpectedWorkerDisconnects - 240 total (1 active), Execution time: mean = 47.707 us, total = 11.450 ms, Queueing time: mean = 35.870 us, max = 2.008 ms, min = 6.906 us, total = 8.609 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad.HandleRequestImpl - 240 total (0 active), Execution time: mean = 83.398 us, total = 20.016 ms, Queueing time: mean = 45.022 us, max = 87.484 us, min = 7.863 us, total = 10.805 ms
[state-dump] 	NodeManager.deadline_timer.flush_free_objects - 240 total (1 active), Execution time: mean = 7.206 us, total = 1.729 ms, Queueing time: mean = 194.897 us, max = 8.799 ms, min = 15.861 us, total = 46.775 ms
[state-dump] 	NodeManager.deadline_timer.spill_objects_when_over_threshold - 240 total (1 active), Execution time: mean = 2.157 us, total = 517.626 us, Queueing time: mean = 198.948 us, max = 8.796 ms, min = 15.710 us, total = 47.747 ms
[state-dump] 	ClientConnection.async_read.ProcessMessageHeader - 103 total (25 active), Execution time: mean = 2.777 us, total = 286.060 us, Queueing time: mean = 15.460 ms, max = 625.620 ms, min = 12.580 us, total = 1.592 s
[state-dump] 	ClusterResourceManager.ResetRemoteNodeView - 81 total (1 active), Execution time: mean = 6.871 us, total = 556.529 us, Queueing time: mean = 56.444 us, max = 101.828 us, min = 21.085 us, total = 4.572 ms
[state-dump] 	ClientConnection.async_read.ProcessMessage - 78 total (0 active), Execution time: mean = 1.299 ms, total = 101.318 ms, Queueing time: mean = 12.499 us, max = 146.915 us, min = 2.738 us, total = 974.893 us
[state-dump] 	NodeManager.deadline_timer.record_metrics - 48 total (1 active), Execution time: mean = 604.810 us, total = 29.031 ms, Queueing time: mean = 394.075 us, max = 8.350 ms, min = 16.353 us, total = 18.916 ms
[state-dump] 	ClientConnection.async_write.DoAsyncWrites - 26 total (0 active), Execution time: mean = 525.308 ns, total = 13.658 us, Queueing time: mean = 16.479 us, max = 51.598 us, min = 8.590 us, total = 428.462 us
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig - 25 total (0 active), Execution time: mean = 361.343 us, total = 9.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ObjectManager.ObjectDeleted - 25 total (0 active), Execution time: mean = 9.360 us, total = 234.005 us, Queueing time: mean = 44.687 us, max = 285.967 us, min = 18.537 us, total = 1.117 ms
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig.HandleRequestImpl - 25 total (0 active), Execution time: mean = 29.747 us, total = 743.681 us, Queueing time: mean = 142.844 us, max = 2.629 ms, min = 7.599 us, total = 3.571 ms
[state-dump] 	ObjectManager.ObjectAdded - 25 total (0 active), Execution time: mean = 6.450 us, total = 161.244 us, Queueing time: mean = 54.017 us, max = 364.526 us, min = 8.081 us, total = 1.350 ms
[state-dump] 	NodeManager.deadline_timer.debug_state_dump - 24 total (1 active), Execution time: mean = 1.922 ms, total = 46.118 ms, Queueing time: mean = 48.151 us, max = 111.692 us, min = 21.936 us, total = 1.156 ms
[state-dump] 	PeriodicalRunner.RunFnPeriodically - 14 total (0 active), Execution time: mean = 197.858 us, total = 2.770 ms, Queueing time: mean = 3.470 ms, max = 9.264 ms, min = 28.791 us, total = 48.577 ms
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive.OnReplyReceived - 4 total (0 active), Execution time: mean = 34.100 us, total = 136.399 us, Queueing time: mean = 38.621 us, max = 62.580 us, min = 12.023 us, total = 154.484 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive - 4 total (0 active), Execution time: mean = 855.882 us, total = 3.424 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GcsCheckAlive - 4 total (1 active), Execution time: mean = 174.668 us, total = 698.671 us, Queueing time: mean = 1.429 ms, max = 2.004 ms, min = 1.837 ms, total = 5.715 ms
[state-dump] 	NodeManager.deadline_timer.print_event_loop_stats - 4 total (1 active, 1 running), Execution time: mean = 1.534 ms, total = 6.137 ms, Queueing time: mean = 36.089 us, max = 64.311 us, min = 37.290 us, total = 144.354 us
[state-dump] 	RaySyncerRegister - 2 total (0 active), Execution time: mean = 1.762 us, total = 3.523 us, Queueing time: mean = 147.500 ns, max = 247.000 ns, min = 48.000 ns, total = 295.000 ns
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease - 2 total (0 active), Execution time: mean = 364.964 us, total = 729.928 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	WorkerPool.PopWorkerCallback - 2 total (0 active), Execution time: mean = 21.812 us, total = 43.624 us, Queueing time: mean = 9.826 us, max = 13.376 us, min = 6.277 us, total = 19.653 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 144.096 us, total = 288.193 us, Queueing time: mean = 929.080 us, max = 1.676 ms, min = 182.285 us, total = 1.858 ms
[state-dump] 	 - 2 total (0 active), Execution time: mean = 772.000 ns, total = 1.544 us, Queueing time: mean = 45.443 us, max = 71.510 us, min = 19.376 us, total = 90.886 us
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease.HandleRequestImpl - 2 total (0 active), Execution time: mean = 111.613 us, total = 223.226 us, Queueing time: mean = 31.811 us, max = 50.391 us, min = 13.231 us, total = 63.622 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 428.071 ms, total = 856.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 108.770 us, total = 217.541 us, Queueing time: mean = 200.000 ns, max = 223.000 ns, min = 177.000 ns, total = 400.000 ns
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 1.015 ms, total = 2.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.715 us, total = 123.715 us, Queueing time: mean = 8.396 us, max = 8.396 us, min = 8.396 us, total = 8.396 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob - 1 total (0 active), Execution time: mean = 496.434 us, total = 496.434 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.025 ms, total = 1.025 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 179.567 us, total = 179.567 us, Queueing time: mean = 9.133 us, max = 9.133 us, min = 9.133 us, total = 9.133 us
[state-dump] 	Subscriber.HandlePublishedMessage_GCS_JOB_CHANNEL - 1 total (0 active), Execution time: mean = 29.045 us, total = 29.045 us, Queueing time: mean = 141.546 us, max = 141.546 us, min = 141.546 us, total = 141.546 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
[state-dump] 	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo - 1 total (0 active), Execution time: mean = 879.382 us, total = 879.382 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.314 us, total = 22.314 us, Queueing time: mean = 125.550 us, max = 125.550 us, min = 125.550 us, total = 125.550 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.569 us, total = 18.569 us, Queueing time: mean = 49.567 us, max = 49.567 us, min = 49.567 us, total = 49.567 us
[state-dump] DebugString() time ms: 1
[state-dump] 
[state-dump] 
[2025-07-05 18:46:59,108 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:46:59,120 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2872 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:47:04,113 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:09,118 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:09,131 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2832 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:47:14,122 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:19,127 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:19,142 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.302 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:47:24,132 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:29,137 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:29,153 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2841 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:47:34,142 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:39,146 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:39,163 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.3048 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:47:44,151 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:49,156 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:49,172 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2868 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:47:54,160 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:58,862 I 660838 660867] (raylet) store.cc:576: Plasma store debug dump: 
Current usage: 0 / 79.3075 GB
- num bytes created total: 200
0 pending objects of total size 0MB
- objects spillable: 0
- bytes spillable: 0
- objects unsealed: 0
- bytes unsealed: 0
- objects in use: 0
- bytes in use: 0
- objects evictable: 0
- bytes evictable: 0

- objects created by worker: 0
- bytes created by worker: 0
- objects restored: 0
- bytes restored: 0
- objects received: 0
- bytes received: 0
- objects errored: 0
- bytes errored: 0

[2025-07-05 18:47:58,889 I 660838 660838] (raylet) node_manager.cc:450: [state-dump] NodeManager:
[state-dump] Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[state-dump] Node name: ***********
[state-dump] InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:***********: 1, memory: 1.85051e+11}
[state-dump] ClusterTaskManager:
[state-dump] ========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
[state-dump] Infeasible queue length: 0
[state-dump] Schedule queue length: 0
[state-dump] Dispatch queue length: 0
[state-dump] num_waiting_for_resource: 0
[state-dump] num_waiting_for_plasma_memory: 0
[state-dump] num_waiting_for_remote_node_resources: 0
[state-dump] num_worker_not_started_by_job_config_not_exist: 0
[state-dump] num_worker_not_started_by_registration_timeout: 0
[state-dump] num_tasks_waiting_for_workers: 0
[state-dump] num_cancelled_tasks: 0
[state-dump] cluster_resource_scheduler state: 
[state-dump] Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "available": {CPU: [230000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [0, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 0 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, node:***********: 10000, GPU: 40000, memory: 1850507337730000, CPU: 240000, object_store_memory: 793074573310000, node:__internal_head__: 10000}}, "available": {object_store_memory: 793074573310000, node:***********: 10000, node:__internal_head__: 10000, memory: 1850507337730000, CPU: 230000, accelerator_type:G: 10000, GPU: 30000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
[state-dump] Waiting tasks size: 0
[state-dump] Number of executing tasks: 1
[state-dump] Number of pinned task arguments: 0
[state-dump] Number of total spilled tasks: 0
[state-dump] Number of spilled waiting tasks: 0
[state-dump] Number of spilled unschedulable tasks: 0
[state-dump] Resource usage {
[state-dump]     - (language=PYTHON actor_or_task=ResultCollector.__init__ pid=660927 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385): {}
[state-dump]     - (language=PYTHON actor_or_task=LLMEngine.__init__ pid=660939 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0): {GPU: 1, CPU: 1}
[state-dump] }
[state-dump] Backlog Size per scheduling descriptor :{workerId: num backlogs}:
[state-dump] 
[state-dump] Running tasks by scheduling class:
[state-dump]     - {depth=1 function_descriptor={type=PythonFunctionDescriptor, module_name=Ayo.engines.llm, class_name=LLMEngine, function_name=__init__, function_hash=e2f5af0ea8e345998125e6a621a10aec} scheduling_strategy=default_scheduling_strategy {
[state-dump] }
[state-dump]  resource_set={GPU : 1, CPU : 1, }}: 1/24
[state-dump] ==================================================
[state-dump] 
[state-dump] ClusterResources:
[state-dump] LocalObjectManager:
[state-dump] - num pinned objects: 0
[state-dump] - pinned objects size: 0
[state-dump] - num objects pending restore: 0
[state-dump] - num objects pending spill: 0
[state-dump] - num bytes pending spill: 0
[state-dump] - num bytes currently spilled: 0
[state-dump] - cumulative spill requests: 0
[state-dump] - cumulative restore requests: 0
[state-dump] - spilled objects pending delete: 0
[state-dump] 
[state-dump] ObjectManager:
[state-dump] - num local objects: 0
[state-dump] - num unfulfilled push requests: 0
[state-dump] - num object pull requests: 0
[state-dump] - num chunks received total: 0
[state-dump] - num chunks received failed (all): 0
[state-dump] - num chunks received failed / cancelled: 0
[state-dump] - num chunks received failed / plasma error: 0
[state-dump] Event stats:
[state-dump] Global stats: 0 total (0 active)
[state-dump] Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] Execution time:  mean = -nan s, total = 0.000 s
[state-dump] Event stats:
[state-dump] PushManager:
[state-dump] - num pushes in flight: 0
[state-dump] - num chunks in flight: 0
[state-dump] - num chunks remaining: 0
[state-dump] - max chunks allowed: 409
[state-dump] OwnershipBasedObjectDirectory:
[state-dump] - num listeners: 0
[state-dump] - cumulative location updates: 0
[state-dump] - num location updates per second: 0.000
[state-dump] - num location lookups per second: 0.000
[state-dump] - num locations added per second: 0.000
[state-dump] - num locations removed per second: 0.000
[state-dump] BufferPool:
[state-dump] - create buffer state map size: 0
[state-dump] PullManager:
[state-dump] - num bytes available for pulled objects: 79307457331
[state-dump] - num bytes being pulled (all): 0
[state-dump] - num bytes being pulled / pinned: 0
[state-dump] - get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - first get request bundle: N/A
[state-dump] - first wait request bundle: N/A
[state-dump] - first task request bundle: N/A
[state-dump] - num objects queued: 0
[state-dump] - num objects actively pulled (all): 0
[state-dump] - num objects actively pulled / pinned: 0
[state-dump] - num bundles being pulled: 0
[state-dump] - num pull retries: 0
[state-dump] - max timeout seconds: 0
[state-dump] - max timeout request is already processed. No entry.
[state-dump] 
[state-dump] WorkerPool:
[state-dump] - registered jobs: 1
[state-dump] - process_failed_job_config_missing: 0
[state-dump] - process_failed_rate_limited: 0
[state-dump] - process_failed_pending_registration: 0
[state-dump] - process_failed_runtime_env_setup_failed: 0
[state-dump] - num PYTHON workers: 24
[state-dump] - num PYTHON drivers: 1
[state-dump] - num PYTHON pending start requests: 0
[state-dump] - num PYTHON pending registration requests: 0
[state-dump] - num object spill callbacks queued: 0
[state-dump] - num object restore queued: 0
[state-dump] - num util functions queued: 0
[state-dump] - num idle workers: 22
[state-dump] TaskDependencyManager:
[state-dump] - task deps map size: 0
[state-dump] - get req map size: 0
[state-dump] - wait req map size: 0
[state-dump] - local objects map size: 0
[state-dump] WaitManager:
[state-dump] - num active wait requests: 0
[state-dump] Subscriber:
[state-dump] Channel WORKER_OBJECT_EVICTION
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_REF_REMOVED_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_OBJECT_LOCATIONS_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] num async plasma notifications: 0
[state-dump] Event stats:
[state-dump] Global stats: 29055 total (40 active)
[state-dump] Queueing time: mean = 97.060 us, max = 625.620 ms, min = -0.000 s, total = 2.820 s
[state-dump] Execution time:  mean = 128.537 us, total = 3.735 s
[state-dump] Event stats:
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog.HandleRequestImpl - 7500 total (0 active), Execution time: mean = 26.026 us, total = 195.199 ms, Queueing time: mean = 39.710 us, max = 659.005 us, min = 4.063 us, total = 297.822 ms
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog - 7500 total (0 active), Execution time: mean = 267.307 us, total = 2.005 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckGC - 2998 total (1 active), Execution time: mean = 1.895 us, total = 5.681 ms, Queueing time: mean = 75.823 us, max = 25.832 ms, min = 7.559 us, total = 227.316 ms
[state-dump] 	RaySyncer.OnDemandBroadcasting - 2998 total (1 active), Execution time: mean = 6.185 us, total = 18.541 ms, Queueing time: mean = 71.936 us, max = 25.814 ms, min = 3.808 us, total = 215.663 ms
[state-dump] 	ObjectManager.UpdateAvailableMemory - 2997 total (0 active), Execution time: mean = 2.852 us, total = 8.547 ms, Queueing time: mean = 19.295 us, max = 627.487 us, min = 2.792 us, total = 57.827 ms
[state-dump] 	RayletWorkerPool.deadline_timer.kill_idle_workers - 1500 total (1 active), Execution time: mean = 10.180 us, total = 15.270 ms, Queueing time: mean = 75.331 us, max = 30.900 ms, min = 11.665 us, total = 112.996 ms
[state-dump] 	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 1199 total (1 active), Execution time: mean = 189.507 us, total = 227.218 ms, Queueing time: mean = 54.321 us, max = 115.114 us, min = -0.000 s, total = 65.131 ms
[state-dump] 	NodeManager.ScheduleAndDispatchTasks - 300 total (1 active), Execution time: mean = 11.663 us, total = 3.499 ms, Queueing time: mean = 62.777 us, max = 2.069 ms, min = 15.308 us, total = 18.833 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad - 300 total (0 active), Execution time: mean = 372.636 us, total = 111.791 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckForUnexpectedWorkerDisconnects - 300 total (1 active), Execution time: mean = 48.143 us, total = 14.443 ms, Queueing time: mean = 39.501 us, max = 2.008 ms, min = 6.906 us, total = 11.850 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad.HandleRequestImpl - 300 total (0 active), Execution time: mean = 83.721 us, total = 25.116 ms, Queueing time: mean = 45.360 us, max = 87.484 us, min = 7.863 us, total = 13.608 ms
[state-dump] 	NodeManager.deadline_timer.flush_free_objects - 300 total (1 active), Execution time: mean = 7.114 us, total = 2.134 ms, Queueing time: mean = 185.500 us, max = 8.799 ms, min = 15.861 us, total = 55.650 ms
[state-dump] 	NodeManager.deadline_timer.spill_objects_when_over_threshold - 300 total (1 active), Execution time: mean = 2.138 us, total = 641.515 us, Queueing time: mean = 189.483 us, max = 8.796 ms, min = 15.710 us, total = 56.845 ms
[state-dump] 	ClientConnection.async_read.ProcessMessageHeader - 103 total (25 active), Execution time: mean = 2.777 us, total = 286.060 us, Queueing time: mean = 15.460 ms, max = 625.620 ms, min = 12.580 us, total = 1.592 s
[state-dump] 	ClusterResourceManager.ResetRemoteNodeView - 101 total (1 active), Execution time: mean = 6.926 us, total = 699.499 us, Queueing time: mean = 56.419 us, max = 101.828 us, min = 21.085 us, total = 5.698 ms
[state-dump] 	ClientConnection.async_read.ProcessMessage - 78 total (0 active), Execution time: mean = 1.299 ms, total = 101.318 ms, Queueing time: mean = 12.499 us, max = 146.915 us, min = 2.738 us, total = 974.893 us
[state-dump] 	NodeManager.deadline_timer.record_metrics - 60 total (1 active), Execution time: mean = 612.960 us, total = 36.778 ms, Queueing time: mean = 340.804 us, max = 8.350 ms, min = 16.353 us, total = 20.448 ms
[state-dump] 	NodeManager.deadline_timer.debug_state_dump - 30 total (1 active), Execution time: mean = 1.837 ms, total = 55.103 ms, Queueing time: mean = 53.129 us, max = 111.692 us, min = 21.936 us, total = 1.594 ms
[state-dump] 	ClientConnection.async_write.DoAsyncWrites - 26 total (0 active), Execution time: mean = 525.308 ns, total = 13.658 us, Queueing time: mean = 16.479 us, max = 51.598 us, min = 8.590 us, total = 428.462 us
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig - 25 total (0 active), Execution time: mean = 361.343 us, total = 9.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ObjectManager.ObjectDeleted - 25 total (0 active), Execution time: mean = 9.360 us, total = 234.005 us, Queueing time: mean = 44.687 us, max = 285.967 us, min = 18.537 us, total = 1.117 ms
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig.HandleRequestImpl - 25 total (0 active), Execution time: mean = 29.747 us, total = 743.681 us, Queueing time: mean = 142.844 us, max = 2.629 ms, min = 7.599 us, total = 3.571 ms
[state-dump] 	ObjectManager.ObjectAdded - 25 total (0 active), Execution time: mean = 6.450 us, total = 161.244 us, Queueing time: mean = 54.017 us, max = 364.526 us, min = 8.081 us, total = 1.350 ms
[state-dump] 	PeriodicalRunner.RunFnPeriodically - 14 total (0 active), Execution time: mean = 197.858 us, total = 2.770 ms, Queueing time: mean = 3.470 ms, max = 9.264 ms, min = 28.791 us, total = 48.577 ms
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive.OnReplyReceived - 5 total (0 active), Execution time: mean = 35.337 us, total = 176.683 us, Queueing time: mean = 41.377 us, max = 62.580 us, min = 12.023 us, total = 206.887 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive - 5 total (0 active), Execution time: mean = 856.782 us, total = 4.284 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GcsCheckAlive - 5 total (1 active), Execution time: mean = 186.985 us, total = 934.925 us, Queueing time: mean = 1.518 ms, max = 2.004 ms, min = 1.837 ms, total = 7.589 ms
[state-dump] 	NodeManager.deadline_timer.print_event_loop_stats - 5 total (1 active, 1 running), Execution time: mean = 1.636 ms, total = 8.178 ms, Queueing time: mean = 42.616 us, max = 68.728 us, min = 37.290 us, total = 213.082 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 144.096 us, total = 288.193 us, Queueing time: mean = 929.080 us, max = 1.676 ms, min = 182.285 us, total = 1.858 ms
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 428.071 ms, total = 856.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease.HandleRequestImpl - 2 total (0 active), Execution time: mean = 111.613 us, total = 223.226 us, Queueing time: mean = 31.811 us, max = 50.391 us, min = 13.231 us, total = 63.622 us
[state-dump] 	 - 2 total (0 active), Execution time: mean = 772.000 ns, total = 1.544 us, Queueing time: mean = 45.443 us, max = 71.510 us, min = 19.376 us, total = 90.886 us
[state-dump] 	WorkerPool.PopWorkerCallback - 2 total (0 active), Execution time: mean = 21.812 us, total = 43.624 us, Queueing time: mean = 9.826 us, max = 13.376 us, min = 6.277 us, total = 19.653 us
[state-dump] 	RaySyncerRegister - 2 total (0 active), Execution time: mean = 1.762 us, total = 3.523 us, Queueing time: mean = 147.500 ns, max = 247.000 ns, min = 48.000 ns, total = 295.000 ns
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 1.015 ms, total = 2.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 108.770 us, total = 217.541 us, Queueing time: mean = 200.000 ns, max = 223.000 ns, min = 177.000 ns, total = 400.000 ns
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease - 2 total (0 active), Execution time: mean = 364.964 us, total = 729.928 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.715 us, total = 123.715 us, Queueing time: mean = 8.396 us, max = 8.396 us, min = 8.396 us, total = 8.396 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.025 ms, total = 1.025 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	Subscriber.HandlePublishedMessage_GCS_JOB_CHANNEL - 1 total (0 active), Execution time: mean = 29.045 us, total = 29.045 us, Queueing time: mean = 141.546 us, max = 141.546 us, min = 141.546 us, total = 141.546 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 179.567 us, total = 179.567 us, Queueing time: mean = 9.133 us, max = 9.133 us, min = 9.133 us, total = 9.133 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob - 1 total (0 active), Execution time: mean = 496.434 us, total = 496.434 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.569 us, total = 18.569 us, Queueing time: mean = 49.567 us, max = 49.567 us, min = 49.567 us, total = 49.567 us
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.314 us, total = 22.314 us, Queueing time: mean = 125.550 us, max = 125.550 us, min = 125.550 us, total = 125.550 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo - 1 total (0 active), Execution time: mean = 879.382 us, total = 879.382 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] DebugString() time ms: 1
[state-dump] 
[state-dump] 
[2025-07-05 18:47:59,165 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:47:59,182 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2817 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:48:04,170 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:09,175 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:09,193 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2885 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:48:14,180 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:19,185 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:19,204 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2865 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:48:24,190 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:29,195 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:29,214 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.304 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:48:34,200 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:39,205 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:39,225 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2157 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:48:44,210 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:49,215 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:49,236 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2188 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:48:54,219 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:58,862 I 660838 660867] (raylet) store.cc:576: Plasma store debug dump: 
Current usage: 0 / 79.3075 GB
- num bytes created total: 200
0 pending objects of total size 0MB
- objects spillable: 0
- bytes spillable: 0
- objects unsealed: 0
- bytes unsealed: 0
- objects in use: 0
- bytes in use: 0
- objects evictable: 0
- bytes evictable: 0

- objects created by worker: 0
- bytes created by worker: 0
- objects restored: 0
- bytes restored: 0
- objects received: 0
- bytes received: 0
- objects errored: 0
- bytes errored: 0

[2025-07-05 18:48:58,891 I 660838 660838] (raylet) node_manager.cc:450: [state-dump] NodeManager:
[state-dump] Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[state-dump] Node name: ***********
[state-dump] InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:***********: 1, memory: 1.85051e+11}
[state-dump] ClusterTaskManager:
[state-dump] ========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
[state-dump] Infeasible queue length: 0
[state-dump] Schedule queue length: 0
[state-dump] Dispatch queue length: 0
[state-dump] num_waiting_for_resource: 0
[state-dump] num_waiting_for_plasma_memory: 0
[state-dump] num_waiting_for_remote_node_resources: 0
[state-dump] num_worker_not_started_by_job_config_not_exist: 0
[state-dump] num_worker_not_started_by_registration_timeout: 0
[state-dump] num_tasks_waiting_for_workers: 0
[state-dump] num_cancelled_tasks: 0
[state-dump] cluster_resource_scheduler state: 
[state-dump] Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "available": {CPU: [230000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [0, 10000, 10000, 10000], object_store_memory: [793074573310000], node:***********: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 0 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, node:***********: 10000, GPU: 40000, memory: 1850507337730000, CPU: 240000, object_store_memory: 793074573310000, node:__internal_head__: 10000}}, "available": {object_store_memory: 793074573310000, node:***********: 10000, node:__internal_head__: 10000, memory: 1850507337730000, CPU: 230000, accelerator_type:G: 10000, GPU: 30000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
[state-dump] Waiting tasks size: 0
[state-dump] Number of executing tasks: 1
[state-dump] Number of pinned task arguments: 0
[state-dump] Number of total spilled tasks: 0
[state-dump] Number of spilled waiting tasks: 0
[state-dump] Number of spilled unschedulable tasks: 0
[state-dump] Resource usage {
[state-dump]     - (language=PYTHON actor_or_task=ResultCollector.__init__ pid=660927 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385): {}
[state-dump]     - (language=PYTHON actor_or_task=LLMEngine.__init__ pid=660939 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0): {GPU: 1, CPU: 1}
[state-dump] }
[state-dump] Backlog Size per scheduling descriptor :{workerId: num backlogs}:
[state-dump] 
[state-dump] Running tasks by scheduling class:
[state-dump]     - {depth=1 function_descriptor={type=PythonFunctionDescriptor, module_name=Ayo.engines.llm, class_name=LLMEngine, function_name=__init__, function_hash=e2f5af0ea8e345998125e6a621a10aec} scheduling_strategy=default_scheduling_strategy {
[state-dump] }
[state-dump]  resource_set={CPU : 1, GPU : 1, }}: 1/24
[state-dump] ==================================================
[state-dump] 
[state-dump] ClusterResources:
[state-dump] LocalObjectManager:
[state-dump] - num pinned objects: 0
[state-dump] - pinned objects size: 0
[state-dump] - num objects pending restore: 0
[state-dump] - num objects pending spill: 0
[state-dump] - num bytes pending spill: 0
[state-dump] - num bytes currently spilled: 0
[state-dump] - cumulative spill requests: 0
[state-dump] - cumulative restore requests: 0
[state-dump] - spilled objects pending delete: 0
[state-dump] 
[state-dump] ObjectManager:
[state-dump] - num local objects: 0
[state-dump] - num unfulfilled push requests: 0
[state-dump] - num object pull requests: 0
[state-dump] - num chunks received total: 0
[state-dump] - num chunks received failed (all): 0
[state-dump] - num chunks received failed / cancelled: 0
[state-dump] - num chunks received failed / plasma error: 0
[state-dump] Event stats:
[state-dump] Global stats: 0 total (0 active)
[state-dump] Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] Execution time:  mean = -nan s, total = 0.000 s
[state-dump] Event stats:
[state-dump] PushManager:
[state-dump] - num pushes in flight: 0
[state-dump] - num chunks in flight: 0
[state-dump] - num chunks remaining: 0
[state-dump] - max chunks allowed: 409
[state-dump] OwnershipBasedObjectDirectory:
[state-dump] - num listeners: 0
[state-dump] - cumulative location updates: 0
[state-dump] - num location updates per second: 0.000
[state-dump] - num location lookups per second: 0.000
[state-dump] - num locations added per second: 0.000
[state-dump] - num locations removed per second: 0.000
[state-dump] BufferPool:
[state-dump] - create buffer state map size: 0
[state-dump] PullManager:
[state-dump] - num bytes available for pulled objects: 79307457331
[state-dump] - num bytes being pulled (all): 0
[state-dump] - num bytes being pulled / pinned: 0
[state-dump] - get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
[state-dump] - first get request bundle: N/A
[state-dump] - first wait request bundle: N/A
[state-dump] - first task request bundle: N/A
[state-dump] - num objects queued: 0
[state-dump] - num objects actively pulled (all): 0
[state-dump] - num objects actively pulled / pinned: 0
[state-dump] - num bundles being pulled: 0
[state-dump] - num pull retries: 0
[state-dump] - max timeout seconds: 0
[state-dump] - max timeout request is already processed. No entry.
[state-dump] 
[state-dump] WorkerPool:
[state-dump] - registered jobs: 1
[state-dump] - process_failed_job_config_missing: 0
[state-dump] - process_failed_rate_limited: 0
[state-dump] - process_failed_pending_registration: 0
[state-dump] - process_failed_runtime_env_setup_failed: 0
[state-dump] - num PYTHON workers: 24
[state-dump] - num PYTHON drivers: 1
[state-dump] - num PYTHON pending start requests: 0
[state-dump] - num PYTHON pending registration requests: 0
[state-dump] - num object spill callbacks queued: 0
[state-dump] - num object restore queued: 0
[state-dump] - num util functions queued: 0
[state-dump] - num idle workers: 22
[state-dump] TaskDependencyManager:
[state-dump] - task deps map size: 0
[state-dump] - get req map size: 0
[state-dump] - wait req map size: 0
[state-dump] - local objects map size: 0
[state-dump] WaitManager:
[state-dump] - num active wait requests: 0
[state-dump] Subscriber:
[state-dump] Channel WORKER_OBJECT_EVICTION
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_REF_REMOVED_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] Channel WORKER_OBJECT_LOCATIONS_CHANNEL
[state-dump] - cumulative subscribe requests: 0
[state-dump] - cumulative unsubscribe requests: 0
[state-dump] - active subscribed publishers: 0
[state-dump] - cumulative published messages: 0
[state-dump] - cumulative processed messages: 0
[state-dump] num async plasma notifications: 0
[state-dump] Event stats:
[state-dump] Global stats: 34797 total (40 active)
[state-dump] Queueing time: mean = 87.508 us, max = 625.620 ms, min = -0.000 s, total = 3.045 s
[state-dump] Execution time:  mean = 123.112 us, total = 4.284 s
[state-dump] Event stats:
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog.HandleRequestImpl - 9000 total (0 active), Execution time: mean = 25.748 us, total = 231.736 ms, Queueing time: mean = 40.313 us, max = 659.005 us, min = 4.063 us, total = 362.813 ms
[state-dump] 	NodeManagerService.grpc_server.ReportWorkerBacklog - 9000 total (0 active), Execution time: mean = 268.279 us, total = 2.415 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckGC - 3598 total (1 active), Execution time: mean = 1.893 us, total = 6.810 ms, Queueing time: mean = 75.002 us, max = 25.832 ms, min = 7.559 us, total = 269.859 ms
[state-dump] 	RaySyncer.OnDemandBroadcasting - 3598 total (1 active), Execution time: mean = 6.177 us, total = 22.227 ms, Queueing time: mean = 71.121 us, max = 25.814 ms, min = 3.808 us, total = 255.894 ms
[state-dump] 	ObjectManager.UpdateAvailableMemory - 3597 total (0 active), Execution time: mean = 2.850 us, total = 10.250 ms, Queueing time: mean = 19.307 us, max = 627.487 us, min = 2.792 us, total = 69.449 ms
[state-dump] 	RayletWorkerPool.deadline_timer.kill_idle_workers - 1800 total (1 active), Execution time: mean = 10.166 us, total = 18.299 ms, Queueing time: mean = 72.367 us, max = 30.900 ms, min = 11.665 us, total = 130.260 ms
[state-dump] 	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 1439 total (1 active), Execution time: mean = 188.851 us, total = 271.756 ms, Queueing time: mean = 55.525 us, max = 427.834 us, min = -0.000 s, total = 79.901 ms
[state-dump] 	NodeManager.ScheduleAndDispatchTasks - 360 total (1 active), Execution time: mean = 11.747 us, total = 4.229 ms, Queueing time: mean = 62.727 us, max = 2.069 ms, min = 15.308 us, total = 22.582 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad - 360 total (0 active), Execution time: mean = 370.528 us, total = 133.390 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.CheckForUnexpectedWorkerDisconnects - 360 total (1 active), Execution time: mean = 47.930 us, total = 17.255 ms, Queueing time: mean = 42.334 us, max = 2.008 ms, min = 6.906 us, total = 15.240 ms
[state-dump] 	NodeManagerService.grpc_server.GetResourceLoad.HandleRequestImpl - 360 total (0 active), Execution time: mean = 82.814 us, total = 29.813 ms, Queueing time: mean = 45.000 us, max = 87.484 us, min = 7.863 us, total = 16.200 ms
[state-dump] 	NodeManager.deadline_timer.flush_free_objects - 360 total (1 active), Execution time: mean = 7.028 us, total = 2.530 ms, Queueing time: mean = 178.748 us, max = 8.799 ms, min = 15.861 us, total = 64.349 ms
[state-dump] 	NodeManager.deadline_timer.spill_objects_when_over_threshold - 360 total (1 active), Execution time: mean = 2.126 us, total = 765.242 us, Queueing time: mean = 182.667 us, max = 8.796 ms, min = 15.710 us, total = 65.760 ms
[state-dump] 	ClusterResourceManager.ResetRemoteNodeView - 121 total (1 active), Execution time: mean = 6.976 us, total = 844.108 us, Queueing time: mean = 56.649 us, max = 101.828 us, min = 21.085 us, total = 6.855 ms
[state-dump] 	ClientConnection.async_read.ProcessMessageHeader - 103 total (25 active), Execution time: mean = 2.777 us, total = 286.060 us, Queueing time: mean = 15.460 ms, max = 625.620 ms, min = 12.580 us, total = 1.592 s
[state-dump] 	ClientConnection.async_read.ProcessMessage - 78 total (0 active), Execution time: mean = 1.299 ms, total = 101.318 ms, Queueing time: mean = 12.499 us, max = 146.915 us, min = 2.738 us, total = 974.893 us
[state-dump] 	NodeManager.deadline_timer.record_metrics - 72 total (1 active), Execution time: mean = 599.239 us, total = 43.145 ms, Queueing time: mean = 321.776 us, max = 8.350 ms, min = 16.353 us, total = 23.168 ms
[state-dump] 	NodeManager.deadline_timer.debug_state_dump - 36 total (1 active), Execution time: mean = 1.775 ms, total = 63.902 ms, Queueing time: mean = 53.337 us, max = 111.692 us, min = 21.936 us, total = 1.920 ms
[state-dump] 	ClientConnection.async_write.DoAsyncWrites - 26 total (0 active), Execution time: mean = 525.308 ns, total = 13.658 us, Queueing time: mean = 16.479 us, max = 51.598 us, min = 8.590 us, total = 428.462 us
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig - 25 total (0 active), Execution time: mean = 361.343 us, total = 9.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ObjectManager.ObjectDeleted - 25 total (0 active), Execution time: mean = 9.360 us, total = 234.005 us, Queueing time: mean = 44.687 us, max = 285.967 us, min = 18.537 us, total = 1.117 ms
[state-dump] 	NodeManagerService.grpc_server.GetSystemConfig.HandleRequestImpl - 25 total (0 active), Execution time: mean = 29.747 us, total = 743.681 us, Queueing time: mean = 142.844 us, max = 2.629 ms, min = 7.599 us, total = 3.571 ms
[state-dump] 	ObjectManager.ObjectAdded - 25 total (0 active), Execution time: mean = 6.450 us, total = 161.244 us, Queueing time: mean = 54.017 us, max = 364.526 us, min = 8.081 us, total = 1.350 ms
[state-dump] 	PeriodicalRunner.RunFnPeriodically - 14 total (0 active), Execution time: mean = 197.858 us, total = 2.770 ms, Queueing time: mean = 3.470 ms, max = 9.264 ms, min = 28.791 us, total = 48.577 ms
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive.OnReplyReceived - 6 total (0 active), Execution time: mean = 37.140 us, total = 222.840 us, Queueing time: mean = 44.163 us, max = 62.580 us, min = 12.023 us, total = 264.981 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive - 6 total (0 active), Execution time: mean = 870.437 us, total = 5.223 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GcsCheckAlive - 6 total (1 active), Execution time: mean = 195.793 us, total = 1.175 ms, Queueing time: mean = 1.576 ms, max = 2.004 ms, min = 1.837 ms, total = 9.455 ms
[state-dump] 	NodeManager.deadline_timer.print_event_loop_stats - 6 total (1 active, 1 running), Execution time: mean = 1.707 ms, total = 10.242 ms, Queueing time: mean = 41.728 us, max = 68.728 us, min = 37.284 us, total = 250.366 us
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 144.096 us, total = 288.193 us, Queueing time: mean = 929.080 us, max = 1.676 ms, min = 182.285 us, total = 1.858 ms
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 428.071 ms, total = 856.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease.HandleRequestImpl - 2 total (0 active), Execution time: mean = 111.613 us, total = 223.226 us, Queueing time: mean = 31.811 us, max = 50.391 us, min = 13.231 us, total = 63.622 us
[state-dump] 	 - 2 total (0 active), Execution time: mean = 772.000 ns, total = 1.544 us, Queueing time: mean = 45.443 us, max = 71.510 us, min = 19.376 us, total = 90.886 us
[state-dump] 	WorkerPool.PopWorkerCallback - 2 total (0 active), Execution time: mean = 21.812 us, total = 43.624 us, Queueing time: mean = 9.826 us, max = 13.376 us, min = 6.277 us, total = 19.653 us
[state-dump] 	RaySyncerRegister - 2 total (0 active), Execution time: mean = 1.762 us, total = 3.523 us, Queueing time: mean = 147.500 ns, max = 247.000 ns, min = 48.000 ns, total = 295.000 ns
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 1.015 ms, total = 2.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 108.770 us, total = 217.541 us, Queueing time: mean = 200.000 ns, max = 223.000 ns, min = 177.000 ns, total = 400.000 ns
[state-dump] 	NodeManagerService.grpc_server.RequestWorkerLease - 2 total (0 active), Execution time: mean = 364.964 us, total = 729.928 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.715 us, total = 123.715 us, Queueing time: mean = 8.396 us, max = 8.396 us, min = 8.396 us, total = 8.396 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.025 ms, total = 1.025 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	Subscriber.HandlePublishedMessage_GCS_JOB_CHANNEL - 1 total (0 active), Execution time: mean = 29.045 us, total = 29.045 us, Queueing time: mean = 141.546 us, max = 141.546 us, min = 141.546 us, total = 141.546 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 179.567 us, total = 179.567 us, Queueing time: mean = 9.133 us, max = 9.133 us, min = 9.133 us, total = 9.133 us
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob - 1 total (0 active), Execution time: mean = 496.434 us, total = 496.434 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.569 us, total = 18.569 us, Queueing time: mean = 49.567 us, max = 49.567 us, min = 49.567 us, total = 49.567 us
[state-dump] 	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.AddJob.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.314 us, total = 22.314 us, Queueing time: mean = 125.550 us, max = 125.550 us, min = 125.550 us, total = 125.550 us
[state-dump] 	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo - 1 total (0 active), Execution time: mean = 879.382 us, total = 879.382 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
[state-dump] DebugString() time ms: 1
[state-dump] 
[state-dump] 
[2025-07-05 18:48:59,224 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:48:59,246 E 660838 660871] (raylet) file_system_monitor.cc:116: /home/<USER>/von/Ayo/cache/ray/session_2025-07-05_18-42-57_549726_660616 is over 95% full, available space: 15.2186 GB; capacity: 491.586 GB. Object creation will fail if spilling is required.
[2025-07-05 18:49:04,229 W 660838 660838] (raylet) memory_monitor.cc:198: Got negative used memory for cgroup -1, setting it to zero
[2025-07-05 18:49:08,659 I 660838 660838] (raylet) node_manager.cc:1523: Disconnecting client, graceful=true, disconnect_type=3, has_creation_task_exception=false worker_id=01000000ffffffffffffffffffffffffffffffffffffffffffffffff
[2025-07-05 18:49:08,659 I 660838 660838] (raylet) node_manager.cc:1615: Driver (pid=660616) is disconnected. worker_id=01000000ffffffffffffffffffffffffffffffffffffffffffffffff job_id=01000000
[2025-07-05 18:49:08,665 W 660838 660867] (raylet) store.cc:368: Disconnecting client due to connection error with code 2: End of file
[2025-07-05 18:49:08,665 I 660838 660838] (raylet) node_manager.cc:1018: The leased worker dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385 is killed because the owner process 01000000ffffffffffffffffffffffffffffffffffffffffffffffff died.
[2025-07-05 18:49:08,665 I 660838 660838] (raylet) node_manager.cc:1018: The leased worker d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0 is killed because the owner process 01000000ffffffffffffffffffffffffffffffffffffffffffffffff died.
[2025-07-05 18:49:08,666 I 660838 660838] (raylet) worker_pool.cc:724: Job 01000000 already started in worker pool.
[2025-07-05 18:49:08,666 I 660838 660838] (raylet) node_manager.cc:559: The leased worker  is killed because the job 01000000 finished. worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385
[2025-07-05 18:49:08,666 I 660838 660838] (raylet) node_manager.cc:559: The leased worker  is killed because the job 01000000 finished. worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0
[2025-07-05 18:49:08,682 I 660838 660838] (raylet) node_manager.cc:1523: Disconnecting client, graceful=true, disconnect_type=1, has_creation_task_exception=false worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385
[2025-07-05 18:49:08,682 I 660838 660838] (raylet) node_manager.cc:1523: Disconnecting client, graceful=true, disconnect_type=1, has_creation_task_exception=false worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0
[2025-07-05 18:49:08,684 W 660838 660838] (raylet) node_manager.cc:567: Failed to send exit request to worker : RpcError: RPC Error message: Socket closed; RPC Error details:  rpc_code: 14. Killing it using SIGKILL instead. worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0
[2025-07-05 18:49:08,697 W 660838 660867] (raylet) store.cc:368: Disconnecting client due to connection error with code 2: End of file
[2025-07-05 18:49:08,697 W 660838 660838] (raylet) node_manager.cc:567: Failed to send exit request to worker : RpcError: RPC Error message: Socket closed; RPC Error details:  rpc_code: 14. Killing it using SIGKILL instead. worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385
[2025-07-05 18:49:08,783 I 660838 660838] (raylet) main.cc:504: received SIGTERM. Existing local drain request = None
[2025-07-05 18:49:08,783 I 660838 660838] (raylet) main.cc:307: Raylet graceful shutdown triggered, reason = EXPECTED_TERMINATION, reason message = received SIGTERM
[2025-07-05 18:49:08,783 I 660838 660838] (raylet) main.cc:310: Shutting down...
[2025-07-05 18:49:08,783 I 660838 660838] (raylet) accessor.cc:518: Unregistering node node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660838 660838] (raylet) accessor.cc:531: Finished unregistering node info, status = OK node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 W 660838 660867] (raylet) store.cc:368: Disconnecting client due to connection error with code 2: End of file
[2025-07-05 18:49:08,790 I 660838 660838] (raylet) agent_manager.cc:115: Killing agent dashboard_agent, pid 660902.
[2025-07-05 18:49:08,799 I 660838 660905] (raylet) agent_manager.cc:82: Agent process with name dashboard_agent exited, exit code 0.
[2025-07-05 18:49:08,799 I 660838 660838] (raylet) agent_manager.cc:115: Killing agent runtime_env_agent, pid 660906.
[2025-07-05 18:49:08,805 I 660838 660907] (raylet) agent_manager.cc:82: Agent process with name runtime_env_agent exited, exit code 0.
[2025-07-05 18:49:08,805 I 660838 660838] (raylet) io_service_pool.cc:48: IOServicePool is stopped.
[2025-07-05 18:49:08,871 I 660838 660838] (raylet) stats.h:120: Stats module has shutdown.
