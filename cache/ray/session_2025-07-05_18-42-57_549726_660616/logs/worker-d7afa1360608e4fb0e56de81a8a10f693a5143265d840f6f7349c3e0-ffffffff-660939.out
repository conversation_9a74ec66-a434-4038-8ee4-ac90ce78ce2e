:job_id:01000000
:actor_name:LLMEngine
CUDA available: True
Number of available GPUs: 1
Current GPU device: 0
GPU name: NVIDIA GeForce RTX 3090
Initializing vLLM engine with model: meta-llama/Llama-2-7b-chat-hf
INFO 07-05 18:43:05 llm_engine.py:79] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-chat-hf', tokenizer='meta-llama/Llama-2-7b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=True, dtype=torch.float16, max_seq_len=4096, download_dir=None, load_format=auto, tensor_parallel_size=1, pipeline_parallel_size=1, quantization=None, seed=0)
INFO 07-05 18:43:05 tokenizer.py:31] For some LLaMA V1 models, initializing the fast tokenizer may take a long time. To reduce the initialization time, consider using 'hf-internal-testing/llama-tokenizer' instead of the original tokenizer.
INFO 07-05 18:48:55 llm_engine.py:250] # GPU blocks: 1042, # CPU blocks: 512
init cache cost: 3.47446580696851
vLLM engine initialized with model: meta-llama/Llama-2-7b-chat-hf
