[2025-07-05 18:42:59,472 I 660937 660937] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660937
[2025-07-05 18:42:59,477 I 660937 660937] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,481 I 660937 660937] grpc_server.cc:141: worker server started, listening on port 40033.
[2025-07-05 18:42:59,483 I 660937 660937] core_worker.cc:542: Initializing worker at address: ***********:40033 worker_id=677c708bfd6ab5727da69b2e4d0552efe8f2f66d375a8a9f7b61b049 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,484 I 660937 660937] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-07-05 18:42:59,485 I 660937 661492] core_worker.cc:902: Event stats:


Global stats: 8 total (6 active)
Queueing time: mean = 11.424 us, max = 74.950 us, min = 16.442 us, total = 91.392 us
Execution time:  mean = 3.284 us, total = 26.273 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 8.758 us, total = 26.273 us, Queueing time: mean = 30.464 us, max = 74.950 us, min = 16.442 us, total = 91.392 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3 total (2 active)
Queueing time: mean = 2.373 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
Execution time:  mean = 53.987 us, total = 161.960 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 161.960 us, total = 161.960 us, Queueing time: mean = 7.118 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:1
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,487 I 660937 661492] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,487 I 660937 661492] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,487 I 660937 660937] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,487 I 660937 660937] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,487 I 660937 660937] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,487 I 660937 660937] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,488 W 660937 661474] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,485 I 660937 661492] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 45.573 us, max = 531.320 us, min = 8.686 us, total = 39.877 ms
Execution time:  mean = 71.339 us, total = 62.422 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.301 us, total = 4.980 ms, Queueing time: mean = 50.084 us, max = 146.576 us, min = 13.300 us, total = 30.050 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.311 us, total = 628.943 us, Queueing time: mean = 50.027 us, max = 79.533 us, min = 19.831 us, total = 3.052 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 25.737 us, total = 1.544 ms, Queueing time: mean = 32.397 us, max = 67.167 us, min = 8.985 us, total = 1.944 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 669.273 us, total = 40.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 165.376 us, total = 9.923 ms, Queueing time: mean = 42.653 us, max = 116.568 us, min = 13.411 us, total = 2.559 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 32.745 us, total = 392.943 us, Queueing time: mean = 32.737 us, max = 70.432 us, min = 15.339 us, total = 392.846 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.197 us, total = 484.376 us, Queueing time: mean = 96.119 us, max = 285.425 us, min = 8.686 us, total = 672.833 us
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.021 us, total = 30.126 us, Queueing time: mean = 28.383 us, max = 44.538 us, min = 27.050 us, total = 170.296 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 813.460 us, total = 813.460 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.313 ms, total = 1.313 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 14.612 us, total = 14.612 us, Queueing time: mean = 531.320 us, max = 531.320 us, min = 531.320 us, total = 531.320 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.969 ms, total = 1.969 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 66.624 us, total = 66.624 us, Queueing time: mean = 29.983 us, max = 29.983 us, min = 29.983 us, total = 29.983 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 105.038 us, total = 105.038 us, Queueing time: mean = 474.316 us, max = 474.316 us, min = 474.316 us, total = 474.316 us

-----------------
Task execution event stats:

Global stats: 5962 total (1 active)
Queueing time: mean = 55.406 us, max = 2.359 ms, min = -0.000 s, total = 330.328 ms
Execution time:  mean = 14.901 us, total = 88.837 ms
Event stats:
	CoreWorker.CheckSignal - 5961 total (1 active), Execution time: mean = 14.902 us, total = 88.829 ms, Queueing time: mean = 55.415 us, max = 2.359 ms, min = -0.000 s, total = 330.326 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.656 us, total = 8.656 us, Queueing time: mean = 2.581 us, max = 2.581 us, min = 2.581 us, total = 2.581 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 31.556 us, max = 115.462 us, min = 7.118 us, total = 5.712 ms
Execution time:  mean = 348.422 us, total = 63.064 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 204.175 us, total = 12.251 ms, Queueing time: mean = 56.673 us, max = 115.462 us, min = 25.661 us, total = 3.400 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 809.140 us, total = 48.548 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 35.057 us, total = 2.103 ms, Queueing time: mean = 38.402 us, max = 60.808 us, min = 11.863 us, total = 2.304 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 161.960 us, total = 161.960 us, Queueing time: mean = 7.118 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,486 I 660937 661492] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 49.463 us, max = 531.320 us, min = -0.000 s, total = 85.719 ms
Execution time:  mean = 69.952 us, total = 121.227 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.415 us, total = 10.097 ms, Queueing time: mean = 54.562 us, max = 150.954 us, min = -0.000 s, total = 65.474 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 27.787 us, total = 3.334 ms, Queueing time: mean = 34.595 us, max = 67.167 us, min = 8.985 us, total = 4.151 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.792 us, total = 1.295 ms, Queueing time: mean = 56.136 us, max = 129.793 us, min = 19.831 us, total = 6.736 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 661.004 us, total = 79.320 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 173.445 us, total = 20.813 ms, Queueing time: mean = 48.991 us, max = 116.568 us, min = 13.411 us, total = 5.879 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 35.326 us, total = 847.815 us, Queueing time: mean = 48.596 us, max = 78.126 us, min = 15.339 us, total = 1.166 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.597 us, total = 67.159 us, Queueing time: mean = 44.866 us, max = 67.973 us, min = 27.050 us, total = 538.396 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.197 us, total = 484.376 us, Queueing time: mean = 96.119 us, max = 285.425 us, min = 8.686 us, total = 672.833 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 342.172 us, total = 684.344 us, Queueing time: mean = 32.782 us, max = 65.563 us, min = 65.563 us, total = 65.563 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 813.460 us, total = 813.460 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.313 ms, total = 1.313 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 14.612 us, total = 14.612 us, Queueing time: mean = 531.320 us, max = 531.320 us, min = 531.320 us, total = 531.320 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.969 ms, total = 1.969 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 66.624 us, total = 66.624 us, Queueing time: mean = 29.983 us, max = 29.983 us, min = 29.983 us, total = 29.983 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 105.038 us, total = 105.038 us, Queueing time: mean = 474.316 us, max = 474.316 us, min = 474.316 us, total = 474.316 us

-----------------
Task execution event stats:

Global stats: 11920 total (1 active)
Queueing time: mean = 57.780 us, max = 2.359 ms, min = -0.000 s, total = 688.733 ms
Execution time:  mean = 15.249 us, total = 181.770 ms
Event stats:
	CoreWorker.CheckSignal - 11919 total (1 active), Execution time: mean = 15.250 us, total = 181.762 ms, Queueing time: mean = 57.784 us, max = 2.359 ms, min = -0.000 s, total = 688.730 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.656 us, total = 8.656 us, Queueing time: mean = 2.581 us, max = 2.581 us, min = 2.581 us, total = 2.581 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 30.393 us, max = 312.233 us, min = 7.118 us, total = 10.972 ms
Execution time:  mean = 330.559 us, total = 119.332 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 205.419 us, total = 24.650 ms, Queueing time: mean = 54.219 us, max = 312.233 us, min = 21.485 us, total = 6.506 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 752.508 us, total = 90.301 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 35.156 us, total = 4.219 ms, Queueing time: mean = 37.153 us, max = 69.164 us, min = 11.863 us, total = 4.458 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 161.960 us, total = 161.960 us, Queueing time: mean = 7.118 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,487 I 660937 661492] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 48.796 us, max = 729.534 us, min = -0.000 s, total = 126.430 ms
Execution time:  mean = 71.296 us, total = 184.728 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.622 us, total = 15.511 ms, Queueing time: mean = 53.419 us, max = 150.954 us, min = -0.000 s, total = 96.101 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.467 us, total = 5.304 ms, Queueing time: mean = 38.543 us, max = 318.779 us, min = 8.985 us, total = 6.938 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 11.281 us, total = 2.031 ms, Queueing time: mean = 58.541 us, max = 729.534 us, min = 17.568 us, total = 10.537 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 677.307 us, total = 121.915 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 179.076 us, total = 32.234 ms, Queueing time: mean = 46.891 us, max = 116.568 us, min = 13.411 us, total = 8.440 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 36.745 us, total = 1.323 ms, Queueing time: mean = 47.844 us, max = 78.126 us, min = 12.594 us, total = 1.722 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.588 us, total = 100.577 us, Queueing time: mean = 45.762 us, max = 79.005 us, min = 20.082 us, total = 823.720 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.197 us, total = 484.376 us, Queueing time: mean = 96.119 us, max = 285.425 us, min = 8.686 us, total = 672.833 us
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 514.519 us, total = 1.544 ms, Queueing time: mean = 53.236 us, max = 94.146 us, min = 65.563 us, total = 159.709 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 813.460 us, total = 813.460 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.313 ms, total = 1.313 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 14.612 us, total = 14.612 us, Queueing time: mean = 531.320 us, max = 531.320 us, min = 531.320 us, total = 531.320 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.969 ms, total = 1.969 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 66.624 us, total = 66.624 us, Queueing time: mean = 29.983 us, max = 29.983 us, min = 29.983 us, total = 29.983 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 105.038 us, total = 105.038 us, Queueing time: mean = 474.316 us, max = 474.316 us, min = 474.316 us, total = 474.316 us

-----------------
Task execution event stats:

Global stats: 17885 total (1 active)
Queueing time: mean = 54.886 us, max = 2.359 ms, min = -0.000 s, total = 981.642 ms
Execution time:  mean = 15.173 us, total = 271.369 ms
Event stats:
	CoreWorker.CheckSignal - 17884 total (1 active), Execution time: mean = 15.173 us, total = 271.360 ms, Queueing time: mean = 54.889 us, max = 2.359 ms, min = -0.000 s, total = 981.639 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.656 us, total = 8.656 us, Queueing time: mean = 2.581 us, max = 2.581 us, min = 2.581 us, total = 2.581 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 32.311 us, max = 872.888 us, min = 7.118 us, total = 17.480 ms
Execution time:  mean = 325.818 us, total = 176.268 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 203.944 us, total = 36.710 ms, Queueing time: mean = 58.034 us, max = 872.888 us, min = 18.940 us, total = 10.446 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 738.072 us, total = 132.853 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 36.349 us, total = 6.543 ms, Queueing time: mean = 39.039 us, max = 88.434 us, min = 11.863 us, total = 7.027 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 161.960 us, total = 161.960 us, Queueing time: mean = 7.118 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,488 I 660937 661492] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 49.206 us, max = 729.534 us, min = -0.000 s, total = 169.759 ms
Execution time:  mean = 72.009 us, total = 248.431 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.505 us, total = 20.403 ms, Queueing time: mean = 54.068 us, max = 150.954 us, min = -0.000 s, total = 129.710 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.775 us, total = 7.146 ms, Queueing time: mean = 40.571 us, max = 318.779 us, min = 8.985 us, total = 9.737 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 11.279 us, total = 2.707 ms, Queueing time: mean = 56.942 us, max = 729.534 us, min = 16.806 us, total = 13.666 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 690.622 us, total = 165.749 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 181.566 us, total = 43.576 ms, Queueing time: mean = 46.168 us, max = 120.192 us, min = 13.411 us, total = 11.080 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 36.508 us, total = 1.752 ms, Queueing time: mean = 50.082 us, max = 116.165 us, min = 12.594 us, total = 2.404 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.735 us, total = 137.635 us, Queueing time: mean = 50.603 us, max = 79.005 us, min = 20.082 us, total = 1.214 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.197 us, total = 484.376 us, Queueing time: mean = 96.119 us, max = 285.425 us, min = 8.686 us, total = 672.833 us
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 548.523 us, total = 2.194 ms, Queueing time: mean = 59.818 us, max = 94.146 us, min = 65.563 us, total = 239.272 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 813.460 us, total = 813.460 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.313 ms, total = 1.313 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 14.612 us, total = 14.612 us, Queueing time: mean = 531.320 us, max = 531.320 us, min = 531.320 us, total = 531.320 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.969 ms, total = 1.969 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 66.624 us, total = 66.624 us, Queueing time: mean = 29.983 us, max = 29.983 us, min = 29.983 us, total = 29.983 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 105.038 us, total = 105.038 us, Queueing time: mean = 474.316 us, max = 474.316 us, min = 474.316 us, total = 474.316 us

-----------------
Task execution event stats:

Global stats: 23850 total (1 active)
Queueing time: mean = 53.366 us, max = 2.359 ms, min = -0.000 s, total = 1.273 s
Execution time:  mean = 15.269 us, total = 364.160 ms
Event stats:
	CoreWorker.CheckSignal - 23849 total (1 active), Execution time: mean = 15.269 us, total = 364.151 ms, Queueing time: mean = 53.368 us, max = 2.359 ms, min = -0.000 s, total = 1.273 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.656 us, total = 8.656 us, Queueing time: mean = 2.581 us, max = 2.581 us, min = 2.581 us, total = 2.581 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 31.919 us, max = 872.888 us, min = 7.118 us, total = 23.014 ms
Execution time:  mean = 331.187 us, total = 238.786 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 208.387 us, total = 50.013 ms, Queueing time: mean = 56.472 us, max = 872.888 us, min = 18.940 us, total = 13.553 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 748.252 us, total = 179.581 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 37.628 us, total = 9.031 ms, Queueing time: mean = 39.389 us, max = 88.434 us, min = 11.863 us, total = 9.453 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 161.960 us, total = 161.960 us, Queueing time: mean = 7.118 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,488 I 660937 661492] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 49.660 us, max = 729.534 us, min = -0.000 s, total = 214.033 ms
Execution time:  mean = 73.321 us, total = 316.013 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.443 us, total = 25.320 ms, Queueing time: mean = 54.785 us, max = 150.954 us, min = -0.000 s, total = 164.301 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.702 us, total = 8.911 ms, Queueing time: mean = 38.617 us, max = 318.779 us, min = 8.985 us, total = 11.585 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.129 us, total = 3.339 ms, Queueing time: mean = 56.486 us, max = 729.534 us, min = 16.806 us, total = 16.946 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 712.845 us, total = 213.854 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 182.125 us, total = 54.638 ms, Queueing time: mean = 48.209 us, max = 120.192 us, min = 13.411 us, total = 14.463 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.831 us, total = 2.150 ms, Queueing time: mean = 51.666 us, max = 116.165 us, min = 12.594 us, total = 3.100 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.659 us, total = 169.773 us, Queueing time: mean = 53.702 us, max = 79.933 us, min = 20.082 us, total = 1.611 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.197 us, total = 484.376 us, Queueing time: mean = 96.119 us, max = 285.425 us, min = 8.686 us, total = 672.833 us
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 570.602 us, total = 2.853 ms, Queueing time: mean = 55.764 us, max = 94.146 us, min = 39.549 us, total = 278.821 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.842 us, total = 13.684 us, Queueing time: mean = 19.925 us, max = 39.851 us, min = 39.851 us, total = 39.851 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 813.460 us, total = 813.460 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.313 ms, total = 1.313 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 14.612 us, total = 14.612 us, Queueing time: mean = 531.320 us, max = 531.320 us, min = 531.320 us, total = 531.320 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.969 ms, total = 1.969 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 66.624 us, total = 66.624 us, Queueing time: mean = 29.983 us, max = 29.983 us, min = 29.983 us, total = 29.983 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 105.038 us, total = 105.038 us, Queueing time: mean = 474.316 us, max = 474.316 us, min = 474.316 us, total = 474.316 us

-----------------
Task execution event stats:

Global stats: 29809 total (1 active)
Queueing time: mean = 54.437 us, max = 2.359 ms, min = -0.000 s, total = 1.623 s
Execution time:  mean = 15.247 us, total = 454.502 ms
Event stats:
	CoreWorker.CheckSignal - 29808 total (1 active), Execution time: mean = 15.247 us, total = 454.493 ms, Queueing time: mean = 54.438 us, max = 2.359 ms, min = -0.000 s, total = 1.623 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.656 us, total = 8.656 us, Queueing time: mean = 2.581 us, max = 2.581 us, min = 2.581 us, total = 2.581 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 31.943 us, max = 872.888 us, min = 7.118 us, total = 28.780 ms
Execution time:  mean = 330.335 us, total = 297.632 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 209.538 us, total = 62.861 ms, Queueing time: mean = 56.614 us, max = 872.888 us, min = 18.940 us, total = 16.984 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 744.257 us, total = 223.277 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 37.773 us, total = 11.332 ms, Queueing time: mean = 39.297 us, max = 88.434 us, min = 11.863 us, total = 11.789 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 161.960 us, total = 161.960 us, Queueing time: mean = 7.118 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,489 I 660937 661492] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.257 us, max = 729.534 us, min = -0.000 s, total = 259.727 ms
Execution time:  mean = 73.964 us, total = 382.245 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.430 us, total = 30.331 ms, Queueing time: mean = 55.466 us, max = 150.954 us, min = -0.000 s, total = 199.567 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.682 us, total = 10.686 ms, Queueing time: mean = 37.917 us, max = 318.779 us, min = 8.985 us, total = 13.650 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.061 us, total = 3.982 ms, Queueing time: mean = 56.371 us, max = 729.534 us, min = 16.026 us, total = 20.293 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 722.439 us, total = 260.078 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 183.340 us, total = 66.002 ms, Queueing time: mean = 50.887 us, max = 315.593 us, min = 13.411 us, total = 18.319 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 37.215 us, total = 2.679 ms, Queueing time: mean = 53.990 us, max = 116.165 us, min = 12.594 us, total = 3.887 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.828 us, total = 209.823 us, Queueing time: mean = 54.037 us, max = 80.639 us, min = 20.082 us, total = 1.945 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.197 us, total = 484.376 us, Queueing time: mean = 96.119 us, max = 285.425 us, min = 8.686 us, total = 672.833 us
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 582.711 us, total = 3.496 ms, Queueing time: mean = 52.628 us, max = 94.146 us, min = 36.949 us, total = 315.770 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.842 us, total = 13.684 us, Queueing time: mean = 19.925 us, max = 39.851 us, min = 39.851 us, total = 39.851 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 813.460 us, total = 813.460 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.313 ms, total = 1.313 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 14.612 us, total = 14.612 us, Queueing time: mean = 531.320 us, max = 531.320 us, min = 531.320 us, total = 531.320 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.969 ms, total = 1.969 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 66.624 us, total = 66.624 us, Queueing time: mean = 29.983 us, max = 29.983 us, min = 29.983 us, total = 29.983 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 105.038 us, total = 105.038 us, Queueing time: mean = 474.316 us, max = 474.316 us, min = 474.316 us, total = 474.316 us

-----------------
Task execution event stats:

Global stats: 35768 total (1 active)
Queueing time: mean = 55.112 us, max = 14.733 ms, min = -0.000 s, total = 1.971 s
Execution time:  mean = 15.254 us, total = 545.587 ms
Event stats:
	CoreWorker.CheckSignal - 35767 total (1 active), Execution time: mean = 15.254 us, total = 545.579 ms, Queueing time: mean = 55.114 us, max = 14.733 ms, min = -0.000 s, total = 1.971 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.656 us, total = 8.656 us, Queueing time: mean = 2.581 us, max = 2.581 us, min = 2.581 us, total = 2.581 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 31.040 us, max = 872.888 us, min = 7.118 us, total = 33.554 ms
Execution time:  mean = 326.466 us, total = 352.910 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 207.891 us, total = 74.841 ms, Queueing time: mean = 53.653 us, max = 872.888 us, min = 18.940 us, total = 19.315 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 734.433 us, total = 264.396 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 37.530 us, total = 13.511 ms, Queueing time: mean = 39.533 us, max = 88.434 us, min = 11.863 us, total = 14.232 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 161.960 us, total = 161.960 us, Queueing time: mean = 7.118 us, max = 7.118 us, min = 7.118 us, total = 7.118 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660937 661492] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660937 661492] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660937 661492] core_worker.cc:5107: Number of alive nodes:0
