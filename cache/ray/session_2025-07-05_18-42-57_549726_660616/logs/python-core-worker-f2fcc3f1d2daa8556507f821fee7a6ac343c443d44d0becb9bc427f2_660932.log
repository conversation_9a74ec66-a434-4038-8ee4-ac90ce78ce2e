[2025-07-05 18:42:59,521 I 660932 660932] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660932
[2025-07-05 18:42:59,523 I 660932 660932] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,525 I 660932 660932] grpc_server.cc:141: worker server started, listening on port 42199.
[2025-07-05 18:42:59,526 I 660932 660932] core_worker.cc:542: Initializing worker at address: ***********:42199 worker_id=f2fcc3f1d2daa8556507f821fee7a6ac343c443d44d0becb9bc427f2 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,527 I 660932 660932] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,528 I 660932 661898] core_worker.cc:902: Event stats:


Global stats: 11 total (5 active)
Queueing time: mean = 9.233 us, max = 70.149 us, min = 7.860 us, total = 101.568 us
Execution time:  mean = 90.708 us, total = 997.787 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 7.461 us, total = 22.382 us, Queueing time: mean = 27.612 us, max = 70.149 us, min = 12.686 us, total = 82.835 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.926 us, total = 106.926 us, Queueing time: mean = 7.860 us, max = 7.860 us, min = 7.860 us, total = 7.860 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 474.956 us, total = 474.956 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.257 us, total = 17.257 us, Queueing time: mean = 10.873 us, max = 10.873 us, min = 10.873 us, total = 10.873 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.266 us, total = 376.266 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 5.029 us, max = 13.353 us, min = 6.765 us, total = 20.118 us
Execution time:  mean = 156.939 us, total = 627.756 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.769 us, total = 17.769 us, Queueing time: mean = 13.353 us, max = 13.353 us, min = 13.353 us, total = 13.353 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 111.067 us, total = 111.067 us, Queueing time: mean = 6.765 us, max = 6.765 us, min = 6.765 us, total = 6.765 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 498.920 us, total = 498.920 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,528 I 660932 661898] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,528 I 660932 661898] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,528 I 660932 660932] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,528 I 660932 660932] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,529 I 660932 660932] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,529 I 660932 660932] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,533 W 660932 661890] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,529 I 660932 661898] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 49.448 us, max = 106.924 us, min = 3.870 us, total = 43.267 ms
Execution time:  mean = 65.604 us, total = 57.404 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.058 us, total = 4.835 ms, Queueing time: mean = 57.230 us, max = 106.924 us, min = 14.426 us, total = 34.338 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 9.767 us, total = 595.789 us, Queueing time: mean = 55.984 us, max = 93.521 us, min = 23.579 us, total = 3.415 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.259 us, total = 1.636 ms, Queueing time: mean = 34.756 us, max = 70.680 us, min = 9.635 us, total = 2.085 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 642.640 us, total = 38.558 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 158.806 us, total = 9.528 ms, Queueing time: mean = 43.469 us, max = 74.858 us, min = 8.753 us, total = 2.608 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 29.346 us, total = 352.151 us, Queueing time: mean = 37.694 us, max = 77.800 us, min = 8.062 us, total = 452.327 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.655 us, total = 487.582 us, Queueing time: mean = 18.473 us, max = 70.149 us, min = 6.553 us, total = 129.308 us
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.841 us, total = 29.047 us, Queueing time: mean = 35.993 us, max = 74.727 us, min = 15.593 us, total = 215.961 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 474.956 us, total = 474.956 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.926 us, total = 106.926 us, Queueing time: mean = 7.860 us, max = 7.860 us, min = 7.860 us, total = 7.860 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.257 us, total = 17.257 us, Queueing time: mean = 10.873 us, max = 10.873 us, min = 10.873 us, total = 10.873 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.806 us, total = 38.806 us, Queueing time: mean = 3.870 us, max = 3.870 us, min = 3.870 us, total = 3.870 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 367.816 us, total = 367.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.266 us, total = 376.266 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5964 total (1 active)
Queueing time: mean = 52.330 us, max = 2.028 ms, min = -0.000 s, total = 312.094 ms
Execution time:  mean = 14.692 us, total = 87.626 ms
Event stats:
	CoreWorker.CheckSignal - 5963 total (1 active), Execution time: mean = 14.694 us, total = 87.618 ms, Queueing time: mean = 52.338 us, max = 2.028 ms, min = -0.000 s, total = 312.091 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.952 us, total = 7.952 us, Queueing time: mean = 2.460 us, max = 2.460 us, min = 2.460 us, total = 2.460 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 27.019 us, max = 98.749 us, min = 6.765 us, total = 4.890 ms
Execution time:  mean = 295.043 us, total = 53.403 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 190.988 us, total = 11.459 ms, Queueing time: mean = 49.961 us, max = 98.749 us, min = 24.492 us, total = 2.998 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 31.971 us, total = 1.918 ms, Queueing time: mean = 31.434 us, max = 87.754 us, min = 12.029 us, total = 1.886 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 665.236 us, total = 39.914 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 111.067 us, total = 111.067 us, Queueing time: mean = 6.765 us, max = 6.765 us, min = 6.765 us, total = 6.765 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,529 I 660932 661898] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 52.228 us, max = 106.924 us, min = 3.870 us, total = 90.512 ms
Execution time:  mean = 68.165 us, total = 118.130 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.106 us, total = 9.727 ms, Queueing time: mean = 59.625 us, max = 106.924 us, min = 14.426 us, total = 71.550 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.472 us, total = 3.417 ms, Queueing time: mean = 39.173 us, max = 70.680 us, min = 9.635 us, total = 4.701 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 671.525 us, total = 80.583 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 165.826 us, total = 19.899 ms, Queueing time: mean = 46.719 us, max = 101.988 us, min = 8.753 us, total = 5.606 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 9.948 us, total = 1.194 ms, Queueing time: mean = 56.906 us, max = 93.521 us, min = 16.946 us, total = 6.829 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 30.857 us, total = 740.559 us, Queueing time: mean = 48.536 us, max = 93.236 us, min = 8.062 us, total = 1.165 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.851 us, total = 58.209 us, Queueing time: mean = 40.819 us, max = 74.727 us, min = 15.593 us, total = 489.826 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.655 us, total = 487.582 us, Queueing time: mean = 18.473 us, max = 70.149 us, min = 6.553 us, total = 129.308 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 321.341 us, total = 642.681 us, Queueing time: mean = 9.790 us, max = 19.581 us, min = 19.581 us, total = 19.581 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 474.956 us, total = 474.956 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.926 us, total = 106.926 us, Queueing time: mean = 7.860 us, max = 7.860 us, min = 7.860 us, total = 7.860 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.257 us, total = 17.257 us, Queueing time: mean = 10.873 us, max = 10.873 us, min = 10.873 us, total = 10.873 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.806 us, total = 38.806 us, Queueing time: mean = 3.870 us, max = 3.870 us, min = 3.870 us, total = 3.870 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 367.816 us, total = 367.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.266 us, total = 376.266 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11927 total (1 active)
Queueing time: mean = 52.067 us, max = 2.028 ms, min = -0.000 s, total = 621.006 ms
Execution time:  mean = 14.908 us, total = 177.811 ms
Event stats:
	CoreWorker.CheckSignal - 11926 total (1 active, 1 running), Execution time: mean = 14.909 us, total = 177.803 ms, Queueing time: mean = 52.068 us, max = 2.028 ms, min = -0.000 s, total = 620.967 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.952 us, total = 7.952 us, Queueing time: mean = 2.460 us, max = 2.460 us, min = 2.460 us, total = 2.460 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 27.136 us, max = 106.979 us, min = 6.765 us, total = 9.796 ms
Execution time:  mean = 305.036 us, total = 110.118 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 192.550 us, total = 23.106 ms, Queueing time: mean = 48.582 us, max = 106.979 us, min = 21.795 us, total = 5.830 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 32.874 us, total = 3.945 ms, Queueing time: mean = 32.995 us, max = 87.754 us, min = 12.029 us, total = 3.959 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 691.301 us, total = 82.956 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 111.067 us, total = 111.067 us, Queueing time: mean = 6.765 us, max = 6.765 us, min = 6.765 us, total = 6.765 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,530 I 660932 661898] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 49.586 us, max = 514.719 us, min = 3.870 us, total = 128.476 ms
Execution time:  mean = 68.917 us, total = 178.563 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.166 us, total = 14.690 ms, Queueing time: mean = 55.646 us, max = 136.409 us, min = 14.426 us, total = 100.108 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 28.918 us, total = 5.205 ms, Queueing time: mean = 39.889 us, max = 116.804 us, min = 9.635 us, total = 7.180 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 673.012 us, total = 121.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 172.767 us, total = 31.098 ms, Queueing time: mean = 46.786 us, max = 101.988 us, min = 8.753 us, total = 8.421 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.241 us, total = 1.843 ms, Queueing time: mean = 53.438 us, max = 93.521 us, min = 15.121 us, total = 9.619 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.149 us, total = 1.265 ms, Queueing time: mean = 44.575 us, max = 93.236 us, min = 8.062 us, total = 1.605 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.962 us, total = 89.309 us, Queueing time: mean = 72.138 us, max = 514.719 us, min = 15.142 us, total = 1.298 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.655 us, total = 487.582 us, Queueing time: mean = 18.473 us, max = 70.149 us, min = 6.553 us, total = 129.308 us
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 453.304 us, total = 1.360 ms, Queueing time: mean = 31.118 us, max = 73.774 us, min = 19.581 us, total = 93.355 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 474.956 us, total = 474.956 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.926 us, total = 106.926 us, Queueing time: mean = 7.860 us, max = 7.860 us, min = 7.860 us, total = 7.860 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.257 us, total = 17.257 us, Queueing time: mean = 10.873 us, max = 10.873 us, min = 10.873 us, total = 10.873 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.806 us, total = 38.806 us, Queueing time: mean = 3.870 us, max = 3.870 us, min = 3.870 us, total = 3.870 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 367.816 us, total = 367.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.266 us, total = 376.266 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17890 total (1 active)
Queueing time: mean = 52.140 us, max = 2.028 ms, min = -0.000 s, total = 932.790 ms
Execution time:  mean = 15.050 us, total = 269.240 ms
Event stats:
	CoreWorker.CheckSignal - 17889 total (1 active), Execution time: mean = 15.050 us, total = 269.232 ms, Queueing time: mean = 52.143 us, max = 2.028 ms, min = -0.000 s, total = 932.788 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.952 us, total = 7.952 us, Queueing time: mean = 2.460 us, max = 2.460 us, min = 2.460 us, total = 2.460 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 27.888 us, max = 106.979 us, min = 6.765 us, total = 15.088 ms
Execution time:  mean = 310.590 us, total = 168.029 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 194.527 us, total = 35.015 ms, Queueing time: mean = 52.589 us, max = 106.979 us, min = 21.795 us, total = 9.466 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 33.161 us, total = 5.969 ms, Queueing time: mean = 31.193 us, max = 87.754 us, min = 12.029 us, total = 5.615 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 705.192 us, total = 126.934 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 111.067 us, total = 111.067 us, Queueing time: mean = 6.765 us, max = 6.765 us, min = 6.765 us, total = 6.765 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,531 I 660932 661898] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 49.287 us, max = 514.719 us, min = 3.870 us, total = 170.040 ms
Execution time:  mean = 69.107 us, total = 238.421 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.171 us, total = 19.603 ms, Queueing time: mean = 54.921 us, max = 136.409 us, min = 12.344 us, total = 131.755 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.319 us, total = 7.036 ms, Queueing time: mean = 40.334 us, max = 116.804 us, min = 9.635 us, total = 9.680 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 671.444 us, total = 161.146 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 176.674 us, total = 42.402 ms, Queueing time: mean = 47.908 us, max = 101.988 us, min = 8.753 us, total = 11.498 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.346 us, total = 2.483 ms, Queueing time: mean = 52.813 us, max = 103.137 us, min = 15.121 us, total = 12.675 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.842 us, total = 1.720 ms, Queueing time: mean = 45.943 us, max = 93.236 us, min = 8.062 us, total = 2.205 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.045 us, total = 121.075 us, Queueing time: mean = 81.283 us, max = 514.719 us, min = 15.142 us, total = 1.951 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.655 us, total = 487.582 us, Queueing time: mean = 18.473 us, max = 70.149 us, min = 6.553 us, total = 129.308 us
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 509.691 us, total = 2.039 ms, Queueing time: mean = 30.946 us, max = 73.774 us, min = 19.581 us, total = 123.783 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 474.956 us, total = 474.956 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.926 us, total = 106.926 us, Queueing time: mean = 7.860 us, max = 7.860 us, min = 7.860 us, total = 7.860 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.257 us, total = 17.257 us, Queueing time: mean = 10.873 us, max = 10.873 us, min = 10.873 us, total = 10.873 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.806 us, total = 38.806 us, Queueing time: mean = 3.870 us, max = 3.870 us, min = 3.870 us, total = 3.870 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 367.816 us, total = 367.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.266 us, total = 376.266 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23848 total (1 active)
Queueing time: mean = 54.275 us, max = 2.028 ms, min = -0.000 s, total = 1.294 s
Execution time:  mean = 15.030 us, total = 358.442 ms
Event stats:
	CoreWorker.CheckSignal - 23847 total (1 active), Execution time: mean = 15.031 us, total = 358.434 ms, Queueing time: mean = 54.277 us, max = 2.028 ms, min = -0.000 s, total = 1.294 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.952 us, total = 7.952 us, Queueing time: mean = 2.460 us, max = 2.460 us, min = 2.460 us, total = 2.460 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 28.801 us, max = 106.979 us, min = 6.765 us, total = 20.765 ms
Execution time:  mean = 315.505 us, total = 227.479 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 195.869 us, total = 47.008 ms, Queueing time: mean = 54.478 us, max = 106.979 us, min = 16.632 us, total = 13.075 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 33.894 us, total = 8.135 ms, Queueing time: mean = 32.017 us, max = 87.754 us, min = 12.029 us, total = 7.684 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 717.603 us, total = 172.225 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 111.067 us, total = 111.067 us, Queueing time: mean = 6.765 us, max = 6.765 us, min = 6.765 us, total = 6.765 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,531 I 660932 661898] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 49.681 us, max = 514.719 us, min = 3.870 us, total = 214.126 ms
Execution time:  mean = 69.252 us, total = 298.476 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.142 us, total = 24.418 ms, Queueing time: mean = 55.155 us, max = 186.452 us, min = 12.344 us, total = 165.411 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.603 us, total = 8.881 ms, Queueing time: mean = 39.923 us, max = 116.804 us, min = 9.635 us, total = 11.977 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 673.392 us, total = 202.018 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 177.021 us, total = 53.106 ms, Queueing time: mean = 48.659 us, max = 111.140 us, min = 8.753 us, total = 14.598 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.417 us, total = 3.125 ms, Queueing time: mean = 53.852 us, max = 103.137 us, min = 15.121 us, total = 16.155 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 36.225 us, total = 2.174 ms, Queueing time: mean = 49.661 us, max = 93.236 us, min = 8.062 us, total = 2.980 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.054 us, total = 151.629 us, Queueing time: mean = 87.160 us, max = 514.719 us, min = 15.142 us, total = 2.615 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.655 us, total = 487.582 us, Queueing time: mean = 18.473 us, max = 70.149 us, min = 6.553 us, total = 129.308 us
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 545.026 us, total = 2.725 ms, Queueing time: mean = 30.552 us, max = 73.774 us, min = 19.581 us, total = 152.761 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.347 us, total = 8.694 us, Queueing time: mean = 42.766 us, max = 85.531 us, min = 85.531 us, total = 85.531 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 474.956 us, total = 474.956 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.926 us, total = 106.926 us, Queueing time: mean = 7.860 us, max = 7.860 us, min = 7.860 us, total = 7.860 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.257 us, total = 17.257 us, Queueing time: mean = 10.873 us, max = 10.873 us, min = 10.873 us, total = 10.873 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.806 us, total = 38.806 us, Queueing time: mean = 3.870 us, max = 3.870 us, min = 3.870 us, total = 3.870 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 367.816 us, total = 367.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.266 us, total = 376.266 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29807 total (1 active)
Queueing time: mean = 55.201 us, max = 2.028 ms, min = -0.000 s, total = 1.645 s
Execution time:  mean = 15.001 us, total = 447.131 ms
Event stats:
	CoreWorker.CheckSignal - 29806 total (1 active), Execution time: mean = 15.001 us, total = 447.123 ms, Queueing time: mean = 55.202 us, max = 2.028 ms, min = -0.000 s, total = 1.645 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.952 us, total = 7.952 us, Queueing time: mean = 2.460 us, max = 2.460 us, min = 2.460 us, total = 2.460 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 29.626 us, max = 184.221 us, min = 6.765 us, total = 26.693 ms
Execution time:  mean = 319.563 us, total = 287.926 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 196.138 us, total = 58.841 ms, Queueing time: mean = 55.611 us, max = 106.979 us, min = 16.632 us, total = 16.683 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 34.308 us, total = 10.292 ms, Queueing time: mean = 33.345 us, max = 184.221 us, min = 12.029 us, total = 10.004 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 728.938 us, total = 218.682 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 111.067 us, total = 111.067 us, Queueing time: mean = 6.765 us, max = 6.765 us, min = 6.765 us, total = 6.765 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,532 I 660932 661898] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.454 us, max = 514.719 us, min = 3.870 us, total = 260.748 ms
Execution time:  mean = 70.066 us, total = 362.099 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.116 us, total = 29.202 ms, Queueing time: mean = 55.961 us, max = 186.452 us, min = 12.344 us, total = 201.346 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.351 us, total = 10.566 ms, Queueing time: mean = 38.597 us, max = 116.804 us, min = 9.635 us, total = 13.895 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 687.826 us, total = 247.617 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 174.882 us, total = 62.958 ms, Queueing time: mean = 50.767 us, max = 111.140 us, min = 8.753 us, total = 18.276 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.428 us, total = 3.754 ms, Queueing time: mean = 55.155 us, max = 103.137 us, min = 15.121 us, total = 19.856 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.892 us, total = 2.584 ms, Queueing time: mean = 50.338 us, max = 93.236 us, min = 8.062 us, total = 3.624 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.029 us, total = 181.029 us, Queueing time: mean = 92.299 us, max = 514.719 us, min = 15.142 us, total = 3.323 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 69.655 us, total = 487.582 us, Queueing time: mean = 18.473 us, max = 70.149 us, min = 6.553 us, total = 129.308 us
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 559.692 us, total = 3.358 ms, Queueing time: mean = 31.780 us, max = 73.774 us, min = 19.581 us, total = 190.680 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.347 us, total = 8.694 us, Queueing time: mean = 42.766 us, max = 85.531 us, min = 85.531 us, total = 85.531 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 474.956 us, total = 474.956 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.926 us, total = 106.926 us, Queueing time: mean = 7.860 us, max = 7.860 us, min = 7.860 us, total = 7.860 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.257 us, total = 17.257 us, Queueing time: mean = 10.873 us, max = 10.873 us, min = 10.873 us, total = 10.873 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.806 us, total = 38.806 us, Queueing time: mean = 3.870 us, max = 3.870 us, min = 3.870 us, total = 3.870 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 367.816 us, total = 367.816 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.266 us, total = 376.266 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35766 total (1 active)
Queueing time: mean = 55.945 us, max = 2.028 ms, min = -0.000 s, total = 2.001 s
Execution time:  mean = 14.958 us, total = 534.997 ms
Event stats:
	CoreWorker.CheckSignal - 35765 total (1 active), Execution time: mean = 14.958 us, total = 534.989 ms, Queueing time: mean = 55.947 us, max = 2.028 ms, min = -0.000 s, total = 2.001 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.952 us, total = 7.952 us, Queueing time: mean = 2.460 us, max = 2.460 us, min = 2.460 us, total = 2.460 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 30.630 us, max = 184.221 us, min = 6.765 us, total = 33.112 ms
Execution time:  mean = 322.503 us, total = 348.626 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 196.111 us, total = 70.600 ms, Queueing time: mean = 56.951 us, max = 106.979 us, min = 16.632 us, total = 20.502 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 34.655 us, total = 12.476 ms, Queueing time: mean = 35.007 us, max = 184.221 us, min = 12.029 us, total = 12.603 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 737.331 us, total = 265.439 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 111.067 us, total = 111.067 us, Queueing time: mean = 6.765 us, max = 6.765 us, min = 6.765 us, total = 6.765 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660932 661898] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660932 661898] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660932 661898] core_worker.cc:5107: Number of alive nodes:0
