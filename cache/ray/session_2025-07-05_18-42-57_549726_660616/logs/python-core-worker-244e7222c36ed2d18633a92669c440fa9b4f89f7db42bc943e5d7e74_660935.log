[2025-07-05 18:42:59,521 I 660935 660935] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660935
[2025-07-05 18:42:59,522 I 660935 660935] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,524 I 660935 660935] grpc_server.cc:141: worker server started, listening on port 33777.
[2025-07-05 18:42:59,525 I 660935 660935] core_worker.cc:542: Initializing worker at address: ***********:33777 worker_id=244e7222c36ed2d18633a92669c440fa9b4f89f7db42bc943e5d7e74 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,526 I 660935 660935] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,527 I 660935 660935] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,527 I 660935 661889] core_worker.cc:902: Event stats:


Global stats: 16 total (9 active)
Queueing time: mean = 7.220 us, max = 77.612 us, min = 9.833 us, total = 115.522 us
Execution time:  mean = 81.736 us, total = 1.308 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 3.276 us, total = 22.933 us, Queueing time: mean = 13.268 us, max = 77.612 us, min = 15.265 us, total = 92.877 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 21.858 us, total = 21.858 us, Queueing time: mean = 9.833 us, max = 9.833 us, min = 9.833 us, total = 9.833 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 373.097 us, total = 373.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 366.750 us, total = 366.750 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 406.836 us, total = 406.836 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 116.306 us, total = 116.306 us, Queueing time: mean = 12.812 us, max = 12.812 us, min = 12.812 us, total = 12.812 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 51.734 us, max = 174.267 us, min = 32.668 us, total = 206.935 us
Execution time:  mean = 147.324 us, total = 589.295 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 115.725 us, total = 115.725 us, Queueing time: mean = 174.267 us, max = 174.267 us, min = 174.267 us, total = 174.267 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 455.640 us, total = 455.640 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.930 us, total = 17.930 us, Queueing time: mean = 32.668 us, max = 32.668 us, min = 32.668 us, total = 32.668 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,527 I 660935 660935] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,527 I 660935 660935] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,527 I 660935 660935] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,528 I 660935 661889] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,528 I 660935 661889] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,532 W 660935 661884] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,528 I 660935 661889] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 52.531 us, max = 574.691 us, min = 9.833 us, total = 45.965 ms
Execution time:  mean = 72.095 us, total = 63.083 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.256 us, total = 4.954 ms, Queueing time: mean = 57.428 us, max = 115.848 us, min = 14.561 us, total = 34.457 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.228 us, total = 623.938 us, Queueing time: mean = 51.821 us, max = 78.253 us, min = 22.172 us, total = 3.161 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 165.589 us, total = 9.935 ms, Queueing time: mean = 45.939 us, max = 92.271 us, min = 15.033 us, total = 2.756 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 28.006 us, total = 1.680 ms, Queueing time: mean = 40.004 us, max = 96.837 us, min = 10.227 us, total = 2.400 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 726.098 us, total = 43.566 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 32.317 us, total = 387.800 us, Queueing time: mean = 36.877 us, max = 77.265 us, min = 13.612 us, total = 442.521 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 81.280 us, total = 568.960 us, Queueing time: mean = 264.183 us, max = 574.691 us, min = 15.265 us, total = 1.849 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.231 us, total = 31.383 us, Queueing time: mean = 52.512 us, max = 75.748 us, min = 36.848 us, total = 315.070 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 21.858 us, total = 21.858 us, Queueing time: mean = 9.833 us, max = 9.833 us, min = 9.833 us, total = 9.833 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 406.836 us, total = 406.836 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 373.097 us, total = 373.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 366.750 us, total = 366.750 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 116.306 us, total = 116.306 us, Queueing time: mean = 12.812 us, max = 12.812 us, min = 12.812 us, total = 12.812 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 50.410 us, total = 50.410 us, Queueing time: mean = 560.959 us, max = 560.959 us, min = 560.959 us, total = 560.959 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5961 total (1 active)
Queueing time: mean = 57.387 us, max = 2.638 ms, min = -0.000 s, total = 342.082 ms
Execution time:  mean = 15.217 us, total = 90.709 ms
Event stats:
	CoreWorker.CheckSignal - 5960 total (1 active), Execution time: mean = 15.218 us, total = 90.701 ms, Queueing time: mean = 57.396 us, max = 2.638 ms, min = -0.000 s, total = 342.079 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.795 us, total = 7.795 us, Queueing time: mean = 2.555 us, max = 2.555 us, min = 2.555 us, total = 2.555 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 30.428 us, max = 174.267 us, min = 11.127 us, total = 5.507 ms
Execution time:  mean = 316.029 us, total = 57.201 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 197.980 us, total = 11.879 ms, Queueing time: mean = 53.871 us, max = 108.070 us, min = 17.319 us, total = 3.232 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 717.987 us, total = 43.079 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 35.458 us, total = 2.128 ms, Queueing time: mean = 35.015 us, max = 101.350 us, min = 11.127 us, total = 2.101 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 115.725 us, total = 115.725 us, Queueing time: mean = 174.267 us, max = 174.267 us, min = 174.267 us, total = 174.267 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,529 I 660935 661889] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 51.747 us, max = 574.691 us, min = 9.833 us, total = 89.678 ms
Execution time:  mean = 72.137 us, total = 125.013 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.299 us, total = 9.959 ms, Queueing time: mean = 57.765 us, max = 161.291 us, min = 14.561 us, total = 69.318 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 174.805 us, total = 20.977 ms, Queueing time: mean = 45.301 us, max = 92.271 us, min = 15.033 us, total = 5.436 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.376 us, total = 1.245 ms, Queueing time: mean = 51.461 us, max = 78.253 us, min = 16.161 us, total = 6.175 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.782 us, total = 3.454 ms, Queueing time: mean = 39.605 us, max = 96.837 us, min = 10.227 us, total = 4.753 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 715.143 us, total = 85.817 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 35.924 us, total = 862.167 us, Queueing time: mean = 39.051 us, max = 77.265 us, min = 12.422 us, total = 937.233 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.854 us, total = 58.251 us, Queueing time: mean = 49.933 us, max = 140.318 us, min = 16.556 us, total = 599.194 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 81.280 us, total = 568.960 us, Queueing time: mean = 264.183 us, max = 574.691 us, min = 15.265 us, total = 1.849 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 368.469 us, total = 736.938 us, Queueing time: mean = 13.413 us, max = 26.826 us, min = 26.826 us, total = 26.826 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 21.858 us, total = 21.858 us, Queueing time: mean = 9.833 us, max = 9.833 us, min = 9.833 us, total = 9.833 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 406.836 us, total = 406.836 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 373.097 us, total = 373.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 366.750 us, total = 366.750 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 116.306 us, total = 116.306 us, Queueing time: mean = 12.812 us, max = 12.812 us, min = 12.812 us, total = 12.812 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 50.410 us, total = 50.410 us, Queueing time: mean = 560.959 us, max = 560.959 us, min = 560.959 us, total = 560.959 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11920 total (1 active)
Queueing time: mean = 58.336 us, max = 2.638 ms, min = -0.000 s, total = 695.362 ms
Execution time:  mean = 15.213 us, total = 181.342 ms
Event stats:
	CoreWorker.CheckSignal - 11919 total (1 active), Execution time: mean = 15.214 us, total = 181.334 ms, Queueing time: mean = 58.340 us, max = 2.638 ms, min = -0.000 s, total = 695.360 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.795 us, total = 7.795 us, Queueing time: mean = 2.555 us, max = 2.555 us, min = 2.555 us, total = 2.555 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 30.024 us, max = 174.267 us, min = 11.127 us, total = 10.839 ms
Execution time:  mean = 310.230 us, total = 111.993 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 202.341 us, total = 24.281 ms, Queueing time: mean = 51.090 us, max = 108.070 us, min = 17.319 us, total = 6.131 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 694.318 us, total = 83.318 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 35.651 us, total = 4.278 ms, Queueing time: mean = 37.779 us, max = 101.350 us, min = 11.127 us, total = 4.533 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 115.725 us, total = 115.725 us, Queueing time: mean = 174.267 us, max = 174.267 us, min = 174.267 us, total = 174.267 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,530 I 660935 661889] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 52.297 us, max = 872.955 us, min = 9.833 us, total = 135.500 ms
Execution time:  mean = 70.962 us, total = 183.863 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.271 us, total = 14.879 ms, Queueing time: mean = 58.203 us, max = 161.291 us, min = 14.561 us, total = 104.707 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 175.429 us, total = 31.577 ms, Queueing time: mean = 47.768 us, max = 92.271 us, min = 15.033 us, total = 8.598 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.361 us, total = 1.865 ms, Queueing time: mean = 49.035 us, max = 78.253 us, min = 13.089 us, total = 8.826 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.195 us, total = 5.255 ms, Queueing time: mean = 42.082 us, max = 96.837 us, min = 10.227 us, total = 7.575 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 696.135 us, total = 125.304 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.842 us, total = 1.290 ms, Queueing time: mean = 41.056 us, max = 77.265 us, min = 12.422 us, total = 1.478 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.953 us, total = 89.161 us, Queueing time: mean = 98.952 us, max = 872.955 us, min = 16.556 us, total = 1.781 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 81.280 us, total = 568.960 us, Queueing time: mean = 264.183 us, max = 574.691 us, min = 15.265 us, total = 1.849 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 566.338 us, total = 1.699 ms, Queueing time: mean = 34.080 us, max = 75.415 us, min = 26.826 us, total = 102.241 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 21.858 us, total = 21.858 us, Queueing time: mean = 9.833 us, max = 9.833 us, min = 9.833 us, total = 9.833 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 406.836 us, total = 406.836 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 373.097 us, total = 373.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 366.750 us, total = 366.750 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 116.306 us, total = 116.306 us, Queueing time: mean = 12.812 us, max = 12.812 us, min = 12.812 us, total = 12.812 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 50.410 us, total = 50.410 us, Queueing time: mean = 560.959 us, max = 560.959 us, min = 560.959 us, total = 560.959 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17885 total (1 active)
Queueing time: mean = 55.153 us, max = 2.638 ms, min = -0.000 s, total = 986.407 ms
Execution time:  mean = 15.296 us, total = 273.572 ms
Event stats:
	CoreWorker.CheckSignal - 17884 total (1 active), Execution time: mean = 15.297 us, total = 273.564 ms, Queueing time: mean = 55.156 us, max = 2.638 ms, min = -0.000 s, total = 986.405 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.795 us, total = 7.795 us, Queueing time: mean = 2.555 us, max = 2.555 us, min = 2.555 us, total = 2.555 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 29.167 us, max = 174.267 us, min = 11.127 us, total = 15.780 ms
Execution time:  mean = 311.031 us, total = 168.268 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 201.452 us, total = 36.261 ms, Queueing time: mean = 50.201 us, max = 108.070 us, min = 17.319 us, total = 9.036 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 697.088 us, total = 125.476 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 35.639 us, total = 6.415 ms, Queueing time: mean = 36.495 us, max = 101.350 us, min = 11.127 us, total = 6.569 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 115.725 us, total = 115.725 us, Queueing time: mean = 174.267 us, max = 174.267 us, min = 174.267 us, total = 174.267 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,531 I 660935 661889] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 53.275 us, max = 872.955 us, min = 9.833 us, total = 183.798 ms
Execution time:  mean = 70.753 us, total = 244.098 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.220 us, total = 19.720 ms, Queueing time: mean = 59.080 us, max = 161.291 us, min = 14.561 us, total = 141.732 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 176.005 us, total = 42.241 ms, Queueing time: mean = 51.409 us, max = 103.093 us, min = 15.033 us, total = 12.338 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.443 us, total = 2.506 ms, Queueing time: mean = 50.955 us, max = 78.253 us, min = 13.089 us, total = 12.229 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.727 us, total = 7.134 ms, Queueing time: mean = 42.773 us, max = 96.837 us, min = 10.227 us, total = 10.266 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 693.237 us, total = 166.377 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.406 us, total = 1.699 ms, Queueing time: mean = 43.320 us, max = 87.543 us, min = 12.422 us, total = 2.079 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 4.984 us, total = 119.608 us, Queueing time: mean = 106.026 us, max = 872.955 us, min = 16.556 us, total = 2.545 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 81.280 us, total = 568.960 us, Queueing time: mean = 264.183 us, max = 574.691 us, min = 15.265 us, total = 1.849 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 599.026 us, total = 2.396 ms, Queueing time: mean = 44.116 us, max = 75.415 us, min = 26.826 us, total = 176.463 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 21.858 us, total = 21.858 us, Queueing time: mean = 9.833 us, max = 9.833 us, min = 9.833 us, total = 9.833 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 406.836 us, total = 406.836 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 373.097 us, total = 373.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 366.750 us, total = 366.750 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 116.306 us, total = 116.306 us, Queueing time: mean = 12.812 us, max = 12.812 us, min = 12.812 us, total = 12.812 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 50.410 us, total = 50.410 us, Queueing time: mean = 560.959 us, max = 560.959 us, min = 560.959 us, total = 560.959 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23848 total (1 active)
Queueing time: mean = 53.966 us, max = 2.638 ms, min = -0.000 s, total = 1.287 s
Execution time:  mean = 15.320 us, total = 365.352 ms
Event stats:
	CoreWorker.CheckSignal - 23847 total (1 active), Execution time: mean = 15.320 us, total = 365.344 ms, Queueing time: mean = 53.968 us, max = 2.638 ms, min = -0.000 s, total = 1.287 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.795 us, total = 7.795 us, Queueing time: mean = 2.555 us, max = 2.555 us, min = 2.555 us, total = 2.555 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 29.968 us, max = 174.267 us, min = 11.127 us, total = 21.607 ms
Execution time:  mean = 314.766 us, total = 226.946 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 203.573 us, total = 48.858 ms, Queueing time: mean = 51.667 us, max = 108.070 us, min = 17.319 us, total = 12.400 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 705.178 us, total = 169.243 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.376 us, total = 8.730 ms, Queueing time: mean = 37.636 us, max = 101.350 us, min = 11.127 us, total = 9.033 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 115.725 us, total = 115.725 us, Queueing time: mean = 174.267 us, max = 174.267 us, min = 174.267 us, total = 174.267 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,531 I 660935 661889] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 53.231 us, max = 872.955 us, min = 9.833 us, total = 229.426 ms
Execution time:  mean = 71.630 us, total = 308.724 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.208 us, total = 24.617 ms, Queueing time: mean = 58.973 us, max = 161.291 us, min = 14.561 us, total = 176.859 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 176.109 us, total = 52.833 ms, Queueing time: mean = 51.929 us, max = 103.093 us, min = 15.033 us, total = 15.579 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.504 us, total = 3.151 ms, Queueing time: mean = 53.071 us, max = 79.619 us, min = 13.089 us, total = 15.921 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.567 us, total = 8.870 ms, Queueing time: mean = 40.888 us, max = 96.837 us, min = 10.227 us, total = 12.266 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 706.284 us, total = 211.885 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.866 us, total = 2.152 ms, Queueing time: mean = 48.837 us, max = 87.543 us, min = 12.422 us, total = 2.930 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.200 us, total = 155.991 us, Queueing time: mean = 105.412 us, max = 872.955 us, min = 16.556 us, total = 3.162 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 81.280 us, total = 568.960 us, Queueing time: mean = 264.183 us, max = 574.691 us, min = 15.265 us, total = 1.849 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 624.548 us, total = 3.123 ms, Queueing time: mean = 40.735 us, max = 75.415 us, min = 26.826 us, total = 203.675 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.468 us, total = 32.937 us, Queueing time: mean = 35.228 us, max = 70.457 us, min = 70.457 us, total = 70.457 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 21.858 us, total = 21.858 us, Queueing time: mean = 9.833 us, max = 9.833 us, min = 9.833 us, total = 9.833 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 406.836 us, total = 406.836 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 373.097 us, total = 373.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 366.750 us, total = 366.750 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 116.306 us, total = 116.306 us, Queueing time: mean = 12.812 us, max = 12.812 us, min = 12.812 us, total = 12.812 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 50.410 us, total = 50.410 us, Queueing time: mean = 560.959 us, max = 560.959 us, min = 560.959 us, total = 560.959 us

-----------------
Task execution event stats:

Global stats: 29812 total (1 active)
Queueing time: mean = 53.331 us, max = 2.638 ms, min = -0.000 s, total = 1.590 s
Execution time:  mean = 15.327 us, total = 456.942 ms
Event stats:
	CoreWorker.CheckSignal - 29811 total (1 active), Execution time: mean = 15.328 us, total = 456.934 ms, Queueing time: mean = 53.332 us, max = 2.638 ms, min = -0.000 s, total = 1.590 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.795 us, total = 7.795 us, Queueing time: mean = 2.555 us, max = 2.555 us, min = 2.555 us, total = 2.555 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 30.628 us, max = 174.267 us, min = 11.127 us, total = 27.596 ms
Execution time:  mean = 319.326 us, total = 287.713 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 206.353 us, total = 61.906 ms, Queueing time: mean = 52.091 us, max = 108.070 us, min = 17.319 us, total = 15.627 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 715.561 us, total = 214.668 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.743 us, total = 11.023 ms, Queueing time: mean = 39.314 us, max = 101.350 us, min = 11.127 us, total = 11.794 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 115.725 us, total = 115.725 us, Queueing time: mean = 174.267 us, max = 174.267 us, min = 174.267 us, total = 174.267 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,532 I 660935 661889] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 53.194 us, max = 872.955 us, min = 9.833 us, total = 274.906 ms
Execution time:  mean = 72.156 us, total = 372.901 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.184 us, total = 29.447 ms, Queueing time: mean = 58.765 us, max = 161.291 us, min = 14.561 us, total = 211.435 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 175.224 us, total = 63.081 ms, Queueing time: mean = 52.976 us, max = 110.760 us, min = 15.033 us, total = 19.071 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.514 us, total = 3.785 ms, Queueing time: mean = 54.785 us, max = 90.556 us, min = 13.089 us, total = 19.722 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.309 us, total = 10.551 ms, Queueing time: mean = 39.524 us, max = 96.837 us, min = 10.227 us, total = 14.229 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 715.079 us, total = 257.429 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 36.830 us, total = 2.652 ms, Queueing time: mean = 51.322 us, max = 87.543 us, min = 12.422 us, total = 3.695 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.233 us, total = 188.378 us, Queueing time: mean = 111.362 us, max = 872.955 us, min = 16.556 us, total = 4.009 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 81.280 us, total = 568.960 us, Queueing time: mean = 264.183 us, max = 574.691 us, min = 15.265 us, total = 1.849 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 638.527 us, total = 3.831 ms, Queueing time: mean = 40.277 us, max = 75.415 us, min = 26.826 us, total = 241.661 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.468 us, total = 32.937 us, Queueing time: mean = 35.228 us, max = 70.457 us, min = 70.457 us, total = 70.457 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 21.858 us, total = 21.858 us, Queueing time: mean = 9.833 us, max = 9.833 us, min = 9.833 us, total = 9.833 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 406.836 us, total = 406.836 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 373.097 us, total = 373.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 366.750 us, total = 366.750 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 116.306 us, total = 116.306 us, Queueing time: mean = 12.812 us, max = 12.812 us, min = 12.812 us, total = 12.812 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 50.410 us, total = 50.410 us, Queueing time: mean = 560.959 us, max = 560.959 us, min = 560.959 us, total = 560.959 us

-----------------
Task execution event stats:

Global stats: 35773 total (1 active)
Queueing time: mean = 53.778 us, max = 11.086 ms, min = -0.000 s, total = 1.924 s
Execution time:  mean = 15.284 us, total = 546.770 ms
Event stats:
	CoreWorker.CheckSignal - 35772 total (1 active), Execution time: mean = 15.285 us, total = 546.762 ms, Queueing time: mean = 53.779 us, max = 11.086 ms, min = -0.000 s, total = 1.924 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.795 us, total = 7.795 us, Queueing time: mean = 2.555 us, max = 2.555 us, min = 2.555 us, total = 2.555 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 30.979 us, max = 174.267 us, min = 11.127 us, total = 33.488 ms
Execution time:  mean = 322.551 us, total = 348.678 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 206.499 us, total = 74.340 ms, Queueing time: mean = 52.062 us, max = 108.070 us, min = 17.319 us, total = 18.742 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 724.905 us, total = 260.966 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.825 us, total = 13.257 ms, Queueing time: mean = 40.476 us, max = 101.350 us, min = 11.127 us, total = 14.571 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 115.725 us, total = 115.725 us, Queueing time: mean = 174.267 us, max = 174.267 us, min = 174.267 us, total = 174.267 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660935 661889] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660935 661889] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660935 661889] core_worker.cc:5107: Number of alive nodes:0
