[2025-07-05 18:42:59,472 I 660927 660927] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660927
[2025-07-05 18:42:59,474 I 660927 660927] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,478 I 660927 660927] grpc_server.cc:141: worker server started, listening on port 41221.
[2025-07-05 18:42:59,479 I 660927 660927] core_worker.cc:542: Initializing worker at address: ***********:41221 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,480 I 660927 660927] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,481 I 660927 660927] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,481 I 660927 661452] core_worker.cc:902: Event stats:


Global stats: 9 total (7 active)
Queueing time: mean = 7.323 us, max = 56.873 us, min = 9.032 us, total = 65.905 us
Execution time:  mean = 2.973 us, total = 26.758 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 4 total (2 active, 1 running), Execution time: mean = 6.689 us, total = 26.758 us, Queueing time: mean = 16.476 us, max = 56.873 us, min = 9.032 us, total = 65.905 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3 total (2 active)
Queueing time: mean = 91.217 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
Execution time:  mean = 40.656 us, total = 121.969 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 121.969 us, total = 121.969 us, Queueing time: mean = 273.650 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:1
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,481 I 660927 660927] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,481 I 660927 660927] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,481 I 660927 660927] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,487 I 660927 661452] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,487 I 660927 661452] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,916 I 660927 660927] actor_task_submitter.cc:73: Set actor max pending calls to -1 actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:42:59,916 I 660927 660927] core_worker.cc:3370: Creating actor actor_id=3e5877903265ed7cd75303e201000000
[2025-07-05 18:42:59,922 I 660927 660927] task_receiver.cc:175: Actor creation task finished, task_id: ffffffffffffffff3e5877903265ed7cd75303e201000000, actor_id: 3e5877903265ed7cd75303e201000000, actor_repr_name: 
[2025-07-05 18:43:09,486 W 660927 661442] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,482 I 660927 661452] core_worker.cc:902: Event stats:


Global stats: 877 total (8 active)
Queueing time: mean = 58.399 us, max = 2.807 ms, min = 7.184 us, total = 51.216 ms
Execution time:  mean = 96.783 us, total = 84.879 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.366 us, total = 5.020 ms, Queueing time: mean = 58.290 us, max = 110.392 us, min = 17.623 us, total = 34.974 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 11.897 us, total = 725.706 us, Queueing time: mean = 55.046 us, max = 77.087 us, min = 22.125 us, total = 3.358 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 185.788 us, total = 11.147 ms, Queueing time: mean = 56.350 us, max = 426.383 us, min = 23.211 us, total = 3.381 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 773.003 us, total = 46.380 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 28.470 us, total = 1.708 ms, Queueing time: mean = 40.717 us, max = 63.547 us, min = 7.184 us, total = 2.443 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 182.144 us, total = 2.186 ms, Queueing time: mean = 44.425 us, max = 80.766 us, min = 18.708 us, total = 533.103 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 67.660 us, total = 473.619 us, Queueing time: mean = 184.935 us, max = 440.273 us, min = 9.032 us, total = 1.295 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.574 us, total = 33.443 us, Queueing time: mean = 44.818 us, max = 76.608 us, min = 20.285 us, total = 268.909 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 101.110 us, total = 101.110 us, Queueing time: mean = 10.352 us, max = 10.352 us, min = 10.352 us, total = 10.352 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 4.723 ms, total = 4.723 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.307 ms, total = 1.307 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.140 us, total = 25.140 us, Queueing time: mean = 2.807 ms, max = 2.807 ms, min = 2.807 ms, total = 2.807 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.247 us, total = 112.247 us, Queueing time: mean = 54.964 us, max = 54.964 us, min = 54.964 us, total = 54.964 us
	CoreWorkerService.grpc_server.PushTask - 1 total (0 active), Execution time: mean = 7.199 ms, total = 7.199 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 85.909 us, total = 85.909 us, Queueing time: mean = 2.092 ms, max = 2.092 ms, min = 2.092 ms, total = 2.092 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 3.652 ms, total = 3.652 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5964 total (1 active)
Queueing time: mean = 54.017 us, max = 6.199 ms, min = -0.000 s, total = 322.159 ms
Execution time:  mean = 15.885 us, total = 94.736 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 14.737 us, total = 87.864 ms, Queueing time: mean = 54.032 us, max = 6.199 ms, min = -0.000 s, total = 322.136 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.055 us, total = 8.055 us, Queueing time: mean = 2.776 us, max = 2.776 us, min = 2.776 us, total = 2.776 us
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 6.864 ms, total = 6.864 ms, Queueing time: mean = 19.752 us, max = 19.752 us, min = 19.752 us, total = 19.752 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 34.892 us, max = 273.650 us, min = 8.590 us, total = 6.315 ms
Execution time:  mean = 354.472 us, total = 64.159 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 36.356 us, total = 2.181 ms, Queueing time: mean = 45.059 us, max = 98.864 us, min = 8.590 us, total = 2.704 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 818.289 us, total = 49.097 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 212.646 us, total = 12.759 ms, Queueing time: mean = 55.638 us, max = 106.670 us, min = 11.170 us, total = 3.338 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 121.969 us, total = 121.969 us, Queueing time: mean = 273.650 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000370979 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,482 I 660927 661452] core_worker.cc:902: Event stats:


Global stats: 1735 total (8 active)
Queueing time: mean = 54.521 us, max = 2.807 ms, min = 7.184 us, total = 94.595 ms
Execution time:  mean = 88.077 us, total = 152.814 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.414 us, total = 10.097 ms, Queueing time: mean = 56.775 us, max = 363.061 us, min = 16.755 us, total = 68.130 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 192.875 us, total = 23.145 ms, Queueing time: mean = 53.577 us, max = 426.383 us, min = 22.081 us, total = 6.429 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 772.023 us, total = 92.643 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 11.524 us, total = 1.383 ms, Queueing time: mean = 56.449 us, max = 89.345 us, min = 22.125 us, total = 6.774 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.332 us, total = 3.520 ms, Queueing time: mean = 42.298 us, max = 103.908 us, min = 7.184 us, total = 5.076 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 146.634 us, total = 3.519 ms, Queueing time: mean = 45.869 us, max = 80.766 us, min = 18.708 us, total = 1.101 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.803 us, total = 69.641 us, Queueing time: mean = 66.634 us, max = 231.400 us, min = 20.285 us, total = 799.603 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 67.660 us, total = 473.619 us, Queueing time: mean = 184.935 us, max = 440.273 us, min = 9.032 us, total = 1.295 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 379.884 us, total = 759.768 us, Queueing time: mean = 13.472 us, max = 26.944 us, min = 26.944 us, total = 26.944 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 101.110 us, total = 101.110 us, Queueing time: mean = 10.352 us, max = 10.352 us, min = 10.352 us, total = 10.352 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 4.723 ms, total = 4.723 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.307 ms, total = 1.307 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.140 us, total = 25.140 us, Queueing time: mean = 2.807 ms, max = 2.807 ms, min = 2.807 ms, total = 2.807 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.247 us, total = 112.247 us, Queueing time: mean = 54.964 us, max = 54.964 us, min = 54.964 us, total = 54.964 us
	CoreWorkerService.grpc_server.PushTask - 1 total (0 active), Execution time: mean = 7.199 ms, total = 7.199 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 85.909 us, total = 85.909 us, Queueing time: mean = 2.092 ms, max = 2.092 ms, min = 2.092 ms, total = 2.092 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 3.652 ms, total = 3.652 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11925 total (1 active)
Queueing time: mean = 54.790 us, max = 6.199 ms, min = -0.000 s, total = 653.376 ms
Execution time:  mean = 15.497 us, total = 184.804 ms
Event stats:
	CoreWorker.CheckSignal - 11923 total (1 active), Execution time: mean = 14.923 us, total = 177.932 ms, Queueing time: mean = 54.798 us, max = 6.199 ms, min = -0.000 s, total = 653.353 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.055 us, total = 8.055 us, Queueing time: mean = 2.776 us, max = 2.776 us, min = 2.776 us, total = 2.776 us
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 6.864 ms, total = 6.864 ms, Queueing time: mean = 19.752 us, max = 19.752 us, min = 19.752 us, total = 19.752 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 33.517 us, max = 273.650 us, min = 8.590 us, total = 12.100 ms
Execution time:  mean = 348.963 us, total = 125.975 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 36.094 us, total = 4.331 ms, Queueing time: mean = 42.978 us, max = 98.864 us, min = 8.590 us, total = 5.157 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 797.206 us, total = 95.665 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 215.479 us, total = 25.858 ms, Queueing time: mean = 55.572 us, max = 107.308 us, min = 11.170 us, total = 6.669 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 121.969 us, total = 121.969 us, Queueing time: mean = 273.650 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000370979 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,483 I 660927 661452] core_worker.cc:902: Event stats:


Global stats: 2593 total (8 active)
Queueing time: mean = 54.567 us, max = 2.807 ms, min = 7.184 us, total = 141.492 ms
Execution time:  mean = 86.621 us, total = 224.608 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.367 us, total = 15.053 ms, Queueing time: mean = 57.277 us, max = 363.061 us, min = 16.363 us, total = 103.042 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 191.487 us, total = 34.468 ms, Queueing time: mean = 55.529 us, max = 426.383 us, min = 22.081 us, total = 9.995 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 797.523 us, total = 143.554 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 11.423 us, total = 2.056 ms, Queueing time: mean = 59.414 us, max = 345.688 us, min = 15.861 us, total = 10.695 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.745 us, total = 5.354 ms, Queueing time: mean = 44.156 us, max = 103.908 us, min = 7.184 us, total = 7.948 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 131.345 us, total = 4.728 ms, Queueing time: mean = 50.949 us, max = 98.508 us, min = 18.708 us, total = 1.834 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.629 us, total = 101.318 us, Queueing time: mean = 89.685 us, max = 605.531 us, min = 20.285 us, total = 1.614 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 67.660 us, total = 473.619 us, Queueing time: mean = 184.935 us, max = 440.273 us, min = 9.032 us, total = 1.295 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 538.333 us, total = 1.615 ms, Queueing time: mean = 35.246 us, max = 78.793 us, min = 26.944 us, total = 105.737 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 101.110 us, total = 101.110 us, Queueing time: mean = 10.352 us, max = 10.352 us, min = 10.352 us, total = 10.352 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 4.723 ms, total = 4.723 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.307 ms, total = 1.307 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.140 us, total = 25.140 us, Queueing time: mean = 2.807 ms, max = 2.807 ms, min = 2.807 ms, total = 2.807 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.247 us, total = 112.247 us, Queueing time: mean = 54.964 us, max = 54.964 us, min = 54.964 us, total = 54.964 us
	CoreWorkerService.grpc_server.PushTask - 1 total (0 active), Execution time: mean = 7.199 ms, total = 7.199 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 85.909 us, total = 85.909 us, Queueing time: mean = 2.092 ms, max = 2.092 ms, min = 2.092 ms, total = 2.092 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 3.652 ms, total = 3.652 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17886 total (1 active)
Queueing time: mean = 55.120 us, max = 6.199 ms, min = -0.000 s, total = 985.883 ms
Execution time:  mean = 15.380 us, total = 275.080 ms
Event stats:
	CoreWorker.CheckSignal - 17884 total (1 active), Execution time: mean = 14.997 us, total = 268.208 ms, Queueing time: mean = 55.125 us, max = 6.199 ms, min = -0.000 s, total = 985.861 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.055 us, total = 8.055 us, Queueing time: mean = 2.776 us, max = 2.776 us, min = 2.776 us, total = 2.776 us
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 6.864 ms, total = 6.864 ms, Queueing time: mean = 19.752 us, max = 19.752 us, min = 19.752 us, total = 19.752 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 33.356 us, max = 273.650 us, min = 8.590 us, total = 18.046 ms
Execution time:  mean = 343.507 us, total = 185.837 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 36.231 us, total = 6.522 ms, Queueing time: mean = 42.290 us, max = 98.864 us, min = 8.590 us, total = 7.612 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 781.078 us, total = 140.594 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 214.442 us, total = 38.599 ms, Queueing time: mean = 56.443 us, max = 107.308 us, min = 11.170 us, total = 10.160 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 121.969 us, total = 121.969 us, Queueing time: mean = 273.650 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000370979 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,484 I 660927 661452] core_worker.cc:902: Event stats:


Global stats: 3452 total (8 active)
Queueing time: mean = 54.983 us, max = 2.807 ms, min = 7.184 us, total = 189.800 ms
Execution time:  mean = 86.230 us, total = 297.667 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.319 us, total = 19.957 ms, Queueing time: mean = 58.369 us, max = 363.061 us, min = 16.363 us, total = 140.027 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 193.826 us, total = 46.518 ms, Queueing time: mean = 56.232 us, max = 426.383 us, min = 15.981 us, total = 13.496 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 813.251 us, total = 195.180 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 11.315 us, total = 2.716 ms, Queueing time: mean = 59.326 us, max = 345.688 us, min = 15.861 us, total = 14.238 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 30.299 us, total = 7.272 ms, Queueing time: mean = 44.389 us, max = 103.908 us, min = 7.184 us, total = 10.653 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 121.098 us, total = 5.813 ms, Queueing time: mean = 50.691 us, max = 98.508 us, min = 16.084 us, total = 2.433 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.482 us, total = 131.572 us, Queueing time: mean = 106.291 us, max = 605.531 us, min = 20.285 us, total = 2.551 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 67.660 us, total = 473.619 us, Queueing time: mean = 184.935 us, max = 440.273 us, min = 9.032 us, total = 1.295 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 600.424 us, total = 2.402 ms, Queueing time: mean = 35.615 us, max = 78.793 us, min = 26.944 us, total = 142.460 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 101.110 us, total = 101.110 us, Queueing time: mean = 10.352 us, max = 10.352 us, min = 10.352 us, total = 10.352 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 4.723 ms, total = 4.723 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.307 ms, total = 1.307 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.140 us, total = 25.140 us, Queueing time: mean = 2.807 ms, max = 2.807 ms, min = 2.807 ms, total = 2.807 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.247 us, total = 112.247 us, Queueing time: mean = 54.964 us, max = 54.964 us, min = 54.964 us, total = 54.964 us
	CoreWorkerService.grpc_server.PushTask - 1 total (0 active), Execution time: mean = 7.199 ms, total = 7.199 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 85.909 us, total = 85.909 us, Queueing time: mean = 2.092 ms, max = 2.092 ms, min = 2.092 ms, total = 2.092 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 3.652 ms, total = 3.652 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23846 total (1 active)
Queueing time: mean = 55.411 us, max = 6.199 ms, min = -0.000 s, total = 1.321 s
Execution time:  mean = 15.326 us, total = 365.467 ms
Event stats:
	CoreWorker.CheckSignal - 23844 total (1 active), Execution time: mean = 15.039 us, total = 358.595 ms, Queueing time: mean = 55.415 us, max = 6.199 ms, min = -0.000 s, total = 1.321 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.055 us, total = 8.055 us, Queueing time: mean = 2.776 us, max = 2.776 us, min = 2.776 us, total = 2.776 us
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 6.864 ms, total = 6.864 ms, Queueing time: mean = 19.752 us, max = 19.752 us, min = 19.752 us, total = 19.752 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 32.848 us, max = 273.650 us, min = 8.590 us, total = 23.684 ms
Execution time:  mean = 346.494 us, total = 249.822 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.918 us, total = 8.860 ms, Queueing time: mean = 44.007 us, max = 98.864 us, min = 8.590 us, total = 10.562 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 788.869 us, total = 189.329 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 214.631 us, total = 51.511 ms, Queueing time: mean = 53.535 us, max = 107.308 us, min = 11.170 us, total = 12.848 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 121.969 us, total = 121.969 us, Queueing time: mean = 273.650 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000370979 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,485 I 660927 661452] core_worker.cc:902: Event stats:


Global stats: 4312 total (8 active)
Queueing time: mean = 54.075 us, max = 2.807 ms, min = 7.184 us, total = 233.172 ms
Execution time:  mean = 85.528 us, total = 368.796 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.339 us, total = 25.009 ms, Queueing time: mean = 57.719 us, max = 363.061 us, min = 15.964 us, total = 173.099 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 195.545 us, total = 58.664 ms, Queueing time: mean = 56.184 us, max = 426.383 us, min = 15.981 us, total = 16.855 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 815.295 us, total = 244.588 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.354 us, total = 3.406 ms, Queueing time: mean = 58.220 us, max = 345.688 us, min = 15.861 us, total = 17.466 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.389 us, total = 9.117 ms, Queueing time: mean = 42.687 us, max = 103.908 us, min = 7.184 us, total = 12.806 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 116.246 us, total = 6.975 ms, Queueing time: mean = 50.012 us, max = 98.508 us, min = 16.084 us, total = 3.001 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.465 us, total = 163.958 us, Queueing time: mean = 113.244 us, max = 605.531 us, min = 20.285 us, total = 3.397 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 67.660 us, total = 473.619 us, Queueing time: mean = 184.935 us, max = 440.273 us, min = 9.032 us, total = 1.295 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 635.527 us, total = 3.178 ms, Queueing time: mean = 44.426 us, max = 79.670 us, min = 26.944 us, total = 222.130 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.414 us, total = 16.827 us, Queueing time: mean = 33.486 us, max = 66.971 us, min = 66.971 us, total = 66.971 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 101.110 us, total = 101.110 us, Queueing time: mean = 10.352 us, max = 10.352 us, min = 10.352 us, total = 10.352 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 4.723 ms, total = 4.723 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.307 ms, total = 1.307 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.140 us, total = 25.140 us, Queueing time: mean = 2.807 ms, max = 2.807 ms, min = 2.807 ms, total = 2.807 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.247 us, total = 112.247 us, Queueing time: mean = 54.964 us, max = 54.964 us, min = 54.964 us, total = 54.964 us
	CoreWorkerService.grpc_server.PushTask - 1 total (0 active), Execution time: mean = 7.199 ms, total = 7.199 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 85.909 us, total = 85.909 us, Queueing time: mean = 2.092 ms, max = 2.092 ms, min = 2.092 ms, total = 2.092 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 3.652 ms, total = 3.652 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29811 total (1 active)
Queueing time: mean = 54.319 us, max = 6.199 ms, min = -0.000 s, total = 1.619 s
Execution time:  mean = 15.299 us, total = 456.066 ms
Event stats:
	CoreWorker.CheckSignal - 29809 total (1 active), Execution time: mean = 15.069 us, total = 449.194 ms, Queueing time: mean = 54.322 us, max = 6.199 ms, min = -0.000 s, total = 1.619 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.055 us, total = 8.055 us, Queueing time: mean = 2.776 us, max = 2.776 us, min = 2.776 us, total = 2.776 us
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 6.864 ms, total = 6.864 ms, Queueing time: mean = 19.752 us, max = 19.752 us, min = 19.752 us, total = 19.752 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 32.927 us, max = 273.650 us, min = 8.590 us, total = 29.667 ms
Execution time:  mean = 346.838 us, total = 312.501 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.836 us, total = 11.051 ms, Queueing time: mean = 43.683 us, max = 221.928 us, min = 8.590 us, total = 13.105 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 791.695 us, total = 237.509 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 212.734 us, total = 63.820 ms, Queueing time: mean = 54.294 us, max = 107.308 us, min = 11.170 us, total = 16.288 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 121.969 us, total = 121.969 us, Queueing time: mean = 273.650 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000370979 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,486 I 660927 661452] core_worker.cc:902: Event stats:


Global stats: 5170 total (8 active)
Queueing time: mean = 53.863 us, max = 2.807 ms, min = 7.184 us, total = 278.471 ms
Execution time:  mean = 84.232 us, total = 435.477 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.371 us, total = 30.118 ms, Queueing time: mean = 57.744 us, max = 363.061 us, min = 15.819 us, total = 207.764 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 194.734 us, total = 70.104 ms, Queueing time: mean = 55.539 us, max = 426.383 us, min = 15.237 us, total = 19.994 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 806.655 us, total = 290.396 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.414 us, total = 4.109 ms, Queueing time: mean = 58.023 us, max = 345.688 us, min = 15.325 us, total = 20.888 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.249 us, total = 10.890 ms, Queueing time: mean = 42.490 us, max = 103.908 us, min = 7.184 us, total = 15.296 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 111.641 us, total = 8.038 ms, Queueing time: mean = 51.729 us, max = 98.508 us, min = 16.084 us, total = 3.724 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.436 us, total = 195.705 us, Queueing time: mean = 116.433 us, max = 605.531 us, min = 20.285 us, total = 4.192 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 67.660 us, total = 473.619 us, Queueing time: mean = 184.935 us, max = 440.273 us, min = 9.032 us, total = 1.295 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 655.197 us, total = 3.931 ms, Queueing time: mean = 47.964 us, max = 79.670 us, min = 26.944 us, total = 287.784 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.414 us, total = 16.827 us, Queueing time: mean = 33.486 us, max = 66.971 us, min = 66.971 us, total = 66.971 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 101.110 us, total = 101.110 us, Queueing time: mean = 10.352 us, max = 10.352 us, min = 10.352 us, total = 10.352 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 4.723 ms, total = 4.723 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.307 ms, total = 1.307 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 25.140 us, total = 25.140 us, Queueing time: mean = 2.807 ms, max = 2.807 ms, min = 2.807 ms, total = 2.807 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 1 total (0 active), Execution time: mean = 112.247 us, total = 112.247 us, Queueing time: mean = 54.964 us, max = 54.964 us, min = 54.964 us, total = 54.964 us
	CoreWorkerService.grpc_server.PushTask - 1 total (0 active), Execution time: mean = 7.199 ms, total = 7.199 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 85.909 us, total = 85.909 us, Queueing time: mean = 2.092 ms, max = 2.092 ms, min = 2.092 ms, total = 2.092 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 3.652 ms, total = 3.652 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35772 total (1 active)
Queueing time: mean = 54.433 us, max = 6.199 ms, min = -0.000 s, total = 1.947 s
Execution time:  mean = 15.252 us, total = 545.583 ms
Event stats:
	CoreWorker.CheckSignal - 35770 total (1 active), Execution time: mean = 15.060 us, total = 538.711 ms, Queueing time: mean = 54.435 us, max = 6.199 ms, min = -0.000 s, total = 1.947 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.055 us, total = 8.055 us, Queueing time: mean = 2.776 us, max = 2.776 us, min = 2.776 us, total = 2.776 us
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 6.864 ms, total = 6.864 ms, Queueing time: mean = 19.752 us, max = 19.752 us, min = 19.752 us, total = 19.752 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 32.534 us, max = 273.650 us, min = 8.590 us, total = 35.169 ms
Execution time:  mean = 351.142 us, total = 379.584 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 37.212 us, total = 13.396 ms, Queueing time: mean = 42.818 us, max = 221.928 us, min = 8.590 us, total = 15.414 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 802.590 us, total = 288.932 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 214.260 us, total = 77.134 ms, Queueing time: mean = 54.114 us, max = 107.308 us, min = 11.170 us, total = 19.481 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 121.969 us, total = 121.969 us, Queueing time: mean = 273.650 us, max = 273.650 us, min = 273.650 us, total = 273.650 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000370979 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:07,826 I 660927 660927] out_of_order_actor_scheduling_queue.cc:48: Setting actor as asyncio with max_concurrency=1000, and defined concurrency groups are:

[2025-07-05 18:49:08,664 I 660927 661452] core_worker.cc:4542: Force kill actor request has received. exiting immediately... The actor is dead because its owner has died. Owner Id: 01000000ffffffffffffffffffffffffffffffffffffffffffffffff Owner Ip address: *********** Owner worker exit type: INTENDED_USER_EXIT Worker exit detail: Owner's worker process has crashed.
[2025-07-05 18:49:08,664 W 660927 661452] core_worker.cc:1253: Force exit the process.  Details: Worker exits because the actor is killed. The actor is dead because its owner has died. Owner Id: 01000000ffffffffffffffffffffffffffffffffffffffffffffffff Owner Ip address: *********** Owner worker exit type: INTENDED_USER_EXIT Worker exit detail: Owner's worker process has crashed.
[2025-07-05 18:49:08,669 I 660927 660927] core_worker.cc:1163: Exit signal received, this process will exit after all outstanding tasks have finished, exit_type=SYSTEM_ERROR, detail=Worker exits unexpectedly by a signal. SystemExit is raised (sys.exit is called). Exit code: 1. The process receives a SIGTERM.
[2025-07-05 18:49:08,669 I 660927 660927] core_worker.cc:1206: Wait for currently executing tasks in the underlying thread pools to finish.
[2025-07-05 18:49:08,669 I 660927 660927] concurrency_group_manager.cc:99: Default executor is joining. If the 'Default executor is joined.' message is not printed after this, the worker is probably hanging because the actor task is running an infinite loop.
[2025-07-05 18:49:08,669 I 660927 660927] concurrency_group_manager.cc:103: Default executor is joined.
[2025-07-05 18:49:08,669 I 660927 660927] core_worker.cc:1240: Not draining reference counter since this is an actor worker.
[2025-07-05 18:49:08,680 I 660927 661452] core_worker.cc:1134: Try killing all child processes of this worker as it exits. Child process pids: 
[2025-07-05 18:49:08,681 I 660927 661452] core_worker.cc:1088: Sending disconnect message to the local raylet.
[2025-07-05 18:49:08,681 I 660927 661452] raylet_client.cc:73: RayletClient::Disconnect, exit_type=INTENDED_SYSTEM_EXIT, exit_detail=Worker exits because the actor is killed. The actor is dead because its owner has died. Owner Id: 01000000ffffffffffffffffffffffffffffffffffffffffffffffff Owner Ip address: *********** Owner worker exit type: INTENDED_USER_EXIT Worker exit detail: Owner's worker process has crashed., has creation_task_exception_pb_bytes=0
[2025-07-05 18:49:08,682 I 660927 661452] core_worker.cc:1094: Disconnected from the local raylet.
