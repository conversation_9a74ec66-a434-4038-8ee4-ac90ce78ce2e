[2025-07-05 18:42:59,705 I 660949 660949] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660949
[2025-07-05 18:42:59,706 I 660949 660949] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,708 I 660949 660949] grpc_server.cc:141: worker server started, listening on port 43381.
[2025-07-05 18:42:59,709 I 660949 660949] core_worker.cc:542: Initializing worker at address: ***********:43381 worker_id=305dcfb2168af824970e2f9f89fbccfe0595030843afaaea0fd46984 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,710 I 660949 660949] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-07-05 18:42:59,711 I 660949 660949] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,711 I 660949 660949] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,711 I 660949 662200] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 7.041 us, max = 59.178 us, min = 6.148 us, total = 105.615 us
Execution time:  mean = 56.319 us, total = 844.788 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 2.859 us, total = 20.013 us, Queueing time: mean = 9.332 us, max = 59.178 us, min = 6.148 us, total = 65.326 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.563 us, total = 22.563 us, Queueing time: mean = 33.085 us, max = 33.085 us, min = 33.085 us, total = 33.085 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 368.033 us, total = 368.033 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 328.082 us, total = 328.082 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.097 us, total = 106.097 us, Queueing time: mean = 7.204 us, max = 7.204 us, min = 7.204 us, total = 7.204 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.876 us, max = 8.623 us, min = 6.882 us, total = 15.505 us
Execution time:  mean = 131.308 us, total = 525.233 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 100.041 us, total = 100.041 us, Queueing time: mean = 6.882 us, max = 6.882 us, min = 6.882 us, total = 6.882 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 407.899 us, total = 407.899 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.293 us, total = 17.293 us, Queueing time: mean = 8.623 us, max = 8.623 us, min = 8.623 us, total = 8.623 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,711 I 660949 660949] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,711 I 660949 660949] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,711 I 660949 662200] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,711 I 660949 662200] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,717 W 660949 662178] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,711 I 660949 662200] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 48.057 us, max = 771.594 us, min = 6.148 us, total = 42.050 ms
Execution time:  mean = 73.201 us, total = 64.051 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.380 us, total = 5.028 ms, Queueing time: mean = 52.217 us, max = 771.594 us, min = 13.676 us, total = 31.330 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.467 us, total = 638.465 us, Queueing time: mean = 51.525 us, max = 131.396 us, min = 13.681 us, total = 3.143 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 26.345 us, total = 1.581 ms, Queueing time: mean = 34.089 us, max = 69.237 us, min = 12.960 us, total = 2.045 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 740.292 us, total = 44.418 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 172.066 us, total = 10.324 ms, Queueing time: mean = 50.770 us, max = 77.469 us, min = 11.270 us, total = 3.046 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 29.329 us, total = 351.952 us, Queueing time: mean = 34.289 us, max = 69.996 us, min = 15.785 us, total = 411.472 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.396 us, total = 506.770 us, Queueing time: mean = 204.138 us, max = 480.752 us, min = 6.148 us, total = 1.429 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.871 us, total = 29.225 us, Queueing time: mean = 45.875 us, max = 70.365 us, min = 28.070 us, total = 275.252 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.563 us, total = 22.563 us, Queueing time: mean = 33.085 us, max = 33.085 us, min = 33.085 us, total = 33.085 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.097 us, total = 106.097 us, Queueing time: mean = 7.204 us, max = 7.204 us, min = 7.204 us, total = 7.204 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 70.283 us, total = 70.283 us, Queueing time: mean = 329.597 us, max = 329.597 us, min = 329.597 us, total = 329.597 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 279.280 us, total = 279.280 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 328.082 us, total = 328.082 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 368.033 us, total = 368.033 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5961 total (1 active)
Queueing time: mean = 57.098 us, max = 2.351 ms, min = 2.606 us, total = 340.364 ms
Execution time:  mean = 14.903 us, total = 88.838 ms
Event stats:
	CoreWorker.CheckSignal - 5960 total (1 active), Execution time: mean = 14.904 us, total = 88.830 ms, Queueing time: mean = 57.108 us, max = 2.351 ms, min = 8.002 us, total = 340.361 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.854 us, total = 7.854 us, Queueing time: mean = 2.606 us, max = 2.606 us, min = 2.606 us, total = 2.606 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 36.892 us, max = 104.915 us, min = 6.882 us, total = 6.677 ms
Execution time:  mean = 334.800 us, total = 60.599 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 766.900 us, total = 46.014 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 205.923 us, total = 12.355 ms, Queueing time: mean = 64.199 us, max = 104.915 us, min = 27.583 us, total = 3.852 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 35.488 us, total = 2.129 ms, Queueing time: mean = 46.976 us, max = 63.247 us, min = 8.623 us, total = 2.819 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 100.041 us, total = 100.041 us, Queueing time: mean = 6.882 us, max = 6.882 us, min = 6.882 us, total = 6.882 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,712 I 660949 662200] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 45.753 us, max = 771.594 us, min = 6.148 us, total = 79.289 ms
Execution time:  mean = 75.550 us, total = 130.928 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.498 us, total = 10.197 ms, Queueing time: mean = 49.014 us, max = 771.594 us, min = 13.676 us, total = 58.817 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.598 us, total = 3.432 ms, Queueing time: mean = 38.967 us, max = 80.514 us, min = 12.960 us, total = 4.676 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 11.315 us, total = 1.358 ms, Queueing time: mean = 50.662 us, max = 131.396 us, min = 11.935 us, total = 6.079 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 757.773 us, total = 90.933 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 180.911 us, total = 21.709 ms, Queueing time: mean = 52.425 us, max = 88.305 us, min = 11.270 us, total = 6.291 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 33.863 us, total = 812.712 us, Queueing time: mean = 35.972 us, max = 81.350 us, min = 13.506 us, total = 863.332 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.986 us, total = 59.832 us, Queueing time: mean = 62.060 us, max = 200.905 us, min = 17.525 us, total = 744.721 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.396 us, total = 506.770 us, Queueing time: mean = 204.138 us, max = 480.752 us, min = 6.148 us, total = 1.429 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 372.692 us, total = 745.385 us, Queueing time: mean = 9.592 us, max = 19.185 us, min = 19.185 us, total = 19.185 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.563 us, total = 22.563 us, Queueing time: mean = 33.085 us, max = 33.085 us, min = 33.085 us, total = 33.085 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.097 us, total = 106.097 us, Queueing time: mean = 7.204 us, max = 7.204 us, min = 7.204 us, total = 7.204 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 70.283 us, total = 70.283 us, Queueing time: mean = 329.597 us, max = 329.597 us, min = 329.597 us, total = 329.597 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 279.280 us, total = 279.280 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 328.082 us, total = 328.082 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 368.033 us, total = 368.033 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11926 total (1 active)
Queueing time: mean = 53.113 us, max = 2.351 ms, min = -0.001 s, total = 633.426 ms
Execution time:  mean = 15.243 us, total = 181.784 ms
Event stats:
	CoreWorker.CheckSignal - 11925 total (1 active), Execution time: mean = 15.243 us, total = 181.777 ms, Queueing time: mean = 53.117 us, max = 2.351 ms, min = -0.001 s, total = 633.423 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.854 us, total = 7.854 us, Queueing time: mean = 2.606 us, max = 2.606 us, min = 2.606 us, total = 2.606 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 36.361 us, max = 109.227 us, min = 6.882 us, total = 13.126 ms
Execution time:  mean = 338.002 us, total = 122.019 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 772.620 us, total = 92.714 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 207.648 us, total = 24.918 ms, Queueing time: mean = 66.588 us, max = 109.227 us, min = 27.583 us, total = 7.991 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 35.720 us, total = 4.286 ms, Queueing time: mean = 42.739 us, max = 64.327 us, min = 8.623 us, total = 5.129 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 100.041 us, total = 100.041 us, Queueing time: mean = 6.882 us, max = 6.882 us, min = 6.882 us, total = 6.882 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,713 I 660949 662200] core_worker.cc:902: Event stats:


Global stats: 2592 total (8 active)
Queueing time: mean = 46.423 us, max = 771.594 us, min = -0.000 s, total = 120.328 ms
Execution time:  mean = 76.005 us, total = 197.004 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.521 us, total = 15.338 ms, Queueing time: mean = 50.117 us, max = 771.594 us, min = -0.000 s, total = 90.210 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.162 us, total = 5.249 ms, Queueing time: mean = 40.631 us, max = 80.514 us, min = 12.960 us, total = 7.314 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 11.333 us, total = 2.040 ms, Queueing time: mean = 49.425 us, max = 131.396 us, min = 11.935 us, total = 8.897 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 763.330 us, total = 137.399 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 180.613 us, total = 32.510 ms, Queueing time: mean = 50.826 us, max = 88.305 us, min = 11.270 us, total = 9.149 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 34.025 us, total = 1.225 ms, Queueing time: mean = 37.925 us, max = 81.350 us, min = 13.506 us, total = 1.365 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.122 us, total = 92.190 us, Queueing time: mean = 85.548 us, max = 465.513 us, min = 17.525 us, total = 1.540 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.396 us, total = 506.770 us, Queueing time: mean = 204.138 us, max = 480.752 us, min = 6.148 us, total = 1.429 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 489.662 us, total = 1.469 ms, Queueing time: mean = 18.217 us, max = 35.467 us, min = 19.185 us, total = 54.652 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.563 us, total = 22.563 us, Queueing time: mean = 33.085 us, max = 33.085 us, min = 33.085 us, total = 33.085 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.097 us, total = 106.097 us, Queueing time: mean = 7.204 us, max = 7.204 us, min = 7.204 us, total = 7.204 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 70.283 us, total = 70.283 us, Queueing time: mean = 329.597 us, max = 329.597 us, min = 329.597 us, total = 329.597 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 279.280 us, total = 279.280 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 328.082 us, total = 328.082 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 368.033 us, total = 368.033 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17884 total (1 active)
Queueing time: mean = 55.521 us, max = 2.351 ms, min = -0.001 s, total = 992.943 ms
Execution time:  mean = 15.215 us, total = 272.102 ms
Event stats:
	CoreWorker.CheckSignal - 17883 total (1 active), Execution time: mean = 15.215 us, total = 272.094 ms, Queueing time: mean = 55.524 us, max = 2.351 ms, min = -0.001 s, total = 992.940 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.854 us, total = 7.854 us, Queueing time: mean = 2.606 us, max = 2.606 us, min = 2.606 us, total = 2.606 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 34.878 us, max = 109.227 us, min = 6.882 us, total = 18.869 ms
Execution time:  mean = 341.548 us, total = 184.778 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 780.673 us, total = 140.521 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 208.343 us, total = 37.502 ms, Queueing time: mean = 61.830 us, max = 109.227 us, min = 26.540 us, total = 11.129 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 36.970 us, total = 6.655 ms, Queueing time: mean = 42.958 us, max = 64.327 us, min = 8.623 us, total = 7.733 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 100.041 us, total = 100.041 us, Queueing time: mean = 6.882 us, max = 6.882 us, min = 6.882 us, total = 6.882 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,714 I 660949 662200] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 47.211 us, max = 771.594 us, min = -0.000 s, total = 162.879 ms
Execution time:  mean = 76.419 us, total = 263.646 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.538 us, total = 20.483 ms, Queueing time: mean = 51.135 us, max = 771.594 us, min = -0.000 s, total = 122.672 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.562 us, total = 7.095 ms, Queueing time: mean = 42.096 us, max = 94.234 us, min = 12.960 us, total = 10.103 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 11.399 us, total = 2.736 ms, Queueing time: mean = 48.724 us, max = 131.396 us, min = 11.935 us, total = 11.694 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 767.771 us, total = 184.265 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 181.189 us, total = 43.485 ms, Queueing time: mean = 51.539 us, max = 88.305 us, min = 11.270 us, total = 12.369 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 34.418 us, total = 1.652 ms, Queueing time: mean = 39.823 us, max = 81.350 us, min = 13.506 us, total = 1.911 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.127 us, total = 123.048 us, Queueing time: mean = 91.904 us, max = 465.513 us, min = 17.525 us, total = 2.206 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.396 us, total = 506.770 us, Queueing time: mean = 204.138 us, max = 480.752 us, min = 6.148 us, total = 1.429 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 531.640 us, total = 2.127 ms, Queueing time: mean = 31.158 us, max = 69.980 us, min = 19.185 us, total = 124.632 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.563 us, total = 22.563 us, Queueing time: mean = 33.085 us, max = 33.085 us, min = 33.085 us, total = 33.085 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.097 us, total = 106.097 us, Queueing time: mean = 7.204 us, max = 7.204 us, min = 7.204 us, total = 7.204 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 70.283 us, total = 70.283 us, Queueing time: mean = 329.597 us, max = 329.597 us, min = 329.597 us, total = 329.597 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 279.280 us, total = 279.280 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 328.082 us, total = 328.082 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 368.033 us, total = 368.033 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23842 total (1 active)
Queueing time: mean = 56.709 us, max = 2.351 ms, min = -0.001 s, total = 1.352 s
Execution time:  mean = 15.218 us, total = 362.825 ms
Event stats:
	CoreWorker.CheckSignal - 23841 total (1 active), Execution time: mean = 15.218 us, total = 362.817 ms, Queueing time: mean = 56.711 us, max = 2.351 ms, min = -0.001 s, total = 1.352 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.854 us, total = 7.854 us, Queueing time: mean = 2.606 us, max = 2.606 us, min = 2.606 us, total = 2.606 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 34.872 us, max = 109.227 us, min = 6.882 us, total = 25.143 ms
Execution time:  mean = 343.814 us, total = 247.890 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 787.343 us, total = 188.962 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 207.797 us, total = 49.871 ms, Queueing time: mean = 60.189 us, max = 109.227 us, min = 26.540 us, total = 14.445 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 37.317 us, total = 8.956 ms, Queueing time: mean = 44.545 us, max = 101.417 us, min = 8.623 us, total = 10.691 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 100.041 us, total = 100.041 us, Queueing time: mean = 6.882 us, max = 6.882 us, min = 6.882 us, total = 6.882 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,714 I 660949 662200] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 47.897 us, max = 771.594 us, min = -0.000 s, total = 206.434 ms
Execution time:  mean = 76.585 us, total = 330.082 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.462 us, total = 25.376 ms, Queueing time: mean = 51.769 us, max = 771.594 us, min = -0.000 s, total = 155.254 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.845 us, total = 8.954 ms, Queueing time: mean = 43.204 us, max = 94.234 us, min = 12.960 us, total = 12.961 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.350 us, total = 3.405 ms, Queueing time: mean = 50.060 us, max = 131.396 us, min = 11.935 us, total = 15.018 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 180.231 us, total = 54.069 ms, Queueing time: mean = 52.205 us, max = 88.305 us, min = 11.270 us, total = 15.662 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 771.647 us, total = 231.494 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 34.500 us, total = 2.070 ms, Queueing time: mean = 42.029 us, max = 81.350 us, min = 13.506 us, total = 2.522 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.112 us, total = 153.351 us, Queueing time: mean = 99.151 us, max = 465.513 us, min = 17.525 us, total = 2.975 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.396 us, total = 506.770 us, Queueing time: mean = 204.138 us, max = 480.752 us, min = 6.148 us, total = 1.429 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 572.362 us, total = 2.862 ms, Queueing time: mean = 29.850 us, max = 69.980 us, min = 19.185 us, total = 149.251 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.684 us, total = 17.367 us, Queueing time: mean = 47.705 us, max = 95.409 us, min = 95.409 us, total = 95.409 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.563 us, total = 22.563 us, Queueing time: mean = 33.085 us, max = 33.085 us, min = 33.085 us, total = 33.085 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 70.283 us, total = 70.283 us, Queueing time: mean = 329.597 us, max = 329.597 us, min = 329.597 us, total = 329.597 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 279.280 us, total = 279.280 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 328.082 us, total = 328.082 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 368.033 us, total = 368.033 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.097 us, total = 106.097 us, Queueing time: mean = 7.204 us, max = 7.204 us, min = 7.204 us, total = 7.204 us

-----------------
Task execution event stats:

Global stats: 29801 total (1 active)
Queueing time: mean = 57.401 us, max = 2.351 ms, min = -0.001 s, total = 1.711 s
Execution time:  mean = 15.170 us, total = 452.093 ms
Event stats:
	CoreWorker.CheckSignal - 29800 total (1 active), Execution time: mean = 15.171 us, total = 452.085 ms, Queueing time: mean = 57.403 us, max = 2.351 ms, min = -0.001 s, total = 1.711 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.854 us, total = 7.854 us, Queueing time: mean = 2.606 us, max = 2.606 us, min = 2.606 us, total = 2.606 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 35.017 us, max = 109.227 us, min = 6.882 us, total = 31.550 ms
Execution time:  mean = 345.927 us, total = 311.681 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 791.794 us, total = 237.538 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 208.933 us, total = 62.680 ms, Queueing time: mean = 59.565 us, max = 109.227 us, min = 25.703 us, total = 17.870 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 37.875 us, total = 11.362 ms, Queueing time: mean = 45.579 us, max = 101.417 us, min = 8.623 us, total = 13.674 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 100.041 us, total = 100.041 us, Queueing time: mean = 6.882 us, max = 6.882 us, min = 6.882 us, total = 6.882 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,715 I 660949 662200] core_worker.cc:902: Event stats:


Global stats: 5169 total (8 active)
Queueing time: mean = 48.655 us, max = 771.594 us, min = -0.000 s, total = 251.498 ms
Execution time:  mean = 76.316 us, total = 394.475 ms
Event stats:
	CoreWorker.RecoverObjects - 3599 total (1 active), Execution time: mean = 8.447 us, total = 30.402 ms, Queueing time: mean = 52.632 us, max = 771.594 us, min = -0.000 s, total = 189.423 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.949 us, total = 10.782 ms, Queueing time: mean = 43.645 us, max = 94.234 us, min = 12.960 us, total = 15.712 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.472 us, total = 4.130 ms, Queueing time: mean = 51.602 us, max = 131.396 us, min = 11.935 us, total = 18.577 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 179.165 us, total = 64.499 ms, Queueing time: mean = 52.245 us, max = 95.915 us, min = 11.270 us, total = 18.808 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 768.729 us, total = 276.742 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 34.812 us, total = 2.506 ms, Queueing time: mean = 46.136 us, max = 81.350 us, min = 13.506 us, total = 3.322 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.227 us, total = 188.186 us, Queueing time: mean = 98.544 us, max = 465.513 us, min = 17.525 us, total = 3.548 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 72.396 us, total = 506.770 us, Queueing time: mean = 204.138 us, max = 480.752 us, min = 6.148 us, total = 1.429 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 587.736 us, total = 3.526 ms, Queueing time: mean = 35.703 us, max = 69.980 us, min = 19.185 us, total = 214.219 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.684 us, total = 17.367 us, Queueing time: mean = 47.705 us, max = 95.409 us, min = 95.409 us, total = 95.409 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.563 us, total = 22.563 us, Queueing time: mean = 33.085 us, max = 33.085 us, min = 33.085 us, total = 33.085 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 70.283 us, total = 70.283 us, Queueing time: mean = 329.597 us, max = 329.597 us, min = 329.597 us, total = 329.597 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 279.280 us, total = 279.280 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 328.082 us, total = 328.082 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 368.033 us, total = 368.033 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 106.097 us, total = 106.097 us, Queueing time: mean = 7.204 us, max = 7.204 us, min = 7.204 us, total = 7.204 us

-----------------
Task execution event stats:

Global stats: 35759 total (1 active)
Queueing time: mean = 57.643 us, max = 13.886 ms, min = -0.001 s, total = 2.061 s
Execution time:  mean = 15.208 us, total = 543.821 ms
Event stats:
	CoreWorker.CheckSignal - 35758 total (1 active), Execution time: mean = 15.208 us, total = 543.813 ms, Queueing time: mean = 57.645 us, max = 13.886 ms, min = -0.001 s, total = 2.061 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.854 us, total = 7.854 us, Queueing time: mean = 2.606 us, max = 2.606 us, min = 2.606 us, total = 2.606 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 35.203 us, max = 109.227 us, min = 6.882 us, total = 38.054 ms
Execution time:  mean = 346.110 us, total = 374.145 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 791.979 us, total = 285.113 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 209.048 us, total = 75.257 ms, Queueing time: mean = 60.167 us, max = 109.227 us, min = 21.964 us, total = 21.660 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 37.988 us, total = 13.676 ms, Queueing time: mean = 45.520 us, max = 101.417 us, min = 8.623 us, total = 16.387 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 100.041 us, total = 100.041 us, Queueing time: mean = 6.882 us, max = 6.882 us, min = 6.882 us, total = 6.882 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660949 662200] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660949 662200] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660949 662200] core_worker.cc:5107: Number of alive nodes:0
[2025-07-05 18:49:08,879 I 660949 662200] raylet_client.cc:281: Error reporting task backlog information: RpcError: RPC Error message: Cancelling all calls; RPC Error details:  rpc_code: 14
