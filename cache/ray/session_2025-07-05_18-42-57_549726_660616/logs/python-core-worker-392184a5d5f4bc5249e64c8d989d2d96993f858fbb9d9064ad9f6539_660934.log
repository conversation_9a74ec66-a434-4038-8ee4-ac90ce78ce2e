[2025-07-05 18:42:59,485 I 660934 660934] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660934
[2025-07-05 18:42:59,487 I 660934 660934] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,489 I 660934 660934] grpc_server.cc:141: worker server started, listening on port 32857.
[2025-07-05 18:42:59,491 I 660934 660934] core_worker.cc:542: Initializing worker at address: ***********:32857 worker_id=392184a5d5f4bc5249e64c8d989d2d96993f858fbb9d9064ad9f6539 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,491 I 660934 660934] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,492 I 660934 660934] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,492 I 660934 660934] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,492 I 660934 661622] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 10.003 us, max = 123.685 us, min = 7.850 us, total = 150.043 us
Execution time:  mean = 78.039 us, total = 1.171 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 3.364 us, total = 23.551 us, Queueing time: mean = 19.131 us, max = 123.685 us, min = 10.229 us, total = 133.914 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.720 us, total = 17.720 us, Queueing time: mean = 8.279 us, max = 8.279 us, min = 8.279 us, total = 8.279 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 350.446 us, total = 350.446 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 650.208 us, total = 650.208 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 128.658 us, total = 128.658 us, Queueing time: mean = 7.850 us, max = 7.850 us, min = 7.850 us, total = 7.850 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 9.675 us, max = 31.336 us, min = 7.366 us, total = 38.702 us
Execution time:  mean = 171.323 us, total = 685.294 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 20.114 us, total = 20.114 us, Queueing time: mean = 31.336 us, max = 31.336 us, min = 31.336 us, total = 31.336 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 133.577 us, total = 133.577 us, Queueing time: mean = 7.366 us, max = 7.366 us, min = 7.366 us, total = 7.366 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 531.603 us, total = 531.603 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,492 I 660934 660934] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,492 I 660934 660934] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,493 I 660934 661622] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,493 I 660934 661622] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,498 W 660934 661603] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,493 I 660934 661622] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 47.540 us, max = 516.539 us, min = 6.417 us, total = 41.598 ms
Execution time:  mean = 68.444 us, total = 59.888 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.366 us, total = 5.020 ms, Queueing time: mean = 52.560 us, max = 110.154 us, min = 15.448 us, total = 31.536 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.289 us, total = 627.640 us, Queueing time: mean = 43.584 us, max = 77.973 us, min = 16.563 us, total = 2.659 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 659.183 us, total = 39.551 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 173.056 us, total = 10.383 ms, Queueing time: mean = 43.082 us, max = 97.649 us, min = 13.523 us, total = 2.585 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.282 us, total = 1.637 ms, Queueing time: mean = 36.458 us, max = 107.456 us, min = 6.417 us, total = 2.187 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 30.884 us, total = 370.612 us, Queueing time: mean = 31.719 us, max = 69.996 us, min = 7.059 us, total = 380.628 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 78.259 us, total = 547.813 us, Queueing time: mean = 232.661 us, max = 516.539 us, min = 9.346 us, total = 1.629 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.601 us, total = 27.609 us, Queueing time: mean = 43.996 us, max = 72.587 us, min = 14.918 us, total = 263.975 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.720 us, total = 17.720 us, Queueing time: mean = 8.279 us, max = 8.279 us, min = 8.279 us, total = 8.279 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 350.446 us, total = 350.446 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 512.372 us, total = 512.372 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 64.540 us, total = 64.540 us, Queueing time: mean = 341.104 us, max = 341.104 us, min = 341.104 us, total = 341.104 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 128.658 us, total = 128.658 us, Queueing time: mean = 7.850 us, max = 7.850 us, min = 7.850 us, total = 7.850 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 650.208 us, total = 650.208 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.388 us, max = 2.349 ms, min = -0.000 s, total = 324.316 ms
Execution time:  mean = 14.927 us, total = 89.012 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 14.928 us, total = 89.003 ms, Queueing time: mean = 54.397 us, max = 2.349 ms, min = -0.000 s, total = 324.313 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.684 us, total = 8.684 us, Queueing time: mean = 2.979 us, max = 2.979 us, min = 2.979 us, total = 2.979 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 30.078 us, max = 97.746 us, min = 7.366 us, total = 5.444 ms
Execution time:  mean = 310.147 us, total = 56.137 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 203.794 us, total = 12.228 ms, Queueing time: mean = 55.160 us, max = 97.746 us, min = 20.060 us, total = 3.310 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 34.435 us, total = 2.066 ms, Queueing time: mean = 35.453 us, max = 62.257 us, min = 15.306 us, total = 2.127 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 695.156 us, total = 41.709 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 133.577 us, total = 133.577 us, Queueing time: mean = 7.366 us, max = 7.366 us, min = 7.366 us, total = 7.366 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,494 I 660934 661622] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 50.308 us, max = 516.539 us, min = 6.417 us, total = 87.184 ms
Execution time:  mean = 71.860 us, total = 124.534 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.331 us, total = 9.997 ms, Queueing time: mean = 55.974 us, max = 175.230 us, min = 13.071 us, total = 67.169 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 706.993 us, total = 84.839 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 175.754 us, total = 21.090 ms, Queueing time: mean = 49.143 us, max = 104.343 us, min = 13.523 us, total = 5.897 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.649 us, total = 3.438 ms, Queueing time: mean = 37.665 us, max = 107.456 us, min = 6.417 us, total = 4.520 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.248 us, total = 1.230 ms, Queueing time: mean = 50.181 us, max = 80.167 us, min = 13.090 us, total = 6.022 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 38.159 us, total = 915.807 us, Queueing time: mean = 33.632 us, max = 71.686 us, min = 7.059 us, total = 807.156 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.624 us, total = 55.486 us, Queueing time: mean = 59.703 us, max = 197.743 us, min = 14.918 us, total = 716.431 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 78.259 us, total = 547.813 us, Queueing time: mean = 232.661 us, max = 516.539 us, min = 9.346 us, total = 1.629 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 348.582 us, total = 697.165 us, Queueing time: mean = 33.408 us, max = 66.817 us, min = 66.817 us, total = 66.817 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.720 us, total = 17.720 us, Queueing time: mean = 8.279 us, max = 8.279 us, min = 8.279 us, total = 8.279 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 350.446 us, total = 350.446 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 512.372 us, total = 512.372 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 64.540 us, total = 64.540 us, Queueing time: mean = 341.104 us, max = 341.104 us, min = 341.104 us, total = 341.104 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 128.658 us, total = 128.658 us, Queueing time: mean = 7.850 us, max = 7.850 us, min = 7.850 us, total = 7.850 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 650.208 us, total = 650.208 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11926 total (1 active)
Queueing time: mean = 53.629 us, max = 2.349 ms, min = -0.000 s, total = 639.577 ms
Execution time:  mean = 14.912 us, total = 177.835 ms
Event stats:
	CoreWorker.CheckSignal - 11925 total (1 active), Execution time: mean = 14.912 us, total = 177.826 ms, Queueing time: mean = 53.633 us, max = 2.349 ms, min = -0.000 s, total = 639.574 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.684 us, total = 8.684 us, Queueing time: mean = 2.979 us, max = 2.979 us, min = 2.979 us, total = 2.979 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 30.213 us, max = 97.746 us, min = 7.366 us, total = 10.907 ms
Execution time:  mean = 310.151 us, total = 111.965 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 200.491 us, total = 24.059 ms, Queueing time: mean = 53.526 us, max = 97.746 us, min = 20.060 us, total = 6.423 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.300 us, total = 4.116 ms, Queueing time: mean = 37.303 us, max = 68.674 us, min = 15.306 us, total = 4.476 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 697.133 us, total = 83.656 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 133.577 us, total = 133.577 us, Queueing time: mean = 7.366 us, max = 7.366 us, min = 7.366 us, total = 7.366 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,495 I 660934 661622] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 50.438 us, max = 747.696 us, min = 6.417 us, total = 130.684 ms
Execution time:  mean = 72.138 us, total = 186.910 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.337 us, total = 14.998 ms, Queueing time: mean = 55.696 us, max = 175.230 us, min = 13.071 us, total = 100.198 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 707.605 us, total = 127.369 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 177.998 us, total = 32.040 ms, Queueing time: mean = 50.615 us, max = 110.520 us, min = 13.523 us, total = 9.111 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.249 us, total = 5.265 ms, Queueing time: mean = 40.216 us, max = 107.456 us, min = 6.417 us, total = 7.239 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.425 us, total = 1.877 ms, Queueing time: mean = 50.299 us, max = 82.961 us, min = 13.090 us, total = 9.054 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 37.561 us, total = 1.352 ms, Queueing time: mean = 35.445 us, max = 73.095 us, min = 7.059 us, total = 1.276 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.984 us, total = 107.717 us, Queueing time: mean = 92.366 us, max = 747.696 us, min = 14.918 us, total = 1.663 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 78.259 us, total = 547.813 us, Queueing time: mean = 232.661 us, max = 516.539 us, min = 9.346 us, total = 1.629 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 543.477 us, total = 1.630 ms, Queueing time: mean = 52.994 us, max = 92.165 us, min = 66.817 us, total = 158.982 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.720 us, total = 17.720 us, Queueing time: mean = 8.279 us, max = 8.279 us, min = 8.279 us, total = 8.279 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 350.446 us, total = 350.446 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 512.372 us, total = 512.372 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 64.540 us, total = 64.540 us, Queueing time: mean = 341.104 us, max = 341.104 us, min = 341.104 us, total = 341.104 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 128.658 us, total = 128.658 us, Queueing time: mean = 7.850 us, max = 7.850 us, min = 7.850 us, total = 7.850 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 650.208 us, total = 650.208 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17886 total (1 active)
Queueing time: mean = 54.594 us, max = 2.349 ms, min = -0.000 s, total = 976.466 ms
Execution time:  mean = 15.016 us, total = 268.570 ms
Event stats:
	CoreWorker.CheckSignal - 17885 total (1 active), Execution time: mean = 15.016 us, total = 268.561 ms, Queueing time: mean = 54.597 us, max = 2.349 ms, min = -0.000 s, total = 976.463 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.684 us, total = 8.684 us, Queueing time: mean = 2.979 us, max = 2.979 us, min = 2.979 us, total = 2.979 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 29.558 us, max = 97.746 us, min = 7.366 us, total = 15.991 ms
Execution time:  mean = 310.229 us, total = 167.834 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 201.672 us, total = 36.301 ms, Queueing time: mean = 50.380 us, max = 97.746 us, min = 20.060 us, total = 9.068 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 35.162 us, total = 6.329 ms, Queueing time: mean = 38.417 us, max = 72.401 us, min = 15.306 us, total = 6.915 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 694.833 us, total = 125.070 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 133.577 us, total = 133.577 us, Queueing time: mean = 7.366 us, max = 7.366 us, min = 7.366 us, total = 7.366 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,496 I 660934 661622] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 49.848 us, max = 747.696 us, min = -0.000 s, total = 171.977 ms
Execution time:  mean = 71.812 us, total = 247.753 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.284 us, total = 19.873 ms, Queueing time: mean = 54.831 us, max = 175.230 us, min = -0.000 s, total = 131.538 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 704.191 us, total = 169.006 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 178.399 us, total = 42.816 ms, Queueing time: mean = 50.219 us, max = 110.520 us, min = 13.523 us, total = 12.053 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.637 us, total = 7.113 ms, Queueing time: mean = 40.657 us, max = 107.456 us, min = 6.417 us, total = 9.758 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.377 us, total = 2.490 ms, Queueing time: mean = 50.650 us, max = 86.921 us, min = 13.090 us, total = 12.156 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 36.766 us, total = 1.765 ms, Queueing time: mean = 37.734 us, max = 75.652 us, min = 7.059 us, total = 1.811 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.773 us, total = 138.563 us, Queueing time: mean = 102.078 us, max = 747.696 us, min = 14.918 us, total = 2.450 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 78.259 us, total = 547.813 us, Queueing time: mean = 232.661 us, max = 516.539 us, min = 9.346 us, total = 1.629 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 569.857 us, total = 2.279 ms, Queueing time: mean = 56.403 us, max = 92.165 us, min = 66.631 us, total = 225.613 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.720 us, total = 17.720 us, Queueing time: mean = 8.279 us, max = 8.279 us, min = 8.279 us, total = 8.279 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 350.446 us, total = 350.446 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 512.372 us, total = 512.372 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 64.540 us, total = 64.540 us, Queueing time: mean = 341.104 us, max = 341.104 us, min = 341.104 us, total = 341.104 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 128.658 us, total = 128.658 us, Queueing time: mean = 7.850 us, max = 7.850 us, min = 7.850 us, total = 7.850 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 650.208 us, total = 650.208 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23845 total (1 active)
Queueing time: mean = 55.720 us, max = 2.349 ms, min = -0.000 s, total = 1.329 s
Execution time:  mean = 15.039 us, total = 358.599 ms
Event stats:
	CoreWorker.CheckSignal - 23844 total (1 active), Execution time: mean = 15.039 us, total = 358.591 ms, Queueing time: mean = 55.722 us, max = 2.349 ms, min = -0.000 s, total = 1.329 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.684 us, total = 8.684 us, Queueing time: mean = 2.979 us, max = 2.979 us, min = 2.979 us, total = 2.979 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 29.701 us, max = 98.616 us, min = 7.366 us, total = 21.414 ms
Execution time:  mean = 317.175 us, total = 228.683 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 203.981 us, total = 48.956 ms, Queueing time: mean = 50.611 us, max = 98.616 us, min = 20.060 us, total = 12.147 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.100 us, total = 8.664 ms, Queueing time: mean = 38.583 us, max = 72.401 us, min = 15.306 us, total = 9.260 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 712.209 us, total = 170.930 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 133.577 us, total = 133.577 us, Queueing time: mean = 7.366 us, max = 7.366 us, min = 7.366 us, total = 7.366 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,496 I 660934 661622] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 50.038 us, max = 747.696 us, min = -0.000 s, total = 215.662 ms
Execution time:  mean = 72.394 us, total = 312.019 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.253 us, total = 24.750 ms, Queueing time: mean = 54.944 us, max = 207.443 us, min = -0.000 s, total = 164.777 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 712.536 us, total = 213.761 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 179.326 us, total = 53.798 ms, Queueing time: mean = 50.023 us, max = 110.520 us, min = 12.861 us, total = 15.007 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.842 us, total = 8.952 ms, Queueing time: mean = 40.183 us, max = 107.456 us, min = 6.417 us, total = 12.055 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.489 us, total = 3.147 ms, Queueing time: mean = 52.866 us, max = 143.007 us, min = 13.090 us, total = 15.860 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 36.456 us, total = 2.187 ms, Queueing time: mean = 41.859 us, max = 75.652 us, min = 7.059 us, total = 2.512 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.693 us, total = 170.790 us, Queueing time: mean = 104.657 us, max = 747.696 us, min = 14.918 us, total = 3.140 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 78.259 us, total = 547.813 us, Queueing time: mean = 232.661 us, max = 516.539 us, min = 9.346 us, total = 1.629 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 593.712 us, total = 2.969 ms, Queueing time: mean = 52.719 us, max = 92.165 us, min = 37.982 us, total = 263.595 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.556 us, total = 13.113 us, Queueing time: mean = 31.596 us, max = 63.192 us, min = 63.192 us, total = 63.192 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.720 us, total = 17.720 us, Queueing time: mean = 8.279 us, max = 8.279 us, min = 8.279 us, total = 8.279 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 350.446 us, total = 350.446 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 512.372 us, total = 512.372 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 64.540 us, total = 64.540 us, Queueing time: mean = 341.104 us, max = 341.104 us, min = 341.104 us, total = 341.104 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 128.658 us, total = 128.658 us, Queueing time: mean = 7.850 us, max = 7.850 us, min = 7.850 us, total = 7.850 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 650.208 us, total = 650.208 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29805 total (1 active)
Queueing time: mean = 56.057 us, max = 2.349 ms, min = -0.000 s, total = 1.671 s
Execution time:  mean = 15.051 us, total = 448.593 ms
Event stats:
	CoreWorker.CheckSignal - 29804 total (1 active), Execution time: mean = 15.051 us, total = 448.584 ms, Queueing time: mean = 56.059 us, max = 2.349 ms, min = -0.000 s, total = 1.671 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.684 us, total = 8.684 us, Queueing time: mean = 2.979 us, max = 2.979 us, min = 2.979 us, total = 2.979 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 30.303 us, max = 101.005 us, min = 7.366 us, total = 27.303 ms
Execution time:  mean = 321.958 us, total = 290.085 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 205.478 us, total = 61.643 ms, Queueing time: mean = 50.891 us, max = 101.005 us, min = 20.060 us, total = 15.267 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.684 us, total = 11.005 ms, Queueing time: mean = 40.096 us, max = 72.401 us, min = 15.306 us, total = 12.029 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 724.341 us, total = 217.302 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 133.577 us, total = 133.577 us, Queueing time: mean = 7.366 us, max = 7.366 us, min = 7.366 us, total = 7.366 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,497 I 660934 661622] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.202 us, max = 747.696 us, min = -0.000 s, total = 259.443 ms
Execution time:  mean = 71.995 us, total = 372.072 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.225 us, total = 29.595 ms, Queueing time: mean = 55.376 us, max = 207.443 us, min = -0.000 s, total = 199.241 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 709.315 us, total = 255.353 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 177.554 us, total = 63.919 ms, Queueing time: mean = 49.572 us, max = 110.520 us, min = 12.861 us, total = 17.846 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.480 us, total = 10.613 ms, Queueing time: mean = 38.403 us, max = 107.456 us, min = 6.417 us, total = 13.825 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.534 us, total = 3.792 ms, Queueing time: mean = 53.059 us, max = 143.007 us, min = 13.090 us, total = 19.101 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 37.571 us, total = 2.705 ms, Queueing time: mean = 44.084 us, max = 75.652 us, min = 7.059 us, total = 3.174 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.646 us, total = 203.270 us, Queueing time: mean = 107.685 us, max = 747.696 us, min = 14.918 us, total = 3.877 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 78.259 us, total = 547.813 us, Queueing time: mean = 232.661 us, max = 516.539 us, min = 9.346 us, total = 1.629 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 601.023 us, total = 3.606 ms, Queueing time: mean = 54.792 us, max = 92.165 us, min = 37.982 us, total = 328.751 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.556 us, total = 13.113 us, Queueing time: mean = 31.596 us, max = 63.192 us, min = 63.192 us, total = 63.192 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 17.720 us, total = 17.720 us, Queueing time: mean = 8.279 us, max = 8.279 us, min = 8.279 us, total = 8.279 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 350.446 us, total = 350.446 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 512.372 us, total = 512.372 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 64.540 us, total = 64.540 us, Queueing time: mean = 341.104 us, max = 341.104 us, min = 341.104 us, total = 341.104 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 128.658 us, total = 128.658 us, Queueing time: mean = 7.850 us, max = 7.850 us, min = 7.850 us, total = 7.850 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 650.208 us, total = 650.208 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35763 total (1 active)
Queueing time: mean = 56.592 us, max = 5.859 ms, min = -0.000 s, total = 2.024 s
Execution time:  mean = 15.032 us, total = 537.604 ms
Event stats:
	CoreWorker.CheckSignal - 35762 total (1 active), Execution time: mean = 15.033 us, total = 537.595 ms, Queueing time: mean = 56.593 us, max = 5.859 ms, min = -0.000 s, total = 2.024 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.684 us, total = 8.684 us, Queueing time: mean = 2.979 us, max = 2.979 us, min = 2.979 us, total = 2.979 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 31.283 us, max = 112.896 us, min = 7.366 us, total = 33.817 ms
Execution time:  mean = 324.195 us, total = 350.455 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 205.058 us, total = 73.821 ms, Queueing time: mean = 52.405 us, max = 112.896 us, min = 19.509 us, total = 18.866 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.544 us, total = 13.156 ms, Queueing time: mean = 41.511 us, max = 72.401 us, min = 15.306 us, total = 14.944 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 731.513 us, total = 263.345 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 133.577 us, total = 133.577 us, Queueing time: mean = 7.366 us, max = 7.366 us, min = 7.366 us, total = 7.366 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660934 661622] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660934 661622] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660934 661622] core_worker.cc:5107: Number of alive nodes:0
