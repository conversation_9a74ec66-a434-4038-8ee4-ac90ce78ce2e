[2025-07-05 18:42:59,480 I 660930 660930] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660930
[2025-07-05 18:42:59,484 I 660930 660930] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,487 I 660930 660930] grpc_server.cc:141: worker server started, listening on port 33843.
[2025-07-05 18:42:59,489 I 660930 660930] core_worker.cc:542: Initializing worker at address: ***********:33843 worker_id=8aa84941deee08202bfa969484e7ed1f1c8669697b5f4e819ed3b33d node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,490 I 660930 660930] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,491 I 660930 660930] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,491 I 660930 660930] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,491 I 660930 661574] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 10.210 us, max = 121.373 us, min = 7.765 us, total = 153.152 us
Execution time:  mean = 62.478 us, total = 937.175 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 4.146 us, total = 29.021 us, Queueing time: mean = 18.448 us, max = 121.373 us, min = 7.765 us, total = 129.138 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.799 us, total = 123.799 us, Queueing time: mean = 10.302 us, max = 10.302 us, min = 10.302 us, total = 10.302 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 36.956 us, total = 36.956 us, Queueing time: mean = 13.712 us, max = 13.712 us, min = 13.712 us, total = 13.712 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 371.221 us, total = 371.221 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.178 us, total = 376.178 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.532 us, max = 8.726 us, min = 5.402 us, total = 14.128 us
Execution time:  mean = 139.556 us, total = 558.223 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 13.488 us, total = 13.488 us, Queueing time: mean = 8.726 us, max = 8.726 us, min = 8.726 us, total = 8.726 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 99.057 us, total = 99.057 us, Queueing time: mean = 5.402 us, max = 5.402 us, min = 5.402 us, total = 5.402 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 445.678 us, total = 445.678 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,491 I 660930 660930] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,491 I 660930 660930] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,491 I 660930 661574] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,491 I 660930 661574] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,495 W 660930 661562] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,491 I 660930 661574] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 52.593 us, max = 503.624 us, min = 7.765 us, total = 46.019 ms
Execution time:  mean = 74.335 us, total = 65.044 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.148 us, total = 4.889 ms, Queueing time: mean = 57.993 us, max = 156.217 us, min = 17.739 us, total = 34.796 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.086 us, total = 615.251 us, Queueing time: mean = 54.224 us, max = 80.236 us, min = 17.490 us, total = 3.308 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 749.028 us, total = 44.942 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 28.639 us, total = 1.718 ms, Queueing time: mean = 44.092 us, max = 67.064 us, min = 10.030 us, total = 2.646 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 168.524 us, total = 10.111 ms, Queueing time: mean = 49.894 us, max = 86.178 us, min = 18.480 us, total = 2.994 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 31.766 us, total = 381.196 us, Queueing time: mean = 37.612 us, max = 74.861 us, min = 14.492 us, total = 451.344 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 76.772 us, total = 537.406 us, Queueing time: mean = 229.342 us, max = 503.624 us, min = 7.765 us, total = 1.605 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.066 us, total = 30.395 us, Queueing time: mean = 30.881 us, max = 66.802 us, min = 25.373 us, total = 185.285 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.681 us, total = 54.681 us, Queueing time: mean = 9.958 us, max = 9.958 us, min = 9.958 us, total = 9.958 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 36.956 us, total = 36.956 us, Queueing time: mean = 13.712 us, max = 13.712 us, min = 13.712 us, total = 13.712 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 856.172 us, total = 856.172 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.799 us, total = 123.799 us, Queueing time: mean = 10.302 us, max = 10.302 us, min = 10.302 us, total = 10.302 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.178 us, total = 376.178 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 371.221 us, total = 371.221 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5960 total (1 active)
Queueing time: mean = 58.830 us, max = 1.246 ms, min = 2.922 us, total = 350.629 ms
Execution time:  mean = 14.900 us, total = 88.803 ms
Event stats:
	CoreWorker.CheckSignal - 5959 total (1 active), Execution time: mean = 14.901 us, total = 88.794 ms, Queueing time: mean = 58.840 us, max = 1.246 ms, min = 9.586 us, total = 350.626 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.962 us, total = 8.962 us, Queueing time: mean = 2.922 us, max = 2.922 us, min = 2.922 us, total = 2.922 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 31.274 us, max = 99.560 us, min = 5.402 us, total = 5.661 ms
Execution time:  mean = 331.039 us, total = 59.918 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 37.703 us, total = 2.262 ms, Queueing time: mean = 40.241 us, max = 94.101 us, min = 8.726 us, total = 2.414 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 204.388 us, total = 12.263 ms, Queueing time: mean = 54.011 us, max = 99.560 us, min = 15.881 us, total = 3.241 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 754.892 us, total = 45.294 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 99.057 us, total = 99.057 us, Queueing time: mean = 5.402 us, max = 5.402 us, min = 5.402 us, total = 5.402 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,492 I 660930 661574] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 53.285 us, max = 503.624 us, min = 7.765 us, total = 92.343 ms
Execution time:  mean = 75.028 us, total = 130.024 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.188 us, total = 9.825 ms, Queueing time: mean = 58.672 us, max = 156.217 us, min = 17.739 us, total = 70.406 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 758.665 us, total = 91.040 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.215 us, total = 3.506 ms, Queueing time: mean = 45.792 us, max = 88.824 us, min = 10.030 us, total = 5.495 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.324 us, total = 1.239 ms, Queueing time: mean = 55.615 us, max = 82.999 us, min = 14.407 us, total = 6.674 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 170.492 us, total = 20.459 ms, Queueing time: mean = 53.632 us, max = 88.131 us, min = 18.480 us, total = 6.436 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 34.656 us, total = 831.744 us, Queueing time: mean = 44.412 us, max = 76.984 us, min = 14.492 us, total = 1.066 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.840 us, total = 58.085 us, Queueing time: mean = 49.171 us, max = 271.883 us, min = 16.179 us, total = 590.047 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 76.772 us, total = 537.406 us, Queueing time: mean = 229.342 us, max = 503.624 us, min = 7.765 us, total = 1.605 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 354.671 us, total = 709.342 us, Queueing time: mean = 18.424 us, max = 36.847 us, min = 36.847 us, total = 36.847 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.681 us, total = 54.681 us, Queueing time: mean = 9.958 us, max = 9.958 us, min = 9.958 us, total = 9.958 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 36.956 us, total = 36.956 us, Queueing time: mean = 13.712 us, max = 13.712 us, min = 13.712 us, total = 13.712 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 856.172 us, total = 856.172 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.799 us, total = 123.799 us, Queueing time: mean = 10.302 us, max = 10.302 us, min = 10.302 us, total = 10.302 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.178 us, total = 376.178 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 371.221 us, total = 371.221 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11923 total (1 active)
Queueing time: mean = 55.672 us, max = 1.246 ms, min = 2.922 us, total = 663.773 ms
Execution time:  mean = 15.124 us, total = 180.327 ms
Event stats:
	CoreWorker.CheckSignal - 11922 total (1 active), Execution time: mean = 15.125 us, total = 180.318 ms, Queueing time: mean = 55.676 us, max = 1.246 ms, min = 9.586 us, total = 663.770 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.962 us, total = 8.962 us, Queueing time: mean = 2.922 us, max = 2.922 us, min = 2.922 us, total = 2.922 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 31.625 us, max = 104.867 us, min = 5.402 us, total = 11.417 ms
Execution time:  mean = 333.925 us, total = 120.547 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 37.167 us, total = 4.460 ms, Queueing time: mean = 40.586 us, max = 94.101 us, min = 8.726 us, total = 4.870 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 203.982 us, total = 24.478 ms, Queueing time: mean = 54.507 us, max = 104.867 us, min = 15.881 us, total = 6.541 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 762.585 us, total = 91.510 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 99.057 us, total = 99.057 us, Queueing time: mean = 5.402 us, max = 5.402 us, min = 5.402 us, total = 5.402 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,493 I 660930 661574] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 54.463 us, max = 692.551 us, min = 7.765 us, total = 141.113 ms
Execution time:  mean = 75.355 us, total = 195.245 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.191 us, total = 14.736 ms, Queueing time: mean = 59.897 us, max = 156.217 us, min = 17.739 us, total = 107.755 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 763.745 us, total = 137.474 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.416 us, total = 5.295 ms, Queueing time: mean = 46.108 us, max = 88.824 us, min = 10.030 us, total = 8.299 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.291 us, total = 1.852 ms, Queueing time: mean = 55.621 us, max = 85.945 us, min = 14.407 us, total = 10.012 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 170.667 us, total = 30.720 ms, Queueing time: mean = 54.782 us, max = 88.131 us, min = 18.480 us, total = 9.861 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 34.783 us, total = 1.252 ms, Queueing time: mean = 51.306 us, max = 79.655 us, min = 14.492 us, total = 1.847 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.827 us, total = 86.895 us, Queueing time: mean = 87.651 us, max = 692.551 us, min = 16.179 us, total = 1.578 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 76.772 us, total = 537.406 us, Queueing time: mean = 229.342 us, max = 503.624 us, min = 7.765 us, total = 1.605 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 490.568 us, total = 1.472 ms, Queueing time: mean = 40.740 us, max = 85.373 us, min = 36.847 us, total = 122.220 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.681 us, total = 54.681 us, Queueing time: mean = 9.958 us, max = 9.958 us, min = 9.958 us, total = 9.958 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 36.956 us, total = 36.956 us, Queueing time: mean = 13.712 us, max = 13.712 us, min = 13.712 us, total = 13.712 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 856.172 us, total = 856.172 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.799 us, total = 123.799 us, Queueing time: mean = 10.302 us, max = 10.302 us, min = 10.302 us, total = 10.302 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.178 us, total = 376.178 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 371.221 us, total = 371.221 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17887 total (1 active)
Queueing time: mean = 53.770 us, max = 1.246 ms, min = -0.000 s, total = 961.787 ms
Execution time:  mean = 15.154 us, total = 271.053 ms
Event stats:
	CoreWorker.CheckSignal - 17886 total (1 active), Execution time: mean = 15.154 us, total = 271.044 ms, Queueing time: mean = 53.773 us, max = 1.246 ms, min = -0.000 s, total = 961.784 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.962 us, total = 8.962 us, Queueing time: mean = 2.922 us, max = 2.922 us, min = 2.922 us, total = 2.922 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 31.380 us, max = 104.867 us, min = 5.402 us, total = 16.977 ms
Execution time:  mean = 334.022 us, total = 180.706 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 38.032 us, total = 6.846 ms, Queueing time: mean = 40.454 us, max = 94.101 us, min = 8.726 us, total = 7.282 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 206.491 us, total = 37.168 ms, Queueing time: mean = 53.830 us, max = 104.867 us, min = 15.881 us, total = 9.689 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 758.847 us, total = 136.593 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 99.057 us, total = 99.057 us, Queueing time: mean = 5.402 us, max = 5.402 us, min = 5.402 us, total = 5.402 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,494 I 660930 661574] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 54.669 us, max = 692.551 us, min = 7.765 us, total = 188.607 ms
Execution time:  mean = 75.122 us, total = 259.172 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.148 us, total = 19.546 ms, Queueing time: mean = 59.931 us, max = 156.217 us, min = 17.739 us, total = 143.775 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 761.579 us, total = 182.779 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.489 us, total = 7.077 ms, Queueing time: mean = 45.483 us, max = 88.824 us, min = 10.030 us, total = 10.916 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.337 us, total = 2.481 ms, Queueing time: mean = 57.527 us, max = 85.945 us, min = 14.407 us, total = 13.806 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 170.783 us, total = 40.988 ms, Queueing time: mean = 56.043 us, max = 427.647 us, min = 18.480 us, total = 13.450 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 34.907 us, total = 1.676 ms, Queueing time: mean = 50.849 us, max = 94.999 us, min = 14.492 us, total = 2.441 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 4.967 us, total = 119.205 us, Queueing time: mean = 99.182 us, max = 692.551 us, min = 16.179 us, total = 2.380 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 76.772 us, total = 537.406 us, Queueing time: mean = 229.342 us, max = 503.624 us, min = 7.765 us, total = 1.605 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 537.312 us, total = 2.149 ms, Queueing time: mean = 49.712 us, max = 85.373 us, min = 36.847 us, total = 198.849 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.681 us, total = 54.681 us, Queueing time: mean = 9.958 us, max = 9.958 us, min = 9.958 us, total = 9.958 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 36.956 us, total = 36.956 us, Queueing time: mean = 13.712 us, max = 13.712 us, min = 13.712 us, total = 13.712 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 856.172 us, total = 856.172 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.799 us, total = 123.799 us, Queueing time: mean = 10.302 us, max = 10.302 us, min = 10.302 us, total = 10.302 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.178 us, total = 376.178 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 371.221 us, total = 371.221 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23849 total (1 active)
Queueing time: mean = 53.907 us, max = 1.246 ms, min = -0.000 s, total = 1.286 s
Execution time:  mean = 15.177 us, total = 361.962 ms
Event stats:
	CoreWorker.CheckSignal - 23848 total (1 active), Execution time: mean = 15.178 us, total = 361.953 ms, Queueing time: mean = 53.909 us, max = 1.246 ms, min = -0.000 s, total = 1.286 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.962 us, total = 8.962 us, Queueing time: mean = 2.922 us, max = 2.922 us, min = 2.922 us, total = 2.922 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 31.564 us, max = 104.867 us, min = 5.402 us, total = 22.758 ms
Execution time:  mean = 332.120 us, total = 239.458 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 37.634 us, total = 9.032 ms, Queueing time: mean = 41.158 us, max = 94.101 us, min = 8.726 us, total = 9.878 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 204.502 us, total = 49.080 ms, Queueing time: mean = 53.644 us, max = 104.867 us, min = 15.881 us, total = 12.875 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 755.195 us, total = 181.247 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 99.057 us, total = 99.057 us, Queueing time: mean = 5.402 us, max = 5.402 us, min = 5.402 us, total = 5.402 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,495 I 660930 661574] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 53.892 us, max = 692.551 us, min = 7.765 us, total = 232.276 ms
Execution time:  mean = 75.288 us, total = 324.491 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.153 us, total = 24.449 ms, Queueing time: mean = 59.169 us, max = 156.217 us, min = 15.195 us, total = 177.447 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 763.862 us, total = 229.159 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.343 us, total = 8.803 ms, Queueing time: mean = 44.249 us, max = 88.824 us, min = 10.030 us, total = 13.275 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.413 us, total = 3.124 ms, Queueing time: mean = 57.137 us, max = 104.186 us, min = 14.407 us, total = 17.141 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 171.632 us, total = 51.490 ms, Queueing time: mean = 53.741 us, max = 427.647 us, min = 13.530 us, total = 16.122 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 34.813 us, total = 2.089 ms, Queueing time: mean = 51.901 us, max = 94.999 us, min = 14.492 us, total = 3.114 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.035 us, total = 151.064 us, Queueing time: mean = 104.110 us, max = 692.551 us, min = 16.179 us, total = 3.123 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 76.772 us, total = 537.406 us, Queueing time: mean = 229.342 us, max = 503.624 us, min = 7.765 us, total = 1.605 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 570.269 us, total = 2.851 ms, Queueing time: mean = 53.577 us, max = 85.373 us, min = 36.847 us, total = 267.885 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 9.475 us, total = 18.950 us, Queueing time: mean = 72.866 us, max = 145.732 us, min = 145.732 us, total = 145.732 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.681 us, total = 54.681 us, Queueing time: mean = 9.958 us, max = 9.958 us, min = 9.958 us, total = 9.958 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 36.956 us, total = 36.956 us, Queueing time: mean = 13.712 us, max = 13.712 us, min = 13.712 us, total = 13.712 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 856.172 us, total = 856.172 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.799 us, total = 123.799 us, Queueing time: mean = 10.302 us, max = 10.302 us, min = 10.302 us, total = 10.302 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.178 us, total = 376.178 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 371.221 us, total = 371.221 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29810 total (1 active)
Queueing time: mean = 54.210 us, max = 1.246 ms, min = -0.000 s, total = 1.616 s
Execution time:  mean = 15.176 us, total = 452.411 ms
Event stats:
	CoreWorker.CheckSignal - 29809 total (1 active), Execution time: mean = 15.177 us, total = 452.402 ms, Queueing time: mean = 54.212 us, max = 1.246 ms, min = -0.000 s, total = 1.616 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.962 us, total = 8.962 us, Queueing time: mean = 2.922 us, max = 2.922 us, min = 2.922 us, total = 2.922 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 30.739 us, max = 104.867 us, min = 5.402 us, total = 27.696 ms
Execution time:  mean = 329.511 us, total = 296.890 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 37.860 us, total = 11.358 ms, Queueing time: mean = 39.806 us, max = 94.101 us, min = 8.726 us, total = 11.942 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 204.622 us, total = 61.386 ms, Queueing time: mean = 52.495 us, max = 104.867 us, min = 15.881 us, total = 15.748 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 746.820 us, total = 224.046 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 99.057 us, total = 99.057 us, Queueing time: mean = 5.402 us, max = 5.402 us, min = 5.402 us, total = 5.402 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,495 I 660930 661574] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 53.757 us, max = 692.551 us, min = 7.765 us, total = 277.817 ms
Execution time:  mean = 75.116 us, total = 388.200 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.144 us, total = 29.303 ms, Queueing time: mean = 59.012 us, max = 156.217 us, min = 15.195 us, total = 212.327 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 761.044 us, total = 273.976 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.087 us, total = 10.471 ms, Queueing time: mean = 43.570 us, max = 89.057 us, min = 10.030 us, total = 15.685 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.719 us, total = 3.859 ms, Queueing time: mean = 57.240 us, max = 104.186 us, min = 14.407 us, total = 20.606 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 172.048 us, total = 61.937 ms, Queueing time: mean = 54.458 us, max = 427.647 us, min = 13.530 us, total = 19.605 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.068 us, total = 2.525 ms, Queueing time: mean = 52.224 us, max = 94.999 us, min = 14.492 us, total = 3.760 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.075 us, total = 182.685 us, Queueing time: mean = 102.945 us, max = 692.551 us, min = 16.179 us, total = 3.706 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 76.772 us, total = 537.406 us, Queueing time: mean = 229.342 us, max = 503.624 us, min = 7.765 us, total = 1.605 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 595.151 us, total = 3.571 ms, Queueing time: mean = 57.044 us, max = 85.373 us, min = 36.847 us, total = 342.265 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 9.475 us, total = 18.950 us, Queueing time: mean = 72.866 us, max = 145.732 us, min = 145.732 us, total = 145.732 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 54.681 us, total = 54.681 us, Queueing time: mean = 9.958 us, max = 9.958 us, min = 9.958 us, total = 9.958 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 36.956 us, total = 36.956 us, Queueing time: mean = 13.712 us, max = 13.712 us, min = 13.712 us, total = 13.712 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 856.172 us, total = 856.172 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.799 us, total = 123.799 us, Queueing time: mean = 10.302 us, max = 10.302 us, min = 10.302 us, total = 10.302 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 376.178 us, total = 376.178 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 371.221 us, total = 371.221 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35770 total (1 active)
Queueing time: mean = 54.639 us, max = 7.505 ms, min = -0.000 s, total = 1.954 s
Execution time:  mean = 15.193 us, total = 543.437 ms
Event stats:
	CoreWorker.CheckSignal - 35769 total (1 active), Execution time: mean = 15.193 us, total = 543.428 ms, Queueing time: mean = 54.641 us, max = 7.505 ms, min = -0.000 s, total = 1.954 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.962 us, total = 8.962 us, Queueing time: mean = 2.922 us, max = 2.922 us, min = 2.922 us, total = 2.922 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 30.995 us, max = 104.867 us, min = 5.402 us, total = 33.505 ms
Execution time:  mean = 330.168 us, total = 356.912 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 38.118 us, total = 13.722 ms, Queueing time: mean = 40.753 us, max = 94.101 us, min = 8.726 us, total = 14.671 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 204.498 us, total = 73.619 ms, Queueing time: mean = 52.302 us, max = 104.867 us, min = 15.881 us, total = 18.829 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 748.531 us, total = 269.471 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 99.057 us, total = 99.057 us, Queueing time: mean = 5.402 us, max = 5.402 us, min = 5.402 us, total = 5.402 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660930 661574] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660930 661574] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660930 661574] core_worker.cc:5107: Number of alive nodes:0
