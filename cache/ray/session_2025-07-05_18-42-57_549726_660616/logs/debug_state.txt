NodeManager:
Node ID: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
Node name: 10.10.249.4
InitialConfigResources: {object_store_memory: 7.93075e+10, accelerator_type:G: 1, node:__internal_head__: 1, GPU: 4, CPU: 24, node:10.10.249.4: 1, memory: 1.85051e+11}
ClusterTaskManager:
========== Node: 81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d =================
Infeasible queue length: 0
Schedule queue length: 0
Dispatch queue length: 0
num_waiting_for_resource: 0
num_waiting_for_plasma_memory: 0
num_waiting_for_remote_node_resources: 0
num_worker_not_started_by_job_config_not_exist: 0
num_worker_not_started_by_registration_timeout: 0
num_tasks_waiting_for_workers: 0
num_cancelled_tasks: 0
cluster_resource_scheduler state: 
Local id: 9214086572761971740 Local resources: {"total":{CPU: [240000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [10000, 10000, 10000, 10000], object_store_memory: [793074573310000], node:10.10.249.4: [10000]}}, "available": {CPU: [230000], accelerator_type:G: [10000], node:__internal_head__: [10000], memory: [1850507337730000], GPU: [0, 10000, 10000, 10000], object_store_memory: [793074573310000], node:10.10.249.4: [10000]}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",} is_draining: 0 is_idle: 0 Cluster resources (at most 20 nodes are shown): node id: 9214086572761971740{"total":{accelerator_type:G: 10000, node:10.10.249.4: 10000, GPU: 40000, memory: 1850507337730000, CPU: 240000, object_store_memory: 793074573310000, node:__internal_head__: 10000}}, "available": {object_store_memory: 793074573310000, node:10.10.249.4: 10000, node:__internal_head__: 10000, memory: 1850507337730000, CPU: 230000, accelerator_type:G: 10000, GPU: 30000}}, "labels":{"ray.io/node_id":"81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d",}, "is_draining": 0, "draining_deadline_timestamp_ms": -1} { "placement group locations": [], "node to bundles": []}
Waiting tasks size: 0
Number of executing tasks: 1
Number of pinned task arguments: 0
Number of total spilled tasks: 0
Number of spilled waiting tasks: 0
Number of spilled unschedulable tasks: 0
Resource usage {
    - (language=PYTHON actor_or_task=ResultCollector.__init__ pid=660927 worker_id=dff56da69688e22696d2d921842b8442257815ea14dcb95e21710385): {}
    - (language=PYTHON actor_or_task=LLMEngine.__init__ pid=660939 worker_id=d7afa1360608e4fb0e56de81a8a10f693a5143265d840f6f7349c3e0): {GPU: 1, CPU: 1}
}
Backlog Size per scheduling descriptor :{workerId: num backlogs}:

Running tasks by scheduling class:
    - {depth=1 function_descriptor={type=PythonFunctionDescriptor, module_name=Ayo.engines.llm, class_name=LLMEngine, function_name=__init__, function_hash=e2f5af0ea8e345998125e6a621a10aec} scheduling_strategy=default_scheduling_strategy {
}
 resource_set={GPU : 1, CPU : 1, }}: 1/24
==================================================

ClusterResources:
LocalObjectManager:
- num pinned objects: 0
- pinned objects size: 0
- num objects pending restore: 0
- num objects pending spill: 0
- num bytes pending spill: 0
- num bytes currently spilled: 0
- cumulative spill requests: 0
- cumulative restore requests: 0
- spilled objects pending delete: 0

ObjectManager:
- num local objects: 0
- num unfulfilled push requests: 0
- num object pull requests: 0
- num chunks received total: 0
- num chunks received failed (all): 0
- num chunks received failed / cancelled: 0
- num chunks received failed / plasma error: 0
Event stats:
Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:
PushManager:
- num pushes in flight: 0
- num chunks in flight: 0
- num chunks remaining: 0
- max chunks allowed: 409
OwnershipBasedObjectDirectory:
- num listeners: 0
- cumulative location updates: 0
- num location updates per second: 0.000
- num location lookups per second: 0.000
- num locations added per second: 0.000
- num locations removed per second: 0.000
BufferPool:
- create buffer state map size: 0
PullManager:
- num bytes available for pulled objects: 79307457331
- num bytes being pulled (all): 0
- num bytes being pulled / pinned: 0
- get request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
- wait request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
- task request bundles: BundlePullRequestQueue{0 total, 0 active, 0 inactive, 0 unpullable}
- first get request bundle: N/A
- first wait request bundle: N/A
- first task request bundle: N/A
- num objects queued: 0
- num objects actively pulled (all): 0
- num objects actively pulled / pinned: 0
- num bundles being pulled: 0
- num pull retries: 0
- max timeout seconds: 0
- max timeout request is already processed. No entry.

WorkerPool:
- registered jobs: 1
- process_failed_job_config_missing: 0
- process_failed_rate_limited: 0
- process_failed_pending_registration: 0
- process_failed_runtime_env_setup_failed: 0
- num PYTHON workers: 24
- num PYTHON drivers: 1
- num PYTHON pending start requests: 0
- num PYTHON pending registration requests: 0
- num object spill callbacks queued: 0
- num object restore queued: 0
- num util functions queued: 0
- num idle workers: 22
TaskDependencyManager:
- task deps map size: 0
- get req map size: 0
- wait req map size: 0
- local objects map size: 0
WaitManager:
- num active wait requests: 0
Subscriber:
Channel WORKER_OBJECT_EVICTION
- cumulative subscribe requests: 0
- cumulative unsubscribe requests: 0
- active subscribed publishers: 0
- cumulative published messages: 0
- cumulative processed messages: 0
Channel WORKER_REF_REMOVED_CHANNEL
- cumulative subscribe requests: 0
- cumulative unsubscribe requests: 0
- active subscribed publishers: 0
- cumulative published messages: 0
- cumulative processed messages: 0
Channel WORKER_OBJECT_LOCATIONS_CHANNEL
- cumulative subscribe requests: 0
- cumulative unsubscribe requests: 0
- active subscribed publishers: 0
- cumulative published messages: 0
- cumulative processed messages: 0
num async plasma notifications: 0
Event stats:
Global stats: 34803 total (40 active)
Queueing time: mean = 87.555 us, max = 625.620 ms, min = -0.000 s, total = 3.047 s
Execution time:  mean = 123.184 us, total = 4.287 s
Event stats:
	NodeManagerService.grpc_server.ReportWorkerBacklog.HandleRequestImpl - 9000 total (0 active), Execution time: mean = 25.748 us, total = 231.736 ms, Queueing time: mean = 40.313 us, max = 659.005 us, min = 4.063 us, total = 362.813 ms
	NodeManagerService.grpc_server.ReportWorkerBacklog - 9000 total (0 active), Execution time: mean = 268.279 us, total = 2.415 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManager.CheckGC - 3598 total (1 active), Execution time: mean = 1.893 us, total = 6.810 ms, Queueing time: mean = 75.002 us, max = 25.832 ms, min = 7.559 us, total = 269.859 ms
	RaySyncer.OnDemandBroadcasting - 3598 total (1 active), Execution time: mean = 6.177 us, total = 22.227 ms, Queueing time: mean = 71.121 us, max = 25.814 ms, min = 3.808 us, total = 255.894 ms
	ObjectManager.UpdateAvailableMemory - 3597 total (0 active), Execution time: mean = 2.850 us, total = 10.250 ms, Queueing time: mean = 19.307 us, max = 627.487 us, min = 2.792 us, total = 69.449 ms
	RayletWorkerPool.deadline_timer.kill_idle_workers - 1800 total (1 active), Execution time: mean = 10.166 us, total = 18.299 ms, Queueing time: mean = 72.367 us, max = 30.900 ms, min = 11.665 us, total = 130.260 ms
	MemoryMonitor.CheckIsMemoryUsageAboveThreshold - 1439 total (1 active), Execution time: mean = 188.851 us, total = 271.756 ms, Queueing time: mean = 55.525 us, max = 427.834 us, min = -0.000 s, total = 79.901 ms
	NodeManager.ScheduleAndDispatchTasks - 361 total (1 active), Execution time: mean = 11.748 us, total = 4.241 ms, Queueing time: mean = 62.737 us, max = 2.069 ms, min = 15.308 us, total = 22.648 ms
	NodeManager.CheckForUnexpectedWorkerDisconnects - 361 total (1 active), Execution time: mean = 47.948 us, total = 17.309 ms, Queueing time: mean = 42.397 us, max = 2.008 ms, min = 6.906 us, total = 15.305 ms
	NodeManagerService.grpc_server.GetResourceLoad.HandleRequestImpl - 360 total (0 active), Execution time: mean = 82.814 us, total = 29.813 ms, Queueing time: mean = 45.000 us, max = 87.484 us, min = 7.863 us, total = 16.200 ms
	NodeManager.deadline_timer.flush_free_objects - 360 total (1 active), Execution time: mean = 7.028 us, total = 2.530 ms, Queueing time: mean = 178.748 us, max = 8.799 ms, min = 15.861 us, total = 64.349 ms
	NodeManagerService.grpc_server.GetResourceLoad - 360 total (0 active), Execution time: mean = 370.528 us, total = 133.390 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManager.deadline_timer.spill_objects_when_over_threshold - 360 total (1 active), Execution time: mean = 2.126 us, total = 765.242 us, Queueing time: mean = 182.667 us, max = 8.796 ms, min = 15.710 us, total = 65.760 ms
	ClusterResourceManager.ResetRemoteNodeView - 121 total (1 active), Execution time: mean = 6.976 us, total = 844.108 us, Queueing time: mean = 56.649 us, max = 101.828 us, min = 21.085 us, total = 6.855 ms
	ClientConnection.async_read.ProcessMessageHeader - 103 total (25 active), Execution time: mean = 2.777 us, total = 286.060 us, Queueing time: mean = 15.460 ms, max = 625.620 ms, min = 12.580 us, total = 1.592 s
	ClientConnection.async_read.ProcessMessage - 78 total (0 active), Execution time: mean = 1.299 ms, total = 101.318 ms, Queueing time: mean = 12.499 us, max = 146.915 us, min = 2.738 us, total = 974.893 us
	NodeManager.deadline_timer.record_metrics - 72 total (1 active), Execution time: mean = 599.239 us, total = 43.145 ms, Queueing time: mean = 321.776 us, max = 8.350 ms, min = 16.353 us, total = 23.168 ms
	NodeManager.deadline_timer.debug_state_dump - 36 total (1 active, 1 running), Execution time: mean = 1.775 ms, total = 63.902 ms, Queueing time: mean = 53.337 us, max = 111.692 us, min = 21.936 us, total = 1.920 ms
	ClientConnection.async_write.DoAsyncWrites - 26 total (0 active), Execution time: mean = 525.308 ns, total = 13.658 us, Queueing time: mean = 16.479 us, max = 51.598 us, min = 8.590 us, total = 428.462 us
	NodeManagerService.grpc_server.GetSystemConfig - 25 total (0 active), Execution time: mean = 361.343 us, total = 9.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ObjectManager.ObjectDeleted - 25 total (0 active), Execution time: mean = 9.360 us, total = 234.005 us, Queueing time: mean = 44.687 us, max = 285.967 us, min = 18.537 us, total = 1.117 ms
	NodeManagerService.grpc_server.GetSystemConfig.HandleRequestImpl - 25 total (0 active), Execution time: mean = 29.747 us, total = 743.681 us, Queueing time: mean = 142.844 us, max = 2.629 ms, min = 7.599 us, total = 3.571 ms
	ObjectManager.ObjectAdded - 25 total (0 active), Execution time: mean = 6.450 us, total = 161.244 us, Queueing time: mean = 54.017 us, max = 364.526 us, min = 8.081 us, total = 1.350 ms
	PeriodicalRunner.RunFnPeriodically - 14 total (0 active), Execution time: mean = 197.858 us, total = 2.770 ms, Queueing time: mean = 3.470 ms, max = 9.264 ms, min = 28.791 us, total = 48.577 ms
	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive.OnReplyReceived - 7 total (0 active), Execution time: mean = 37.670 us, total = 263.687 us, Queueing time: mean = 45.496 us, max = 62.580 us, min = 12.023 us, total = 318.474 us
	ray::rpc::NodeInfoGcsService.grpc_client.CheckAlive - 7 total (0 active), Execution time: mean = 862.844 us, total = 6.040 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManager.GcsCheckAlive - 7 total (1 active), Execution time: mean = 202.417 us, total = 1.417 ms, Queueing time: mean = 1.622 ms, max = 2.004 ms, min = 1.837 ms, total = 11.355 ms
	NodeManager.deadline_timer.print_event_loop_stats - 7 total (1 active), Execution time: mean = 1.759 ms, total = 12.310 ms, Queueing time: mean = 46.157 us, max = 72.733 us, min = 37.284 us, total = 323.099 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 144.096 us, total = 288.193 us, Queueing time: mean = 929.080 us, max = 1.676 ms, min = 182.285 us, total = 1.858 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 428.071 ms, total = 856.142 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_server.RequestWorkerLease.HandleRequestImpl - 2 total (0 active), Execution time: mean = 111.613 us, total = 223.226 us, Queueing time: mean = 31.811 us, max = 50.391 us, min = 13.231 us, total = 63.622 us
	 - 2 total (0 active), Execution time: mean = 772.000 ns, total = 1.544 us, Queueing time: mean = 45.443 us, max = 71.510 us, min = 19.376 us, total = 90.886 us
	WorkerPool.PopWorkerCallback - 2 total (0 active), Execution time: mean = 21.812 us, total = 43.624 us, Queueing time: mean = 9.826 us, max = 13.376 us, min = 6.277 us, total = 19.653 us
	RaySyncerRegister - 2 total (0 active), Execution time: mean = 1.762 us, total = 3.523 us, Queueing time: mean = 147.500 ns, max = 247.000 ns, min = 48.000 ns, total = 295.000 ns
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 1.015 ms, total = 2.030 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	RaySyncer.BroadcastMessage - 2 total (0 active), Execution time: mean = 108.770 us, total = 217.541 us, Queueing time: mean = 200.000 ns, max = 223.000 ns, min = 177.000 ns, total = 400.000 ns
	NodeManagerService.grpc_server.RequestWorkerLease - 2 total (0 active), Execution time: mean = 364.964 us, total = 729.928 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManager.GCTaskFailureReason - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 123.715 us, total = 123.715 us, Queueing time: mean = 8.396 us, max = 8.396 us, min = 8.396 us, total = 8.396 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.025 ms, total = 1.025 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_JOB_CHANNEL - 1 total (0 active), Execution time: mean = 29.045 us, total = 29.045 us, Queueing time: mean = 141.546 us, max = 141.546 us, min = 141.546 us, total = 141.546 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 179.567 us, total = 179.567 us, Queueing time: mean = 9.133 us, max = 9.133 us, min = 9.133 us, total = 9.133 us
	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode - 1 total (0 active), Execution time: mean = 1.374 ms, total = 1.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.RegisterNode.OnReplyReceived - 1 total (0 active), Execution time: mean = 300.343 us, total = 300.343 us, Queueing time: mean = 12.432 us, max = 12.432 us, min = 12.432 us, total = 12.432 us
	ray::rpc::JobInfoGcsService.grpc_client.AddJob - 1 total (0 active), Execution time: mean = 496.434 us, total = 496.434 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig - 1 total (0 active), Execution time: mean = 1.090 ms, total = 1.090 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.569 us, total = 18.569 us, Queueing time: mean = 49.567 us, max = 49.567 us, min = 49.567 us, total = 49.567 us
	ray::rpc::InternalKVGcsService.grpc_client.GetInternalConfig.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.832 ms, total = 15.832 ms, Queueing time: mean = 39.097 us, max = 39.097 us, min = 39.097 us, total = 39.097 us
	ray::rpc::JobInfoGcsService.grpc_client.AddJob.OnReplyReceived - 1 total (0 active), Execution time: mean = 22.314 us, total = 22.314 us, Queueing time: mean = 125.550 us, max = 125.550 us, min = 125.550 us, total = 125.550 us
	ray::rpc::JobInfoGcsService.grpc_client.GetAllJobInfo - 1 total (0 active), Execution time: mean = 879.382 us, total = 879.382 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
DebugString() time ms: 1