[2025-07-05 18:42:59,507 I 660942 660942] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660942
[2025-07-05 18:42:59,508 I 660942 660942] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,510 I 660942 660942] grpc_server.cc:141: worker server started, listening on port 37501.
[2025-07-05 18:42:59,511 I 660942 660942] core_worker.cc:542: Initializing worker at address: ***********:37501 worker_id=3a7e31f237ecda2bdfe0e76840f1d18fe6053a45accc39fc65e55421 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,512 I 660942 660942] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-07-05 18:42:59,513 I 660942 660942] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,513 I 660942 661824] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 6.006 us, max = 69.685 us, min = 5.394 us, total = 90.092 us
Execution time:  mean = 77.286 us, total = 1.159 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 3.242 us, total = 22.695 us, Queueing time: mean = 11.254 us, max = 69.685 us, min = 9.093 us, total = 78.778 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 476.439 us, total = 476.439 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.972 us, total = 113.972 us, Queueing time: mean = 5.394 us, max = 5.394 us, min = 5.394 us, total = 5.394 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.101 us, total = 18.101 us, Queueing time: mean = 5.920 us, max = 5.920 us, min = 5.920 us, total = 5.920 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 528.089 us, total = 528.089 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.856 us, max = 9.912 us, min = 5.513 us, total = 15.425 us
Execution time:  mean = 153.155 us, total = 612.622 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.018 us, total = 19.018 us, Queueing time: mean = 9.912 us, max = 9.912 us, min = 9.912 us, total = 9.912 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 475.325 us, total = 475.325 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 118.279 us, total = 118.279 us, Queueing time: mean = 5.513 us, max = 5.513 us, min = 5.513 us, total = 5.513 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,513 I 660942 660942] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,513 I 660942 660942] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,513 I 660942 660942] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,513 I 660942 661824] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,513 I 660942 661824] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,519 W 660942 661807] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,514 I 660942 661824] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 48.099 us, max = 506.974 us, min = 5.394 us, total = 42.087 ms
Execution time:  mean = 68.192 us, total = 59.668 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.590 us, total = 5.154 ms, Queueing time: mean = 53.180 us, max = 100.396 us, min = 12.270 us, total = 31.908 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 11.371 us, total = 693.610 us, Queueing time: mean = 46.127 us, max = 84.883 us, min = 7.379 us, total = 2.814 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.974 us, total = 1.678 ms, Queueing time: mean = 37.219 us, max = 65.343 us, min = 10.138 us, total = 2.233 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 654.062 us, total = 39.244 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 172.884 us, total = 10.373 ms, Queueing time: mean = 44.270 us, max = 116.682 us, min = 20.868 us, total = 2.656 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 32.282 us, total = 387.384 us, Queueing time: mean = 35.691 us, max = 73.380 us, min = 11.514 us, total = 428.293 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.698 us, total = 515.884 us, Queueing time: mean = 226.023 us, max = 506.974 us, min = 9.093 us, total = 1.582 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.739 us, total = 28.431 us, Queueing time: mean = 28.254 us, max = 79.379 us, min = 17.061 us, total = 169.526 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 476.439 us, total = 476.439 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 59.722 us, total = 59.722 us, Queueing time: mean = 284.588 us, max = 284.588 us, min = 284.588 us, total = 284.588 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.972 us, total = 113.972 us, Queueing time: mean = 5.394 us, max = 5.394 us, min = 5.394 us, total = 5.394 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 396.793 us, total = 396.793 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.101 us, total = 18.101 us, Queueing time: mean = 5.920 us, max = 5.920 us, min = 5.920 us, total = 5.920 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 528.089 us, total = 528.089 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5962 total (1 active)
Queueing time: mean = 56.969 us, max = 977.809 us, min = 2.751 us, total = 339.650 ms
Execution time:  mean = 14.743 us, total = 87.897 ms
Event stats:
	CoreWorker.CheckSignal - 5961 total (1 active), Execution time: mean = 14.744 us, total = 87.890 ms, Queueing time: mean = 56.978 us, max = 977.809 us, min = 9.888 us, total = 339.647 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.654 us, total = 7.654 us, Queueing time: mean = 2.751 us, max = 2.751 us, min = 2.751 us, total = 2.751 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 30.771 us, max = 104.285 us, min = 5.513 us, total = 5.570 ms
Execution time:  mean = 315.870 us, total = 57.172 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 36.563 us, total = 2.194 ms, Queueing time: mean = 43.336 us, max = 70.080 us, min = 9.912 us, total = 2.600 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 204.022 us, total = 12.241 ms, Queueing time: mean = 49.398 us, max = 104.285 us, min = 14.645 us, total = 2.964 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 710.317 us, total = 42.619 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 118.279 us, total = 118.279 us, Queueing time: mean = 5.513 us, max = 5.513 us, min = 5.513 us, total = 5.513 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,514 I 660942 661824] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 46.559 us, max = 506.974 us, min = 5.394 us, total = 80.688 ms
Execution time:  mean = 72.802 us, total = 126.166 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.542 us, total = 10.251 ms, Queueing time: mean = 51.367 us, max = 107.189 us, min = 12.270 us, total = 61.641 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 11.226 us, total = 1.347 ms, Queueing time: mean = 45.606 us, max = 101.656 us, min = 7.379 us, total = 5.473 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 28.901 us, total = 3.468 ms, Queueing time: mean = 37.124 us, max = 66.690 us, min = 10.138 us, total = 4.455 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 714.968 us, total = 85.796 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 180.119 us, total = 21.614 ms, Queueing time: mean = 47.337 us, max = 134.356 us, min = 20.868 us, total = 5.680 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 35.717 us, total = 857.206 us, Queueing time: mean = 37.121 us, max = 73.380 us, min = 11.514 us, total = 890.908 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.758 us, total = 57.090 us, Queueing time: mean = 50.896 us, max = 267.172 us, min = 17.061 us, total = 610.748 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.698 us, total = 515.884 us, Queueing time: mean = 226.023 us, max = 506.974 us, min = 9.093 us, total = 1.582 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 333.214 us, total = 666.428 us, Queueing time: mean = 29.611 us, max = 59.222 us, min = 59.222 us, total = 59.222 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 476.439 us, total = 476.439 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 59.722 us, total = 59.722 us, Queueing time: mean = 284.588 us, max = 284.588 us, min = 284.588 us, total = 284.588 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.972 us, total = 113.972 us, Queueing time: mean = 5.394 us, max = 5.394 us, min = 5.394 us, total = 5.394 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 396.793 us, total = 396.793 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.101 us, total = 18.101 us, Queueing time: mean = 5.920 us, max = 5.920 us, min = 5.920 us, total = 5.920 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 528.089 us, total = 528.089 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11921 total (1 active)
Queueing time: mean = 57.532 us, max = 977.809 us, min = -0.000 s, total = 685.844 ms
Execution time:  mean = 15.025 us, total = 179.111 ms
Event stats:
	CoreWorker.CheckSignal - 11920 total (1 active), Execution time: mean = 15.025 us, total = 179.103 ms, Queueing time: mean = 57.537 us, max = 977.809 us, min = -0.000 s, total = 685.841 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.654 us, total = 7.654 us, Queueing time: mean = 2.751 us, max = 2.751 us, min = 2.751 us, total = 2.751 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 30.370 us, max = 104.285 us, min = 5.513 us, total = 10.964 ms
Execution time:  mean = 316.952 us, total = 114.420 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 36.363 us, total = 4.364 ms, Queueing time: mean = 42.550 us, max = 70.080 us, min = 9.912 us, total = 5.106 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 206.751 us, total = 24.810 ms, Queueing time: mean = 48.768 us, max = 104.285 us, min = 14.645 us, total = 5.852 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 709.397 us, total = 85.128 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 118.279 us, total = 118.279 us, Queueing time: mean = 5.513 us, max = 5.513 us, min = 5.513 us, total = 5.513 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,515 I 660942 661824] core_worker.cc:902: Event stats:


Global stats: 2592 total (8 active)
Queueing time: mean = 46.558 us, max = 749.881 us, min = 3.786 us, total = 120.678 ms
Execution time:  mean = 74.127 us, total = 192.137 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.547 us, total = 15.385 ms, Queueing time: mean = 51.127 us, max = 114.189 us, min = 12.270 us, total = 92.028 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 11.256 us, total = 2.026 ms, Queueing time: mean = 46.810 us, max = 101.656 us, min = 7.379 us, total = 8.426 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 28.811 us, total = 5.186 ms, Queueing time: mean = 36.618 us, max = 66.690 us, min = 10.138 us, total = 6.591 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 732.132 us, total = 131.784 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 181.742 us, total = 32.714 ms, Queueing time: mean = 48.525 us, max = 134.356 us, min = 3.786 us, total = 8.735 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.260 us, total = 1.269 ms, Queueing time: mean = 38.536 us, max = 73.380 us, min = 11.514 us, total = 1.387 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.950 us, total = 89.109 us, Queueing time: mean = 85.755 us, max = 749.881 us, min = 17.061 us, total = 1.544 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.698 us, total = 515.884 us, Queueing time: mean = 226.023 us, max = 506.974 us, min = 9.093 us, total = 1.582 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 524.913 us, total = 1.575 ms, Queueing time: mean = 29.768 us, max = 59.222 us, min = 30.082 us, total = 89.304 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 476.439 us, total = 476.439 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 59.722 us, total = 59.722 us, Queueing time: mean = 284.588 us, max = 284.588 us, min = 284.588 us, total = 284.588 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.972 us, total = 113.972 us, Queueing time: mean = 5.394 us, max = 5.394 us, min = 5.394 us, total = 5.394 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 396.793 us, total = 396.793 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.101 us, total = 18.101 us, Queueing time: mean = 5.920 us, max = 5.920 us, min = 5.920 us, total = 5.920 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 528.089 us, total = 528.089 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17879 total (1 active)
Queueing time: mean = 58.448 us, max = 977.809 us, min = -0.000 s, total = 1.045 s
Execution time:  mean = 15.022 us, total = 268.569 ms
Event stats:
	CoreWorker.CheckSignal - 17878 total (1 active), Execution time: mean = 15.022 us, total = 268.562 ms, Queueing time: mean = 58.451 us, max = 977.809 us, min = -0.000 s, total = 1.045 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.654 us, total = 7.654 us, Queueing time: mean = 2.751 us, max = 2.751 us, min = 2.751 us, total = 2.751 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 32.662 us, max = 104.285 us, min = 5.513 us, total = 17.670 ms
Execution time:  mean = 322.526 us, total = 174.487 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 36.420 us, total = 6.556 ms, Queueing time: mean = 45.578 us, max = 70.080 us, min = 9.912 us, total = 8.204 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 205.871 us, total = 37.057 ms, Queueing time: mean = 52.560 us, max = 104.285 us, min = 14.645 us, total = 9.461 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 726.422 us, total = 130.756 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 118.279 us, total = 118.279 us, Queueing time: mean = 5.513 us, max = 5.513 us, min = 5.513 us, total = 5.513 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,516 I 660942 661824] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 47.285 us, max = 749.881 us, min = -0.000 s, total = 163.135 ms
Execution time:  mean = 74.358 us, total = 256.534 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.646 us, total = 20.742 ms, Queueing time: mean = 51.691 us, max = 136.005 us, min = -0.000 s, total = 124.007 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 11.386 us, total = 2.733 ms, Queueing time: mean = 48.787 us, max = 111.741 us, min = 7.379 us, total = 11.709 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.508 us, total = 7.082 ms, Queueing time: mean = 38.365 us, max = 88.827 us, min = 10.138 us, total = 9.208 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 731.644 us, total = 175.595 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 183.995 us, total = 44.159 ms, Queueing time: mean = 50.124 us, max = 134.356 us, min = 3.786 us, total = 12.030 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 36.199 us, total = 1.738 ms, Queueing time: mean = 39.639 us, max = 74.243 us, min = 11.514 us, total = 1.903 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.526 us, total = 132.620 us, Queueing time: mean = 95.100 us, max = 749.881 us, min = 17.061 us, total = 2.282 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.698 us, total = 515.884 us, Queueing time: mean = 226.023 us, max = 506.974 us, min = 9.093 us, total = 1.582 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 561.435 us, total = 2.246 ms, Queueing time: mean = 29.462 us, max = 59.222 us, min = 28.544 us, total = 117.848 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 476.439 us, total = 476.439 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 59.722 us, total = 59.722 us, Queueing time: mean = 284.588 us, max = 284.588 us, min = 284.588 us, total = 284.588 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.972 us, total = 113.972 us, Queueing time: mean = 5.394 us, max = 5.394 us, min = 5.394 us, total = 5.394 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 396.793 us, total = 396.793 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.101 us, total = 18.101 us, Queueing time: mean = 5.920 us, max = 5.920 us, min = 5.920 us, total = 5.920 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 528.089 us, total = 528.089 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23840 total (1 active)
Queueing time: mean = 57.580 us, max = 977.809 us, min = -0.000 s, total = 1.373 s
Execution time:  mean = 15.058 us, total = 358.981 ms
Event stats:
	CoreWorker.CheckSignal - 23839 total (1 active), Execution time: mean = 15.058 us, total = 358.973 ms, Queueing time: mean = 57.583 us, max = 977.809 us, min = -0.000 s, total = 1.373 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.654 us, total = 7.654 us, Queueing time: mean = 2.751 us, max = 2.751 us, min = 2.751 us, total = 2.751 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 32.898 us, max = 104.285 us, min = 5.513 us, total = 23.719 ms
Execution time:  mean = 323.540 us, total = 233.273 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.336 us, total = 8.721 ms, Queueing time: mean = 43.426 us, max = 70.080 us, min = 9.912 us, total = 10.422 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 205.279 us, total = 49.267 ms, Queueing time: mean = 55.381 us, max = 104.285 us, min = 14.645 us, total = 13.291 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 729.861 us, total = 175.167 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 118.279 us, total = 118.279 us, Queueing time: mean = 5.513 us, max = 5.513 us, min = 5.513 us, total = 5.513 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,517 I 660942 661824] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 46.984 us, max = 749.881 us, min = -0.000 s, total = 202.501 ms
Execution time:  mean = 73.978 us, total = 318.845 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.539 us, total = 25.608 ms, Queueing time: mean = 51.101 us, max = 136.005 us, min = -0.000 s, total = 153.253 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 11.484 us, total = 3.445 ms, Queueing time: mean = 48.847 us, max = 113.542 us, min = 7.379 us, total = 14.654 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.130 us, total = 9.039 ms, Queueing time: mean = 39.714 us, max = 88.827 us, min = 10.138 us, total = 11.914 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 727.220 us, total = 218.166 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 183.917 us, total = 55.175 ms, Queueing time: mean = 49.521 us, max = 134.356 us, min = 3.786 us, total = 14.856 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 36.199 us, total = 2.172 ms, Queueing time: mean = 42.874 us, max = 74.243 us, min = 11.514 us, total = 2.572 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.537 us, total = 166.125 us, Queueing time: mean = 103.285 us, max = 749.881 us, min = 17.061 us, total = 3.099 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.698 us, total = 515.884 us, Queueing time: mean = 226.023 us, max = 506.974 us, min = 9.093 us, total = 1.582 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 586.215 us, total = 2.931 ms, Queueing time: mean = 37.400 us, max = 69.150 us, min = 28.544 us, total = 186.998 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.485 us, total = 32.970 us, Queueing time: mean = 43.498 us, max = 86.996 us, min = 86.996 us, total = 86.996 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 476.439 us, total = 476.439 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 59.722 us, total = 59.722 us, Queueing time: mean = 284.588 us, max = 284.588 us, min = 284.588 us, total = 284.588 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.972 us, total = 113.972 us, Queueing time: mean = 5.394 us, max = 5.394 us, min = 5.394 us, total = 5.394 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 396.793 us, total = 396.793 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.101 us, total = 18.101 us, Queueing time: mean = 5.920 us, max = 5.920 us, min = 5.920 us, total = 5.920 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 528.089 us, total = 528.089 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29802 total (1 active)
Queueing time: mean = 56.906 us, max = 977.809 us, min = -0.000 s, total = 1.696 s
Execution time:  mean = 15.092 us, total = 449.780 ms
Event stats:
	CoreWorker.CheckSignal - 29801 total (1 active), Execution time: mean = 15.093 us, total = 449.773 ms, Queueing time: mean = 56.908 us, max = 977.809 us, min = -0.000 s, total = 1.696 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.654 us, total = 7.654 us, Queueing time: mean = 2.751 us, max = 2.751 us, min = 2.751 us, total = 2.751 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 33.440 us, max = 117.889 us, min = 5.513 us, total = 30.129 ms
Execution time:  mean = 325.816 us, total = 293.560 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.198 us, total = 10.859 ms, Queueing time: mean = 42.409 us, max = 70.080 us, min = 9.912 us, total = 12.723 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 204.506 us, total = 61.352 ms, Queueing time: mean = 58.004 us, max = 117.889 us, min = 14.645 us, total = 17.401 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 737.437 us, total = 221.231 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 118.279 us, total = 118.279 us, Queueing time: mean = 5.513 us, max = 5.513 us, min = 5.513 us, total = 5.513 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,517 I 660942 661824] core_worker.cc:902: Event stats:


Global stats: 5169 total (8 active)
Queueing time: mean = 47.987 us, max = 749.881 us, min = -0.000 s, total = 248.043 ms
Execution time:  mean = 74.443 us, total = 384.797 ms
Event stats:
	CoreWorker.RecoverObjects - 3599 total (1 active), Execution time: mean = 8.543 us, total = 30.747 ms, Queueing time: mean = 52.057 us, max = 136.005 us, min = -0.000 s, total = 187.354 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.512 us, total = 4.144 ms, Queueing time: mean = 50.520 us, max = 113.542 us, min = 7.379 us, total = 18.187 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.349 us, total = 10.926 ms, Queueing time: mean = 41.549 us, max = 88.827 us, min = 10.138 us, total = 14.958 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 734.615 us, total = 264.462 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 183.308 us, total = 65.991 ms, Queueing time: mean = 50.925 us, max = 134.356 us, min = 3.786 us, total = 18.333 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 36.086 us, total = 2.598 ms, Queueing time: mean = 44.333 us, max = 74.733 us, min = 11.514 us, total = 3.192 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.477 us, total = 197.156 us, Queueing time: mean = 105.499 us, max = 749.881 us, min = 17.061 us, total = 3.798 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.698 us, total = 515.884 us, Queueing time: mean = 226.023 us, max = 506.974 us, min = 9.093 us, total = 1.582 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 598.350 us, total = 3.590 ms, Queueing time: mean = 42.735 us, max = 69.414 us, min = 28.544 us, total = 256.412 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.485 us, total = 32.970 us, Queueing time: mean = 43.498 us, max = 86.996 us, min = 86.996 us, total = 86.996 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 476.439 us, total = 476.439 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 59.722 us, total = 59.722 us, Queueing time: mean = 284.588 us, max = 284.588 us, min = 284.588 us, total = 284.588 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 113.972 us, total = 113.972 us, Queueing time: mean = 5.394 us, max = 5.394 us, min = 5.394 us, total = 5.394 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 396.793 us, total = 396.793 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.101 us, total = 18.101 us, Queueing time: mean = 5.920 us, max = 5.920 us, min = 5.920 us, total = 5.920 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 528.089 us, total = 528.089 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35763 total (1 active)
Queueing time: mean = 56.679 us, max = 1.706 ms, min = -0.000 s, total = 2.027 s
Execution time:  mean = 15.064 us, total = 538.726 ms
Event stats:
	CoreWorker.CheckSignal - 35762 total (1 active), Execution time: mean = 15.064 us, total = 538.719 ms, Queueing time: mean = 56.680 us, max = 1.706 ms, min = -0.000 s, total = 2.027 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.654 us, total = 7.654 us, Queueing time: mean = 2.751 us, max = 2.751 us, min = 2.751 us, total = 2.751 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 33.782 us, max = 117.889 us, min = 5.513 us, total = 36.518 ms
Execution time:  mean = 328.928 us, total = 355.571 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.362 us, total = 13.090 ms, Queueing time: mean = 42.863 us, max = 85.140 us, min = 9.912 us, total = 15.431 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 204.525 us, total = 73.629 ms, Queueing time: mean = 58.560 us, max = 117.889 us, min = 14.645 us, total = 21.082 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 746.481 us, total = 268.733 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 118.279 us, total = 118.279 us, Queueing time: mean = 5.513 us, max = 5.513 us, min = 5.513 us, total = 5.513 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660942 661824] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660942 661824] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660942 661824] core_worker.cc:5107: Number of alive nodes:0
