[2025-07-05 18:42:59,721 I 660946 660946] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660946
[2025-07-05 18:42:59,722 I 660946 660946] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,724 I 660946 660946] grpc_server.cc:141: worker server started, listening on port 38509.
[2025-07-05 18:42:59,725 I 660946 660946] core_worker.cc:542: Initializing worker at address: ***********:38509 worker_id=99db126a027fe7e7981eb1f032d53609f734078bec9895ceb72ac50d node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,726 I 660946 660946] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,727 I 660946 660946] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,727 I 660946 660946] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,727 I 660946 662272] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 6.426 us, max = 74.539 us, min = 6.047 us, total = 96.397 us
Execution time:  mean = 58.929 us, total = 883.940 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 5.295 us, total = 37.063 us, Queueing time: mean = 11.568 us, max = 74.539 us, min = 6.434 us, total = 80.973 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 351.234 us, total = 351.234 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 111.238 us, total = 111.238 us, Queueing time: mean = 6.047 us, max = 6.047 us, min = 6.047 us, total = 6.047 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 353.133 us, total = 353.133 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.272 us, total = 31.272 us, Queueing time: mean = 9.377 us, max = 9.377 us, min = 9.377 us, total = 9.377 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 9.642 us, max = 31.049 us, min = 7.520 us, total = 38.569 us
Execution time:  mean = 143.072 us, total = 572.289 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 457.099 us, total = 457.099 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.898 us, total = 98.898 us, Queueing time: mean = 7.520 us, max = 7.520 us, min = 7.520 us, total = 7.520 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.292 us, total = 16.292 us, Queueing time: mean = 31.049 us, max = 31.049 us, min = 31.049 us, total = 31.049 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,727 I 660946 660946] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,727 I 660946 660946] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,727 I 660946 662272] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,727 I 660946 662272] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,733 W 660946 662267] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,728 I 660946 662272] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 47.262 us, max = 480.243 us, min = 6.047 us, total = 41.354 ms
Execution time:  mean = 72.491 us, total = 63.430 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.344 us, total = 5.007 ms, Queueing time: mean = 50.313 us, max = 168.619 us, min = 12.907 us, total = 30.188 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.055 us, total = 613.381 us, Queueing time: mean = 53.633 us, max = 89.688 us, min = 18.997 us, total = 3.272 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 174.728 us, total = 10.484 ms, Queueing time: mean = 48.882 us, max = 112.505 us, min = 15.438 us, total = 2.933 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 725.804 us, total = 43.548 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 27.459 us, total = 1.648 ms, Queueing time: mean = 40.347 us, max = 67.197 us, min = 9.534 us, total = 2.421 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 31.744 us, total = 380.931 us, Queueing time: mean = 34.988 us, max = 119.853 us, min = 14.176 us, total = 419.856 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.608 us, total = 522.253 us, Queueing time: mean = 211.819 us, max = 480.243 us, min = 6.434 us, total = 1.483 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.109 us, total = 30.652 us, Queueing time: mean = 38.560 us, max = 74.034 us, min = 36.861 us, total = 231.363 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 351.234 us, total = 351.234 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 62.552 us, total = 62.552 us, Queueing time: mean = 391.455 us, max = 391.455 us, min = 391.455 us, total = 391.455 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 353.133 us, total = 353.133 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 111.238 us, total = 111.238 us, Queueing time: mean = 6.047 us, max = 6.047 us, min = 6.047 us, total = 6.047 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 286.952 us, total = 286.952 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.272 us, total = 31.272 us, Queueing time: mean = 9.377 us, max = 9.377 us, min = 9.377 us, total = 9.377 us

-----------------
Task execution event stats:

Global stats: 5963 total (1 active)
Queueing time: mean = 54.474 us, max = 5.977 ms, min = 2.709 us, total = 324.827 ms
Execution time:  mean = 14.848 us, total = 88.537 ms
Event stats:
	CoreWorker.CheckSignal - 5962 total (1 active), Execution time: mean = 14.849 us, total = 88.529 ms, Queueing time: mean = 54.482 us, max = 5.977 ms, min = 8.336 us, total = 324.824 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.916 us, total = 7.916 us, Queueing time: mean = 2.709 us, max = 2.709 us, min = 2.709 us, total = 2.709 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 30.389 us, max = 90.096 us, min = 7.520 us, total = 5.500 ms
Execution time:  mean = 329.600 us, total = 59.658 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 202.931 us, total = 12.176 ms, Queueing time: mean = 53.485 us, max = 90.096 us, min = 25.945 us, total = 3.209 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 755.292 us, total = 45.318 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 34.421 us, total = 2.065 ms, Queueing time: mean = 38.062 us, max = 58.301 us, min = 11.137 us, total = 2.284 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.898 us, total = 98.898 us, Queueing time: mean = 7.520 us, max = 7.520 us, min = 7.520 us, total = 7.520 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,728 I 660946 662272] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 46.767 us, max = 480.243 us, min = 6.047 us, total = 81.046 ms
Execution time:  mean = 75.123 us, total = 130.187 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.611 us, total = 10.334 ms, Queueing time: mean = 50.349 us, max = 168.619 us, min = 12.907 us, total = 60.419 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 182.123 us, total = 21.855 ms, Queueing time: mean = 49.898 us, max = 122.296 us, min = 15.438 us, total = 5.988 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.330 us, total = 1.240 ms, Queueing time: mean = 51.180 us, max = 101.375 us, min = 15.370 us, total = 6.142 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 748.046 us, total = 89.766 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.799 us, total = 3.576 ms, Queueing time: mean = 42.412 us, max = 71.657 us, min = 9.534 us, total = 5.089 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 39.599 us, total = 950.386 us, Queueing time: mean = 36.752 us, max = 119.853 us, min = 13.559 us, total = 882.060 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.494 us, total = 65.930 us, Queueing time: mean = 51.544 us, max = 190.111 us, min = 17.631 us, total = 618.527 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.608 us, total = 522.253 us, Queueing time: mean = 211.819 us, max = 480.243 us, min = 6.434 us, total = 1.483 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 341.550 us, total = 683.101 us, Queueing time: mean = 9.178 us, max = 18.355 us, min = 18.355 us, total = 18.355 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 111.238 us, total = 111.238 us, Queueing time: mean = 6.047 us, max = 6.047 us, min = 6.047 us, total = 6.047 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 351.234 us, total = 351.234 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 62.552 us, total = 62.552 us, Queueing time: mean = 391.455 us, max = 391.455 us, min = 391.455 us, total = 391.455 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 353.133 us, total = 353.133 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 286.952 us, total = 286.952 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.272 us, total = 31.272 us, Queueing time: mean = 9.377 us, max = 9.377 us, min = 9.377 us, total = 9.377 us

-----------------
Task execution event stats:

Global stats: 11923 total (1 active)
Queueing time: mean = 55.612 us, max = 5.977 ms, min = -0.000 s, total = 663.062 ms
Execution time:  mean = 15.026 us, total = 179.155 ms
Event stats:
	CoreWorker.CheckSignal - 11922 total (1 active), Execution time: mean = 15.027 us, total = 179.148 ms, Queueing time: mean = 55.616 us, max = 5.977 ms, min = -0.000 s, total = 663.059 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.916 us, total = 7.916 us, Queueing time: mean = 2.709 us, max = 2.709 us, min = 2.709 us, total = 2.709 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 30.407 us, max = 102.579 us, min = 7.520 us, total = 10.977 ms
Execution time:  mean = 334.136 us, total = 120.623 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 202.921 us, total = 24.350 ms, Queueing time: mean = 53.044 us, max = 102.579 us, min = 23.781 us, total = 6.365 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 766.567 us, total = 91.988 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.880 us, total = 4.186 ms, Queueing time: mean = 38.367 us, max = 63.707 us, min = 11.137 us, total = 4.604 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.898 us, total = 98.898 us, Queueing time: mean = 7.520 us, max = 7.520 us, min = 7.520 us, total = 7.520 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,729 I 660946 662272] core_worker.cc:902: Event stats:


Global stats: 2592 total (8 active)
Queueing time: mean = 46.699 us, max = 687.994 us, min = 6.047 us, total = 121.044 ms
Execution time:  mean = 75.452 us, total = 195.571 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.567 us, total = 15.421 ms, Queueing time: mean = 50.274 us, max = 168.619 us, min = 12.158 us, total = 90.494 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 181.229 us, total = 32.621 ms, Queueing time: mean = 48.129 us, max = 122.296 us, min = 15.438 us, total = 8.663 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.556 us, total = 1.900 ms, Queueing time: mean = 51.846 us, max = 101.375 us, min = 15.370 us, total = 9.332 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 752.612 us, total = 135.470 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.796 us, total = 5.363 ms, Queueing time: mean = 42.053 us, max = 95.477 us, min = 9.534 us, total = 7.569 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 39.776 us, total = 1.432 ms, Queueing time: mean = 41.147 us, max = 119.853 us, min = 13.559 us, total = 1.481 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.499 us, total = 98.986 us, Queueing time: mean = 86.515 us, max = 687.994 us, min = 17.631 us, total = 1.557 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.608 us, total = 522.253 us, Queueing time: mean = 211.819 us, max = 480.243 us, min = 6.434 us, total = 1.483 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 515.390 us, total = 1.546 ms, Queueing time: mean = 19.160 us, max = 39.125 us, min = 18.355 us, total = 57.480 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 111.238 us, total = 111.238 us, Queueing time: mean = 6.047 us, max = 6.047 us, min = 6.047 us, total = 6.047 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 351.234 us, total = 351.234 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 62.552 us, total = 62.552 us, Queueing time: mean = 391.455 us, max = 391.455 us, min = 391.455 us, total = 391.455 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 353.133 us, total = 353.133 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 286.952 us, total = 286.952 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.272 us, total = 31.272 us, Queueing time: mean = 9.377 us, max = 9.377 us, min = 9.377 us, total = 9.377 us

-----------------
Task execution event stats:

Global stats: 17883 total (1 active)
Queueing time: mean = 56.042 us, max = 5.977 ms, min = -0.000 s, total = 1.002 s
Execution time:  mean = 15.020 us, total = 268.595 ms
Event stats:
	CoreWorker.CheckSignal - 17882 total (1 active), Execution time: mean = 15.020 us, total = 268.587 ms, Queueing time: mean = 56.045 us, max = 5.977 ms, min = -0.000 s, total = 1.002 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.916 us, total = 7.916 us, Queueing time: mean = 2.709 us, max = 2.709 us, min = 2.709 us, total = 2.709 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 31.320 us, max = 102.579 us, min = 7.520 us, total = 16.944 ms
Execution time:  mean = 330.335 us, total = 178.711 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 201.252 us, total = 36.225 ms, Queueing time: mean = 55.147 us, max = 102.579 us, min = 22.593 us, total = 9.927 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 756.139 us, total = 136.105 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 34.901 us, total = 6.282 ms, Queueing time: mean = 38.944 us, max = 63.707 us, min = 11.137 us, total = 7.010 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.898 us, total = 98.898 us, Queueing time: mean = 7.520 us, max = 7.520 us, min = 7.520 us, total = 7.520 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,730 I 660946 662272] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 47.299 us, max = 687.994 us, min = 6.047 us, total = 163.182 ms
Execution time:  mean = 76.054 us, total = 262.387 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.592 us, total = 20.612 ms, Queueing time: mean = 50.979 us, max = 168.619 us, min = 12.158 us, total = 122.299 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 182.690 us, total = 43.846 ms, Queueing time: mean = 48.951 us, max = 124.971 us, min = 15.438 us, total = 11.748 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.985 us, total = 2.636 ms, Queueing time: mean = 52.664 us, max = 140.469 us, min = 15.370 us, total = 12.639 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 758.502 us, total = 182.040 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 30.337 us, total = 7.281 ms, Queueing time: mean = 42.446 us, max = 107.560 us, min = 9.534 us, total = 10.187 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 38.778 us, total = 1.861 ms, Queueing time: mean = 42.422 us, max = 119.853 us, min = 13.559 us, total = 2.036 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 6.825 us, total = 163.807 us, Queueing time: mean = 94.049 us, max = 687.994 us, min = 17.631 us, total = 2.257 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.608 us, total = 522.253 us, Queueing time: mean = 211.819 us, max = 480.243 us, min = 6.434 us, total = 1.483 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 557.119 us, total = 2.228 ms, Queueing time: mean = 31.371 us, max = 68.005 us, min = 18.355 us, total = 125.485 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 111.238 us, total = 111.238 us, Queueing time: mean = 6.047 us, max = 6.047 us, min = 6.047 us, total = 6.047 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 351.234 us, total = 351.234 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 62.552 us, total = 62.552 us, Queueing time: mean = 391.455 us, max = 391.455 us, min = 391.455 us, total = 391.455 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 353.133 us, total = 353.133 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 286.952 us, total = 286.952 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.272 us, total = 31.272 us, Queueing time: mean = 9.377 us, max = 9.377 us, min = 9.377 us, total = 9.377 us

-----------------
Task execution event stats:

Global stats: 23842 total (1 active)
Queueing time: mean = 56.828 us, max = 5.977 ms, min = -0.000 s, total = 1.355 s
Execution time:  mean = 15.009 us, total = 357.838 ms
Event stats:
	CoreWorker.CheckSignal - 23841 total (1 active), Execution time: mean = 15.009 us, total = 357.830 ms, Queueing time: mean = 56.830 us, max = 5.977 ms, min = -0.000 s, total = 1.355 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.916 us, total = 7.916 us, Queueing time: mean = 2.709 us, max = 2.709 us, min = 2.709 us, total = 2.709 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 31.890 us, max = 102.579 us, min = 7.520 us, total = 22.992 ms
Execution time:  mean = 331.185 us, total = 238.784 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 199.956 us, total = 47.989 ms, Queueing time: mean = 57.085 us, max = 102.579 us, min = 22.593 us, total = 13.701 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 759.286 us, total = 182.229 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 35.281 us, total = 8.467 ms, Queueing time: mean = 38.685 us, max = 67.307 us, min = 11.137 us, total = 9.284 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.898 us, total = 98.898 us, Queueing time: mean = 7.520 us, max = 7.520 us, min = 7.520 us, total = 7.520 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,731 I 660946 662272] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 47.708 us, max = 687.994 us, min = 6.047 us, total = 205.622 ms
Execution time:  mean = 76.048 us, total = 327.767 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.539 us, total = 25.607 ms, Queueing time: mean = 51.389 us, max = 168.619 us, min = 12.158 us, total = 154.115 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 182.809 us, total = 54.843 ms, Queueing time: mean = 49.589 us, max = 124.971 us, min = 15.438 us, total = 14.877 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.918 us, total = 3.275 ms, Queueing time: mean = 52.152 us, max = 140.469 us, min = 15.370 us, total = 15.646 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 758.798 us, total = 227.639 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.556 us, total = 9.167 ms, Queueing time: mean = 43.577 us, max = 107.560 us, min = 9.534 us, total = 13.073 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 39.070 us, total = 2.344 ms, Queueing time: mean = 45.841 us, max = 119.853 us, min = 13.559 us, total = 2.750 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 6.542 us, total = 196.265 us, Queueing time: mean = 100.390 us, max = 687.994 us, min = 17.631 us, total = 3.012 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.608 us, total = 522.253 us, Queueing time: mean = 211.819 us, max = 480.243 us, min = 6.434 us, total = 1.483 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 589.095 us, total = 2.945 ms, Queueing time: mean = 38.711 us, max = 68.068 us, min = 18.355 us, total = 193.553 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 15.659 us, total = 31.317 us, Queueing time: mean = 33.184 us, max = 66.368 us, min = 66.368 us, total = 66.368 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 111.238 us, total = 111.238 us, Queueing time: mean = 6.047 us, max = 6.047 us, min = 6.047 us, total = 6.047 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 351.234 us, total = 351.234 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 62.552 us, total = 62.552 us, Queueing time: mean = 391.455 us, max = 391.455 us, min = 391.455 us, total = 391.455 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 353.133 us, total = 353.133 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 286.952 us, total = 286.952 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.272 us, total = 31.272 us, Queueing time: mean = 9.377 us, max = 9.377 us, min = 9.377 us, total = 9.377 us

-----------------
Task execution event stats:

Global stats: 29802 total (1 active)
Queueing time: mean = 56.833 us, max = 5.977 ms, min = -0.000 s, total = 1.694 s
Execution time:  mean = 15.020 us, total = 447.615 ms
Event stats:
	CoreWorker.CheckSignal - 29801 total (1 active), Execution time: mean = 15.020 us, total = 447.608 ms, Queueing time: mean = 56.834 us, max = 5.977 ms, min = -0.000 s, total = 1.694 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.916 us, total = 7.916 us, Queueing time: mean = 2.709 us, max = 2.709 us, min = 2.709 us, total = 2.709 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 32.590 us, max = 118.944 us, min = 7.520 us, total = 29.364 ms
Execution time:  mean = 334.552 us, total = 301.431 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 201.570 us, total = 60.471 ms, Queueing time: mean = 57.241 us, max = 118.944 us, min = 22.593 us, total = 17.172 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 766.703 us, total = 230.011 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.166 us, total = 10.850 ms, Queueing time: mean = 40.614 us, max = 97.534 us, min = 11.137 us, total = 12.184 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.898 us, total = 98.898 us, Queueing time: mean = 7.520 us, max = 7.520 us, min = 7.520 us, total = 7.520 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,731 I 660946 662272] core_worker.cc:902: Event stats:


Global stats: 5169 total (8 active)
Queueing time: mean = 48.697 us, max = 687.994 us, min = 3.745 us, total = 251.716 ms
Execution time:  mean = 75.710 us, total = 391.346 ms
Event stats:
	CoreWorker.RecoverObjects - 3599 total (1 active), Execution time: mean = 8.579 us, total = 30.878 ms, Queueing time: mean = 52.567 us, max = 210.976 us, min = 3.745 us, total = 189.190 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 181.922 us, total = 65.492 ms, Queueing time: mean = 50.803 us, max = 127.928 us, min = 15.438 us, total = 18.289 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.936 us, total = 3.937 ms, Queueing time: mean = 52.642 us, max = 140.469 us, min = 15.370 us, total = 18.951 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 754.534 us, total = 271.632 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.629 us, total = 11.026 ms, Queueing time: mean = 43.646 us, max = 107.560 us, min = 9.534 us, total = 15.713 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 38.739 us, total = 2.789 ms, Queueing time: mean = 48.707 us, max = 119.853 us, min = 13.559 us, total = 3.507 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 6.397 us, total = 230.286 us, Queueing time: mean = 106.823 us, max = 687.994 us, min = 17.631 us, total = 3.846 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.608 us, total = 522.253 us, Queueing time: mean = 211.819 us, max = 480.243 us, min = 6.434 us, total = 1.483 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 602.010 us, total = 3.612 ms, Queueing time: mean = 44.107 us, max = 71.091 us, min = 18.355 us, total = 264.644 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 15.659 us, total = 31.317 us, Queueing time: mean = 33.184 us, max = 66.368 us, min = 66.368 us, total = 66.368 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 111.238 us, total = 111.238 us, Queueing time: mean = 6.047 us, max = 6.047 us, min = 6.047 us, total = 6.047 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 351.234 us, total = 351.234 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 62.552 us, total = 62.552 us, Queueing time: mean = 391.455 us, max = 391.455 us, min = 391.455 us, total = 391.455 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 353.133 us, total = 353.133 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 286.952 us, total = 286.952 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.272 us, total = 31.272 us, Queueing time: mean = 9.377 us, max = 9.377 us, min = 9.377 us, total = 9.377 us

-----------------
Task execution event stats:

Global stats: 35763 total (1 active)
Queueing time: mean = 56.564 us, max = 8.533 ms, min = -0.000 s, total = 2.023 s
Execution time:  mean = 15.080 us, total = 539.311 ms
Event stats:
	CoreWorker.CheckSignal - 35762 total (1 active), Execution time: mean = 15.080 us, total = 539.303 ms, Queueing time: mean = 56.565 us, max = 8.533 ms, min = -0.000 s, total = 2.023 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.916 us, total = 7.916 us, Queueing time: mean = 2.709 us, max = 2.709 us, min = 2.709 us, total = 2.709 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 33.273 us, max = 118.944 us, min = 7.520 us, total = 35.968 ms
Execution time:  mean = 334.273 us, total = 361.349 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 200.398 us, total = 72.143 ms, Queueing time: mean = 58.253 us, max = 118.944 us, min = 22.300 us, total = 20.971 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 766.864 us, total = 276.071 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.212 us, total = 13.036 ms, Queueing time: mean = 41.637 us, max = 97.534 us, min = 11.137 us, total = 14.989 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 98.898 us, total = 98.898 us, Queueing time: mean = 7.520 us, max = 7.520 us, min = 7.520 us, total = 7.520 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,785 I 660946 662272] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660946 662272] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660946 662272] core_worker.cc:5107: Number of alive nodes:0
[2025-07-05 18:49:08,878 I 660946 662272] raylet_client.cc:281: Error reporting task backlog information: RpcError: RPC Error message: Cancelling all calls; RPC Error details:  rpc_code: 14
