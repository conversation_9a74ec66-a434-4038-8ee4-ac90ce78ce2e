[2025-07-05 18:42:59,486 I 660943 660943] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660943
[2025-07-05 18:42:59,487 I 660943 660943] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,489 I 660943 660943] grpc_server.cc:141: worker server started, listening on port 43737.
[2025-07-05 18:42:59,491 I 660943 660943] core_worker.cc:542: Initializing worker at address: ***********:43737 worker_id=9053b868b403cc8f79dcd08aa4ed4270b6d436a13d63c9f38d5e329f node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,492 I 660943 660943] task_event_buffer.cc:287: Reporting task events to <PERSON><PERSON> every 1000ms.
[2025-07-05 18:42:59,492 I 660943 661621] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,492 I 660943 661621] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,493 I 660943 660943] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,493 I 660943 661621] core_worker.cc:902: Event stats:


Global stats: 12 total (4 active)
Queueing time: mean = 10.165 us, max = 75.495 us, min = 8.509 us, total = 121.980 us
Execution time:  mean = 121.949 us, total = 1.463 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 8.772 us, total = 26.317 us, Queueing time: mean = 28.254 us, max = 75.495 us, min = 9.268 us, total = 84.763 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 109.553 us, total = 109.553 us, Queueing time: mean = 11.208 us, max = 11.208 us, min = 11.208 us, total = 11.208 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.811 us, total = 16.811 us, Queueing time: mean = 8.509 us, max = 8.509 us, min = 8.509 us, total = 8.509 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.374 us, total = 309.374 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 392.337 us, total = 392.337 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 56.990 us, total = 56.990 us, Queueing time: mean = 17.500 us, max = 17.500 us, min = 17.500 us, total = 17.500 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 552.010 us, total = 552.010 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 3.810 us, max = 8.129 us, min = 7.111 us, total = 15.240 us
Execution time:  mean = 166.031 us, total = 664.123 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.934 us, total = 19.934 us, Queueing time: mean = 8.129 us, max = 8.129 us, min = 8.129 us, total = 8.129 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 124.726 us, total = 124.726 us, Queueing time: mean = 7.111 us, max = 7.111 us, min = 7.111 us, total = 7.111 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 519.463 us, total = 519.463 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,493 I 660943 660943] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,493 I 660943 660943] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,493 I 660943 660943] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,498 W 660943 661602] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,494 I 660943 661621] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 47.763 us, max = 587.392 us, min = 7.708 us, total = 41.793 ms
Execution time:  mean = 70.813 us, total = 61.961 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.962 us, total = 5.377 ms, Queueing time: mean = 53.897 us, max = 587.392 us, min = 14.066 us, total = 32.338 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.089 us, total = 615.439 us, Queueing time: mean = 45.391 us, max = 76.807 us, min = 16.603 us, total = 2.769 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 701.353 us, total = 42.081 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 26.528 us, total = 1.592 ms, Queueing time: mean = 34.356 us, max = 63.807 us, min = 8.700 us, total = 2.061 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 165.631 us, total = 9.938 ms, Queueing time: mean = 41.317 us, max = 70.324 us, min = 9.110 us, total = 2.479 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 30.567 us, total = 366.801 us, Queueing time: mean = 29.704 us, max = 72.433 us, min = 14.086 us, total = 356.445 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.418 us, total = 520.923 us, Queueing time: mean = 212.571 us, max = 486.981 us, min = 7.708 us, total = 1.488 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.552 us, total = 33.314 us, Queueing time: mean = 43.927 us, max = 70.226 us, min = 29.725 us, total = 263.563 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 109.553 us, total = 109.553 us, Queueing time: mean = 11.208 us, max = 11.208 us, min = 11.208 us, total = 11.208 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.374 us, total = 309.374 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.811 us, total = 16.811 us, Queueing time: mean = 8.509 us, max = 8.509 us, min = 8.509 us, total = 8.509 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 56.990 us, total = 56.990 us, Queueing time: mean = 17.500 us, max = 17.500 us, min = 17.500 us, total = 17.500 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 552.010 us, total = 552.010 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 392.337 us, total = 392.337 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5961 total (1 active)
Queueing time: mean = 58.602 us, max = 1.079 ms, min = 2.746 us, total = 349.328 ms
Execution time:  mean = 14.934 us, total = 89.019 ms
Event stats:
	CoreWorker.CheckSignal - 5960 total (1 active), Execution time: mean = 14.935 us, total = 89.011 ms, Queueing time: mean = 58.612 us, max = 1.079 ms, min = 8.729 us, total = 349.325 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.924 us, total = 7.924 us, Queueing time: mean = 2.746 us, max = 2.746 us, min = 2.746 us, total = 2.746 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 28.647 us, max = 103.640 us, min = 7.111 us, total = 5.185 ms
Execution time:  mean = 324.695 us, total = 58.770 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 36.443 us, total = 2.187 ms, Queueing time: mean = 38.398 us, max = 65.773 us, min = 8.129 us, total = 2.304 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 202.718 us, total = 12.163 ms, Queueing time: mean = 47.900 us, max = 103.640 us, min = 19.016 us, total = 2.874 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 738.256 us, total = 44.295 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 124.726 us, total = 124.726 us, Queueing time: mean = 7.111 us, max = 7.111 us, min = 7.111 us, total = 7.111 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,494 I 660943 661621] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 49.512 us, max = 587.392 us, min = 7.708 us, total = 85.805 ms
Execution time:  mean = 72.638 us, total = 125.882 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.803 us, total = 10.564 ms, Queueing time: mean = 55.390 us, max = 587.392 us, min = 14.066 us, total = 66.468 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 719.857 us, total = 86.383 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 27.995 us, total = 3.359 ms, Queueing time: mean = 37.647 us, max = 67.834 us, min = 8.700 us, total = 4.518 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.633 us, total = 1.276 ms, Queueing time: mean = 50.498 us, max = 82.658 us, min = 16.603 us, total = 6.060 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 173.326 us, total = 20.799 ms, Queueing time: mean = 47.535 us, max = 86.876 us, min = 9.110 us, total = 5.704 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 32.568 us, total = 781.637 us, Queueing time: mean = 30.047 us, max = 72.433 us, min = 12.183 us, total = 721.137 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.148 us, total = 61.775 us, Queueing time: mean = 61.599 us, max = 212.787 us, min = 28.834 us, total = 739.185 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.418 us, total = 520.923 us, Queueing time: mean = 212.571 us, max = 486.981 us, min = 7.708 us, total = 1.488 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 349.930 us, total = 699.861 us, Queueing time: mean = 34.810 us, max = 69.620 us, min = 69.620 us, total = 69.620 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 109.553 us, total = 109.553 us, Queueing time: mean = 11.208 us, max = 11.208 us, min = 11.208 us, total = 11.208 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.374 us, total = 309.374 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.811 us, total = 16.811 us, Queueing time: mean = 8.509 us, max = 8.509 us, min = 8.509 us, total = 8.509 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 56.990 us, total = 56.990 us, Queueing time: mean = 17.500 us, max = 17.500 us, min = 17.500 us, total = 17.500 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 552.010 us, total = 552.010 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 392.337 us, total = 392.337 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11920 total (1 active)
Queueing time: mean = 58.117 us, max = 1.079 ms, min = 2.746 us, total = 692.752 ms
Execution time:  mean = 15.135 us, total = 180.408 ms
Event stats:
	CoreWorker.CheckSignal - 11919 total (1 active), Execution time: mean = 15.135 us, total = 180.400 ms, Queueing time: mean = 58.121 us, max = 1.079 ms, min = 8.729 us, total = 692.749 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.924 us, total = 7.924 us, Queueing time: mean = 2.746 us, max = 2.746 us, min = 2.746 us, total = 2.746 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 28.104 us, max = 103.640 us, min = 7.111 us, total = 10.146 ms
Execution time:  mean = 326.358 us, total = 117.815 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 35.902 us, total = 4.308 ms, Queueing time: mean = 39.218 us, max = 65.773 us, min = 8.129 us, total = 4.706 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 202.635 us, total = 24.316 ms, Queueing time: mean = 45.269 us, max = 103.640 us, min = 19.016 us, total = 5.432 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 742.218 us, total = 89.066 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 124.726 us, total = 124.726 us, Queueing time: mean = 7.111 us, max = 7.111 us, min = 7.111 us, total = 7.111 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,495 I 660943 661621] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 49.217 us, max = 745.468 us, min = 7.708 us, total = 127.522 ms
Execution time:  mean = 73.701 us, total = 190.959 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.735 us, total = 15.715 ms, Queueing time: mean = 54.857 us, max = 587.392 us, min = 14.066 us, total = 98.687 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 731.081 us, total = 131.595 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 28.501 us, total = 5.130 ms, Queueing time: mean = 37.361 us, max = 104.127 us, min = 8.700 us, total = 6.725 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.652 us, total = 1.917 ms, Queueing time: mean = 50.432 us, max = 90.667 us, min = 16.321 us, total = 9.078 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 175.843 us, total = 31.652 ms, Queueing time: mean = 47.523 us, max = 114.389 us, min = 9.110 us, total = 8.554 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 34.378 us, total = 1.238 ms, Queueing time: mean = 31.812 us, max = 72.433 us, min = 12.183 us, total = 1.145 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.418 us, total = 97.516 us, Queueing time: mean = 92.469 us, max = 745.468 us, min = 28.834 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.418 us, total = 520.923 us, Queueing time: mean = 212.571 us, max = 486.981 us, min = 7.708 us, total = 1.488 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 552.401 us, total = 1.657 ms, Queueing time: mean = 47.543 us, max = 73.008 us, min = 69.620 us, total = 142.628 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 109.553 us, total = 109.553 us, Queueing time: mean = 11.208 us, max = 11.208 us, min = 11.208 us, total = 11.208 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.374 us, total = 309.374 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.811 us, total = 16.811 us, Queueing time: mean = 8.509 us, max = 8.509 us, min = 8.509 us, total = 8.509 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 56.990 us, total = 56.990 us, Queueing time: mean = 17.500 us, max = 17.500 us, min = 17.500 us, total = 17.500 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 552.010 us, total = 552.010 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 392.337 us, total = 392.337 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17881 total (1 active)
Queueing time: mean = 57.313 us, max = 1.079 ms, min = -0.000 s, total = 1.025 s
Execution time:  mean = 15.097 us, total = 269.946 ms
Event stats:
	CoreWorker.CheckSignal - 17880 total (1 active), Execution time: mean = 15.097 us, total = 269.938 ms, Queueing time: mean = 57.316 us, max = 1.079 ms, min = -0.000 s, total = 1.025 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.924 us, total = 7.924 us, Queueing time: mean = 2.746 us, max = 2.746 us, min = 2.746 us, total = 2.746 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 28.837 us, max = 551.604 us, min = 7.111 us, total = 15.601 ms
Execution time:  mean = 326.256 us, total = 176.505 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 37.040 us, total = 6.667 ms, Queueing time: mean = 42.590 us, max = 551.604 us, min = 8.129 us, total = 7.666 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 201.342 us, total = 36.242 ms, Queueing time: mean = 44.041 us, max = 103.640 us, min = 19.016 us, total = 7.927 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 741.507 us, total = 133.471 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 124.726 us, total = 124.726 us, Queueing time: mean = 7.111 us, max = 7.111 us, min = 7.111 us, total = 7.111 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,496 I 660943 661621] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 49.826 us, max = 745.468 us, min = 7.708 us, total = 171.901 ms
Execution time:  mean = 74.469 us, total = 256.917 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.659 us, total = 20.773 ms, Queueing time: mean = 55.381 us, max = 587.392 us, min = 14.066 us, total = 132.859 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 741.533 us, total = 177.968 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.044 us, total = 6.971 ms, Queueing time: mean = 38.197 us, max = 104.127 us, min = 8.700 us, total = 9.167 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.588 us, total = 2.541 ms, Queueing time: mean = 51.650 us, max = 95.624 us, min = 16.321 us, total = 12.396 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 177.553 us, total = 42.613 ms, Queueing time: mean = 48.824 us, max = 114.389 us, min = 9.110 us, total = 11.718 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 34.100 us, total = 1.637 ms, Queueing time: mean = 31.972 us, max = 72.433 us, min = 12.183 us, total = 1.535 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.424 us, total = 130.168 us, Queueing time: mean = 103.771 us, max = 745.468 us, min = 24.612 us, total = 2.491 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.418 us, total = 520.923 us, Queueing time: mean = 212.571 us, max = 486.981 us, min = 7.708 us, total = 1.488 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 581.698 us, total = 2.327 ms, Queueing time: mean = 52.485 us, max = 73.008 us, min = 67.312 us, total = 209.940 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 109.553 us, total = 109.553 us, Queueing time: mean = 11.208 us, max = 11.208 us, min = 11.208 us, total = 11.208 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.374 us, total = 309.374 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.811 us, total = 16.811 us, Queueing time: mean = 8.509 us, max = 8.509 us, min = 8.509 us, total = 8.509 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 56.990 us, total = 56.990 us, Queueing time: mean = 17.500 us, max = 17.500 us, min = 17.500 us, total = 17.500 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 552.010 us, total = 552.010 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 392.337 us, total = 392.337 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23841 total (1 active)
Queueing time: mean = 57.137 us, max = 1.079 ms, min = -0.000 s, total = 1.362 s
Execution time:  mean = 15.091 us, total = 359.779 ms
Event stats:
	CoreWorker.CheckSignal - 23840 total (1 active), Execution time: mean = 15.091 us, total = 359.771 ms, Queueing time: mean = 57.140 us, max = 1.079 ms, min = -0.000 s, total = 1.362 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.924 us, total = 7.924 us, Queueing time: mean = 2.746 us, max = 2.746 us, min = 2.746 us, total = 2.746 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 29.081 us, max = 551.604 us, min = 7.111 us, total = 20.967 ms
Execution time:  mean = 330.305 us, total = 238.150 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 37.466 us, total = 8.992 ms, Queueing time: mean = 42.076 us, max = 551.604 us, min = 8.129 us, total = 10.098 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 202.594 us, total = 48.623 ms, Queueing time: mean = 45.258 us, max = 103.640 us, min = 19.016 us, total = 10.862 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 751.710 us, total = 180.410 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 124.726 us, total = 124.726 us, Queueing time: mean = 7.111 us, max = 7.111 us, min = 7.111 us, total = 7.111 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,497 I 660943 661621] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 49.987 us, max = 745.468 us, min = 7.708 us, total = 215.444 ms
Execution time:  mean = 74.609 us, total = 321.564 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.595 us, total = 25.778 ms, Queueing time: mean = 55.663 us, max = 587.392 us, min = 14.066 us, total = 166.934 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 745.225 us, total = 223.567 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 28.929 us, total = 8.679 ms, Queueing time: mean = 37.707 us, max = 111.574 us, min = 8.700 us, total = 11.312 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.532 us, total = 3.160 ms, Queueing time: mean = 51.882 us, max = 95.624 us, min = 16.321 us, total = 15.565 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 177.115 us, total = 53.135 ms, Queueing time: mean = 48.491 us, max = 114.389 us, min = 9.110 us, total = 14.547 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 34.458 us, total = 2.067 ms, Queueing time: mean = 35.525 us, max = 72.433 us, min = 12.183 us, total = 2.132 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.356 us, total = 160.691 us, Queueing time: mean = 105.415 us, max = 745.468 us, min = 24.612 us, total = 3.162 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.418 us, total = 520.923 us, Queueing time: mean = 212.571 us, max = 486.981 us, min = 7.708 us, total = 1.488 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 605.502 us, total = 3.028 ms, Queueing time: mean = 47.744 us, max = 73.008 us, min = 28.779 us, total = 238.719 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.462 us, total = 32.923 us, Queueing time: mean = 13.644 us, max = 27.288 us, min = 27.288 us, total = 27.288 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 109.553 us, total = 109.553 us, Queueing time: mean = 11.208 us, max = 11.208 us, min = 11.208 us, total = 11.208 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.374 us, total = 309.374 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.811 us, total = 16.811 us, Queueing time: mean = 8.509 us, max = 8.509 us, min = 8.509 us, total = 8.509 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 56.990 us, total = 56.990 us, Queueing time: mean = 17.500 us, max = 17.500 us, min = 17.500 us, total = 17.500 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 552.010 us, total = 552.010 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 392.337 us, total = 392.337 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29802 total (1 active)
Queueing time: mean = 56.979 us, max = 1.079 ms, min = -0.000 s, total = 1.698 s
Execution time:  mean = 15.085 us, total = 449.576 ms
Event stats:
	CoreWorker.CheckSignal - 29801 total (1 active), Execution time: mean = 15.086 us, total = 449.568 ms, Queueing time: mean = 56.980 us, max = 1.079 ms, min = -0.000 s, total = 1.698 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.924 us, total = 7.924 us, Queueing time: mean = 2.746 us, max = 2.746 us, min = 2.746 us, total = 2.746 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 29.435 us, max = 551.604 us, min = 7.111 us, total = 26.521 ms
Execution time:  mean = 327.090 us, total = 294.708 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 37.157 us, total = 11.147 ms, Queueing time: mean = 40.444 us, max = 551.604 us, min = 8.129 us, total = 12.133 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 202.508 us, total = 60.752 ms, Queueing time: mean = 47.935 us, max = 103.640 us, min = 19.016 us, total = 14.380 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 742.278 us, total = 222.683 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 124.726 us, total = 124.726 us, Queueing time: mean = 7.111 us, max = 7.111 us, min = 7.111 us, total = 7.111 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,498 I 660943 661621] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 50.425 us, max = 745.468 us, min = 7.708 us, total = 260.596 ms
Execution time:  mean = 73.806 us, total = 381.430 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.623 us, total = 31.024 ms, Queueing time: mean = 56.027 us, max = 587.392 us, min = 14.066 us, total = 201.585 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 732.552 us, total = 263.719 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.098 us, total = 10.475 ms, Queueing time: mean = 37.775 us, max = 111.574 us, min = 8.700 us, total = 13.599 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 10.887 us, total = 3.919 ms, Queueing time: mean = 52.685 us, max = 102.792 us, min = 16.321 us, total = 18.967 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 177.358 us, total = 63.849 ms, Queueing time: mean = 49.640 us, max = 114.389 us, min = 9.110 us, total = 17.870 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.375 us, total = 2.547 ms, Queueing time: mean = 38.651 us, max = 73.062 us, min = 12.183 us, total = 2.783 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.480 us, total = 197.268 us, Queueing time: mean = 109.304 us, max = 745.468 us, min = 24.612 us, total = 3.935 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.418 us, total = 520.923 us, Queueing time: mean = 212.571 us, max = 486.981 us, min = 7.708 us, total = 1.488 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 618.017 us, total = 3.708 ms, Queueing time: mean = 50.671 us, max = 73.008 us, min = 28.779 us, total = 304.028 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 16.462 us, total = 32.923 us, Queueing time: mean = 13.644 us, max = 27.288 us, min = 27.288 us, total = 27.288 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 109.553 us, total = 109.553 us, Queueing time: mean = 11.208 us, max = 11.208 us, min = 11.208 us, total = 11.208 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 309.374 us, total = 309.374 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 16.811 us, total = 16.811 us, Queueing time: mean = 8.509 us, max = 8.509 us, min = 8.509 us, total = 8.509 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 56.990 us, total = 56.990 us, Queueing time: mean = 17.500 us, max = 17.500 us, min = 17.500 us, total = 17.500 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 552.010 us, total = 552.010 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 392.337 us, total = 392.337 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35762 total (1 active)
Queueing time: mean = 56.937 us, max = 4.966 ms, min = -0.000 s, total = 2.036 s
Execution time:  mean = 15.052 us, total = 538.297 ms
Event stats:
	CoreWorker.CheckSignal - 35761 total (1 active), Execution time: mean = 15.052 us, total = 538.289 ms, Queueing time: mean = 56.938 us, max = 4.966 ms, min = -0.000 s, total = 2.036 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.924 us, total = 7.924 us, Queueing time: mean = 2.746 us, max = 2.746 us, min = 2.746 us, total = 2.746 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 30.033 us, max = 551.604 us, min = 7.111 us, total = 32.465 ms
Execution time:  mean = 325.438 us, total = 351.799 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 37.356 us, total = 13.448 ms, Queueing time: mean = 40.975 us, max = 551.604 us, min = 8.129 us, total = 14.751 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 202.566 us, total = 72.924 ms, Queueing time: mean = 49.187 us, max = 121.333 us, min = 19.016 us, total = 17.707 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 736.950 us, total = 265.302 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 124.726 us, total = 124.726 us, Queueing time: mean = 7.111 us, max = 7.111 us, min = 7.111 us, total = 7.111 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660943 661621] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660943 661621] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,785 I 660943 661621] core_worker.cc:5107: Number of alive nodes:0
