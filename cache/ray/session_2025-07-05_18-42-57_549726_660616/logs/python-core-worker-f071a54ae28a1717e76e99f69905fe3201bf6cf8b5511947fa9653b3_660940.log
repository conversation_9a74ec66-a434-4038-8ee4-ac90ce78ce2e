[2025-07-05 18:42:59,478 I 660940 660940] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660940
[2025-07-05 18:42:59,481 I 660940 660940] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,483 I 660940 660940] grpc_server.cc:141: worker server started, listening on port 39815.
[2025-07-05 18:42:59,484 I 660940 660940] core_worker.cc:542: Initializing worker at address: ***********:39815 worker_id=f071a54ae28a1717e76e99f69905fe3201bf6cf8b5511947fa9653b3 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,485 I 660940 660940] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-07-05 18:42:59,486 I 660940 661529] core_worker.cc:902: Event stats:


Global stats: 11 total (5 active)
Queueing time: mean = 10.479 us, max = 72.105 us, min = 14.170 us, total = 115.267 us
Execution time:  mean = 89.142 us, total = 980.565 us
Event stats:
	PeriodicalRunner.RunFnPeriodically - 3 total (1 active, 1 running), Execution time: mean = 7.477 us, total = 22.432 us, Queueing time: mean = 28.758 us, max = 72.105 us, min = 14.170 us, total = 86.275 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.095 us, total = 27.095 us, Queueing time: mean = 14.376 us, max = 14.376 us, min = 14.376 us, total = 14.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 336.097 us, total = 336.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.970 us, total = 104.970 us, Queueing time: mean = 14.616 us, max = 14.616 us, min = 14.616 us, total = 14.616 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 489.971 us, total = 489.971 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 6.548 us, max = 14.893 us, min = 11.299 us, total = 26.192 us
Execution time:  mean = 266.634 us, total = 1.067 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 150.104 us, total = 150.104 us, Queueing time: mean = 11.299 us, max = 11.299 us, min = 11.299 us, total = 11.299 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 900.925 us, total = 900.925 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 15.508 us, total = 15.508 us, Queueing time: mean = 14.893 us, max = 14.893 us, min = 14.893 us, total = 14.893 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,487 I 660940 660940] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,487 I 660940 660940] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,487 I 660940 660940] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,487 I 660940 660940] event.cc:331: Set ray event level to warning
[2025-07-05 18:42:59,487 I 660940 661529] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,487 I 660940 661529] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:43:09,491 W 660940 661518] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,487 I 660940 661529] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 50.643 us, max = 116.186 us, min = 6.400 us, total = 44.312 ms
Execution time:  mean = 72.752 us, total = 63.658 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 7.970 us, total = 4.782 ms, Queueing time: mean = 58.522 us, max = 111.867 us, min = 16.598 us, total = 35.113 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.391 us, total = 633.854 us, Queueing time: mean = 54.667 us, max = 82.959 us, min = 23.603 us, total = 3.335 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 162.361 us, total = 9.742 ms, Queueing time: mean = 46.241 us, max = 80.289 us, min = 17.702 us, total = 2.774 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 28.126 us, total = 1.688 ms, Queueing time: mean = 39.374 us, max = 83.544 us, min = 6.668 us, total = 2.362 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 719.548 us, total = 43.173 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 31.788 us, total = 381.456 us, Queueing time: mean = 24.800 us, max = 65.871 us, min = 6.505 us, total = 297.604 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.432 us, total = 514.023 us, Queueing time: mean = 18.394 us, max = 72.105 us, min = 6.400 us, total = 128.758 us
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 4.226 us, total = 25.358 us, Queueing time: mean = 25.993 us, max = 39.322 us, min = 15.108 us, total = 155.957 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 336.097 us, total = 336.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.095 us, total = 27.095 us, Queueing time: mean = 14.376 us, max = 14.376 us, min = 14.376 us, total = 14.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 489.971 us, total = 489.971 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.641 us, total = 58.641 us, Queueing time: mean = 116.186 us, max = 116.186 us, min = 116.186 us, total = 116.186 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.970 us, total = 104.970 us, Queueing time: mean = 14.616 us, max = 14.616 us, min = 14.616 us, total = 14.616 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.702 ms, total = 1.702 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5961 total (1 active)
Queueing time: mean = 56.921 us, max = 3.860 ms, min = 2.762 us, total = 339.309 ms
Execution time:  mean = 15.135 us, total = 90.217 ms
Event stats:
	CoreWorker.CheckSignal - 5960 total (1 active), Execution time: mean = 15.136 us, total = 90.209 ms, Queueing time: mean = 56.931 us, max = 3.860 ms, min = 7.625 us, total = 339.306 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.122 us, total = 8.122 us, Queueing time: mean = 2.762 us, max = 2.762 us, min = 2.762 us, total = 2.762 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 29.024 us, max = 121.638 us, min = 10.211 us, total = 5.253 ms
Execution time:  mean = 318.328 us, total = 57.617 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 200.567 us, total = 12.034 ms, Queueing time: mean = 50.080 us, max = 121.638 us, min = 19.862 us, total = 3.005 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 721.409 us, total = 43.285 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 35.811 us, total = 2.149 ms, Queueing time: mean = 37.287 us, max = 74.551 us, min = 10.211 us, total = 2.237 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 150.104 us, total = 150.104 us, Queueing time: mean = 11.299 us, max = 11.299 us, min = 11.299 us, total = 11.299 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,488 I 660940 661529] core_worker.cc:902: Event stats:


Global stats: 1733 total (8 active)
Queueing time: mean = 51.804 us, max = 455.260 us, min = 6.400 us, total = 89.776 ms
Execution time:  mean = 74.552 us, total = 129.198 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.060 us, total = 9.672 ms, Queueing time: mean = 58.330 us, max = 128.840 us, min = 16.598 us, total = 69.996 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 171.504 us, total = 20.580 ms, Queueing time: mean = 50.668 us, max = 80.289 us, min = 17.702 us, total = 6.080 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 29.290 us, total = 3.515 ms, Queueing time: mean = 41.301 us, max = 455.260 us, min = 6.668 us, total = 4.956 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 10.813 us, total = 1.298 ms, Queueing time: mean = 58.280 us, max = 171.277 us, min = 12.912 us, total = 6.994 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 744.425 us, total = 89.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 34.116 us, total = 818.794 us, Queueing time: mean = 27.041 us, max = 73.359 us, min = 6.505 us, total = 648.993 us
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 4.843 us, total = 58.122 us, Queueing time: mean = 63.334 us, max = 249.719 us, min = 15.108 us, total = 760.006 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.432 us, total = 514.023 us, Queueing time: mean = 18.394 us, max = 72.105 us, min = 6.400 us, total = 128.758 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 346.082 us, total = 692.164 us, Queueing time: mean = 33.972 us, max = 67.944 us, min = 67.944 us, total = 67.944 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 336.097 us, total = 336.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.095 us, total = 27.095 us, Queueing time: mean = 14.376 us, max = 14.376 us, min = 14.376 us, total = 14.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 489.971 us, total = 489.971 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.641 us, total = 58.641 us, Queueing time: mean = 116.186 us, max = 116.186 us, min = 116.186 us, total = 116.186 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.970 us, total = 104.970 us, Queueing time: mean = 14.616 us, max = 14.616 us, min = 14.616 us, total = 14.616 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.702 ms, total = 1.702 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11924 total (1 active)
Queueing time: mean = 54.845 us, max = 3.860 ms, min = 102.000 ns, total = 653.974 ms
Execution time:  mean = 15.264 us, total = 182.009 ms
Event stats:
	CoreWorker.CheckSignal - 11923 total (1 active), Execution time: mean = 15.265 us, total = 182.001 ms, Queueing time: mean = 54.850 us, max = 3.860 ms, min = 102.000 ns, total = 653.971 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.122 us, total = 8.122 us, Queueing time: mean = 2.762 us, max = 2.762 us, min = 2.762 us, total = 2.762 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 27.504 us, max = 121.638 us, min = 10.211 us, total = 9.929 ms
Execution time:  mean = 315.633 us, total = 113.944 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 202.257 us, total = 24.271 ms, Queueing time: mean = 46.419 us, max = 121.638 us, min = 19.862 us, total = 5.570 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 710.472 us, total = 85.257 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 35.550 us, total = 4.266 ms, Queueing time: mean = 36.226 us, max = 74.551 us, min = 10.211 us, total = 4.347 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 150.104 us, total = 150.104 us, Queueing time: mean = 11.299 us, max = 11.299 us, min = 11.299 us, total = 11.299 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,489 I 660940 661529] core_worker.cc:902: Event stats:


Global stats: 2591 total (8 active)
Queueing time: mean = 51.421 us, max = 657.594 us, min = 6.400 us, total = 133.232 ms
Execution time:  mean = 74.654 us, total = 193.427 ms
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 8.092 us, total = 14.557 ms, Queueing time: mean = 57.613 us, max = 128.840 us, min = 15.307 us, total = 103.647 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 175.401 us, total = 31.572 ms, Queueing time: mean = 48.547 us, max = 91.194 us, min = 17.702 us, total = 8.739 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.507 us, total = 5.311 ms, Queueing time: mean = 39.472 us, max = 455.260 us, min = 6.668 us, total = 7.105 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 10.912 us, total = 1.964 ms, Queueing time: mean = 58.197 us, max = 171.277 us, min = 12.912 us, total = 10.475 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 743.104 us, total = 133.759 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.664 us, total = 1.284 ms, Queueing time: mean = 31.131 us, max = 85.155 us, min = 6.505 us, total = 1.121 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 4.909 us, total = 88.363 us, Queueing time: mean = 96.236 us, max = 657.594 us, min = 15.108 us, total = 1.732 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.432 us, total = 514.023 us, Queueing time: mean = 18.394 us, max = 72.105 us, min = 6.400 us, total = 128.758 us
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 552.795 us, total = 1.658 ms, Queueing time: mean = 46.475 us, max = 71.482 us, min = 67.944 us, total = 139.426 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 336.097 us, total = 336.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.095 us, total = 27.095 us, Queueing time: mean = 14.376 us, max = 14.376 us, min = 14.376 us, total = 14.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 489.971 us, total = 489.971 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.641 us, total = 58.641 us, Queueing time: mean = 116.186 us, max = 116.186 us, min = 116.186 us, total = 116.186 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.970 us, total = 104.970 us, Queueing time: mean = 14.616 us, max = 14.616 us, min = 14.616 us, total = 14.616 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.702 ms, total = 1.702 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17888 total (1 active)
Queueing time: mean = 53.063 us, max = 3.860 ms, min = -0.000 s, total = 949.196 ms
Execution time:  mean = 15.279 us, total = 273.306 ms
Event stats:
	CoreWorker.CheckSignal - 17887 total (1 active), Execution time: mean = 15.279 us, total = 273.298 ms, Queueing time: mean = 53.066 us, max = 3.860 ms, min = -0.000 s, total = 949.193 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.122 us, total = 8.122 us, Queueing time: mean = 2.762 us, max = 2.762 us, min = 2.762 us, total = 2.762 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 28.549 us, max = 121.638 us, min = 10.211 us, total = 15.445 ms
Execution time:  mean = 328.558 us, total = 177.750 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 208.296 us, total = 37.493 ms, Queueing time: mean = 47.279 us, max = 121.638 us, min = 19.862 us, total = 8.510 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 740.824 us, total = 133.348 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 37.544 us, total = 6.758 ms, Queueing time: mean = 38.462 us, max = 83.560 us, min = 10.211 us, total = 6.923 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 150.104 us, total = 150.104 us, Queueing time: mean = 11.299 us, max = 11.299 us, min = 11.299 us, total = 11.299 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,489 I 660940 661529] core_worker.cc:902: Event stats:


Global stats: 3450 total (8 active)
Queueing time: mean = 52.074 us, max = 657.594 us, min = 6.400 us, total = 179.655 ms
Execution time:  mean = 75.837 us, total = 261.638 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.050 us, total = 19.312 ms, Queueing time: mean = 58.160 us, max = 128.840 us, min = 15.307 us, total = 139.526 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 177.950 us, total = 42.708 ms, Queueing time: mean = 49.812 us, max = 91.194 us, min = 17.702 us, total = 11.955 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 30.373 us, total = 7.290 ms, Queueing time: mean = 40.004 us, max = 455.260 us, min = 6.668 us, total = 9.601 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 10.921 us, total = 2.621 ms, Queueing time: mean = 58.482 us, max = 171.277 us, min = 12.912 us, total = 14.036 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 759.710 us, total = 182.330 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 35.654 us, total = 1.711 ms, Queueing time: mean = 33.387 us, max = 85.155 us, min = 6.505 us, total = 1.603 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 4.888 us, total = 117.320 us, Queueing time: mean = 101.920 us, max = 657.594 us, min = 15.108 us, total = 2.446 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.432 us, total = 514.023 us, Queueing time: mean = 18.394 us, max = 72.105 us, min = 6.400 us, total = 128.758 us
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 578.720 us, total = 2.315 ms, Queueing time: mean = 53.596 us, max = 74.956 us, min = 67.944 us, total = 214.382 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 336.097 us, total = 336.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.095 us, total = 27.095 us, Queueing time: mean = 14.376 us, max = 14.376 us, min = 14.376 us, total = 14.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 489.971 us, total = 489.971 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.641 us, total = 58.641 us, Queueing time: mean = 116.186 us, max = 116.186 us, min = 116.186 us, total = 116.186 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.970 us, total = 104.970 us, Queueing time: mean = 14.616 us, max = 14.616 us, min = 14.616 us, total = 14.616 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.702 ms, total = 1.702 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23852 total (1 active)
Queueing time: mean = 52.626 us, max = 3.860 ms, min = -0.000 s, total = 1.255 s
Execution time:  mean = 15.330 us, total = 365.649 ms
Event stats:
	CoreWorker.CheckSignal - 23851 total (1 active), Execution time: mean = 15.330 us, total = 365.641 ms, Queueing time: mean = 52.628 us, max = 3.860 ms, min = -0.000 s, total = 1.255 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.122 us, total = 8.122 us, Queueing time: mean = 2.762 us, max = 2.762 us, min = 2.762 us, total = 2.762 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 29.522 us, max = 121.638 us, min = 10.211 us, total = 21.285 ms
Execution time:  mean = 329.015 us, total = 237.219 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 208.302 us, total = 49.992 ms, Queueing time: mean = 50.213 us, max = 121.638 us, min = 19.862 us, total = 12.051 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 741.569 us, total = 177.977 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 37.918 us, total = 9.100 ms, Queueing time: mean = 38.428 us, max = 83.560 us, min = 10.211 us, total = 9.223 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 150.104 us, total = 150.104 us, Queueing time: mean = 11.299 us, max = 11.299 us, min = 11.299 us, total = 11.299 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,490 I 660940 661529] core_worker.cc:902: Event stats:


Global stats: 4310 total (8 active)
Queueing time: mean = 51.138 us, max = 657.594 us, min = 2.211 us, total = 220.404 ms
Execution time:  mean = 76.089 us, total = 327.944 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.068 us, total = 24.197 ms, Queueing time: mean = 56.808 us, max = 128.840 us, min = 12.852 us, total = 170.367 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 180.511 us, total = 54.153 ms, Queueing time: mean = 50.249 us, max = 91.194 us, min = 2.211 us, total = 15.075 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 30.595 us, total = 9.179 ms, Queueing time: mean = 39.642 us, max = 455.260 us, min = 6.668 us, total = 11.893 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 10.932 us, total = 3.279 ms, Queueing time: mean = 57.633 us, max = 171.277 us, min = 12.912 us, total = 17.290 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 762.076 us, total = 228.623 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.857 us, total = 2.151 ms, Queueing time: mean = 34.521 us, max = 85.155 us, min = 6.505 us, total = 2.071 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 4.889 us, total = 146.680 us, Queueing time: mean = 103.293 us, max = 657.594 us, min = 15.108 us, total = 3.099 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.432 us, total = 514.023 us, Queueing time: mean = 18.394 us, max = 72.105 us, min = 6.400 us, total = 128.758 us
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 595.069 us, total = 2.975 ms, Queueing time: mean = 48.164 us, max = 74.956 us, min = 26.439 us, total = 240.821 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 3.288 us, total = 6.577 us, Queueing time: mean = 47.605 us, max = 95.211 us, min = 95.211 us, total = 95.211 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 336.097 us, total = 336.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.095 us, total = 27.095 us, Queueing time: mean = 14.376 us, max = 14.376 us, min = 14.376 us, total = 14.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 489.971 us, total = 489.971 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.641 us, total = 58.641 us, Queueing time: mean = 116.186 us, max = 116.186 us, min = 116.186 us, total = 116.186 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.970 us, total = 104.970 us, Queueing time: mean = 14.616 us, max = 14.616 us, min = 14.616 us, total = 14.616 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.702 ms, total = 1.702 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29810 total (1 active)
Queueing time: mean = 54.058 us, max = 3.860 ms, min = -0.000 s, total = 1.611 s
Execution time:  mean = 15.276 us, total = 455.377 ms
Event stats:
	CoreWorker.CheckSignal - 29809 total (1 active), Execution time: mean = 15.276 us, total = 455.369 ms, Queueing time: mean = 54.060 us, max = 3.860 ms, min = -0.000 s, total = 1.611 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.122 us, total = 8.122 us, Queueing time: mean = 2.762 us, max = 2.762 us, min = 2.762 us, total = 2.762 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 29.521 us, max = 121.638 us, min = 10.211 us, total = 26.598 ms
Execution time:  mean = 328.056 us, total = 295.579 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 205.857 us, total = 61.757 ms, Queueing time: mean = 51.661 us, max = 121.638 us, min = 19.483 us, total = 15.498 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 741.722 us, total = 222.517 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 37.183 us, total = 11.155 ms, Queueing time: mean = 36.963 us, max = 83.560 us, min = 10.211 us, total = 11.089 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 150.104 us, total = 150.104 us, Queueing time: mean = 11.299 us, max = 11.299 us, min = 11.299 us, total = 11.299 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,491 I 660940 661529] core_worker.cc:902: Event stats:


Global stats: 5168 total (8 active)
Queueing time: mean = 51.806 us, max = 657.594 us, min = 2.211 us, total = 267.732 ms
Execution time:  mean = 77.003 us, total = 397.949 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.127 us, total = 29.243 ms, Queueing time: mean = 57.593 us, max = 128.840 us, min = 12.852 us, total = 207.220 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 183.330 us, total = 65.999 ms, Queueing time: mean = 51.865 us, max = 110.004 us, min = 2.211 us, total = 18.672 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 30.690 us, total = 11.048 ms, Queueing time: mean = 38.654 us, max = 455.260 us, min = 6.668 us, total = 13.916 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 11.206 us, total = 4.034 ms, Queueing time: mean = 57.383 us, max = 171.277 us, min = 12.912 us, total = 20.658 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 771.988 us, total = 277.916 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 36.386 us, total = 2.620 ms, Queueing time: mean = 38.683 us, max = 89.669 us, min = 6.505 us, total = 2.785 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 4.948 us, total = 178.124 us, Queueing time: mean = 105.629 us, max = 657.594 us, min = 15.108 us, total = 3.803 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 73.432 us, total = 514.023 us, Queueing time: mean = 18.394 us, max = 72.105 us, min = 6.400 us, total = 128.758 us
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 611.994 us, total = 3.672 ms, Queueing time: mean = 51.620 us, max = 74.956 us, min = 26.439 us, total = 309.721 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 3.288 us, total = 6.577 us, Queueing time: mean = 47.605 us, max = 95.211 us, min = 95.211 us, total = 95.211 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 336.097 us, total = 336.097 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 27.095 us, total = 27.095 us, Queueing time: mean = 14.376 us, max = 14.376 us, min = 14.376 us, total = 14.376 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 489.971 us, total = 489.971 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 58.641 us, total = 58.641 us, Queueing time: mean = 116.186 us, max = 116.186 us, min = 116.186 us, total = 116.186 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 104.970 us, total = 104.970 us, Queueing time: mean = 14.616 us, max = 14.616 us, min = 14.616 us, total = 14.616 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.702 ms, total = 1.702 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35769 total (1 active)
Queueing time: mean = 54.975 us, max = 3.860 ms, min = -0.000 s, total = 1.966 s
Execution time:  mean = 15.205 us, total = 543.883 ms
Event stats:
	CoreWorker.CheckSignal - 35768 total (1 active), Execution time: mean = 15.206 us, total = 543.875 ms, Queueing time: mean = 54.976 us, max = 3.860 ms, min = -0.000 s, total = 1.966 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 8.122 us, total = 8.122 us, Queueing time: mean = 2.762 us, max = 2.762 us, min = 2.762 us, total = 2.762 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 29.590 us, max = 121.638 us, min = 10.211 us, total = 31.987 ms
Execution time:  mean = 327.089 us, total = 353.583 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 205.565 us, total = 74.003 ms, Queueing time: mean = 51.612 us, max = 121.638 us, min = 16.423 us, total = 18.580 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 738.966 us, total = 266.028 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 37.228 us, total = 13.402 ms, Queueing time: mean = 37.208 us, max = 83.560 us, min = 10.211 us, total = 13.395 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 150.104 us, total = 150.104 us, Queueing time: mean = 11.299 us, max = 11.299 us, min = 11.299 us, total = 11.299 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660940 661529] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660940 661529] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660940 661529] core_worker.cc:5107: Number of alive nodes:0
