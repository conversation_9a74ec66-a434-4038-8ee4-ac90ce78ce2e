[2025-07-05 18:42:59,663 I 660947 660947] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 660947
[2025-07-05 18:42:59,665 I 660947 660947] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-07-05 18:42:59,666 I 660947 660947] grpc_server.cc:141: worker server started, listening on port 36193.
[2025-07-05 18:42:59,668 I 660947 660947] core_worker.cc:542: Initializing worker at address: ***********:36193 worker_id=f4adcda25d09cb2fa11fe83c1adc159fca61ef2ad30ada767342df0b node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,669 I 660947 660947] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-07-05 18:42:59,670 I 660947 662093] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:42:59,670 I 660947 662093] core_worker.cc:5107: Number of alive nodes:1
[2025-07-05 18:42:59,699 I 660947 660947] core_worker.cc:967: Adjusted worker niceness to 15
[2025-07-05 18:42:59,699 I 660947 660947] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-07-05 18:42:59,699 I 660947 662093] core_worker.cc:902: Event stats:


Global stats: 14 total (6 active)
Queueing time: mean = 14.896 us, max = 110.181 us, min = 10.896 us, total = 208.544 us
Execution time:  mean = 112.735 us, total = 1.578 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 5 total (3 active, 1 running), Execution time: mean = 5.065 us, total = 25.326 us, Queueing time: mean = 28.324 us, max = 110.181 us, min = 31.439 us, total = 141.620 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 472.090 us, total = 472.090 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.744 us, total = 19.744 us, Queueing time: mean = 25.146 us, max = 25.146 us, min = 25.146 us, total = 25.146 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.152 us, total = 520.152 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.714 us, total = 63.714 us, Queueing time: mean = 30.882 us, max = 30.882 us, min = 30.882 us, total = 30.882 us
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 107.768 us, total = 107.768 us, Queueing time: mean = 10.896 us, max = 10.896 us, min = 10.896 us, total = 10.896 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 369.491 us, total = 369.491 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 8.636 us, max = 26.843 us, min = 7.703 us, total = 34.546 us
Execution time:  mean = 142.146 us, total = 568.583 us
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 405.834 us, total = 405.834 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.637 us, total = 144.637 us, Queueing time: mean = 26.843 us, max = 26.843 us, min = 26.843 us, total = 26.843 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.112 us, total = 18.112 us, Queueing time: mean = 7.703 us, max = 7.703 us, min = 7.703 us, total = 7.703 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:42:59,699 I 660947 660947] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-07-05 18:42:59,699 I 660947 660947] event.cc:331: Set ray event level to warning
[2025-07-05 18:43:09,676 W 660947 662088] metric_exporter.cc:105: [1] Export metrics to agent failed: RpcError: RPC Error message: failed to connect to all addresses; last error: UNKNOWN: ipv4:127.0.0.1:37368: Failed to connect to remote host: Connection refused; RPC Error details:  rpc_code: 14. This won't affect Ray, but you can lose metrics from the cluster.
[2025-07-05 18:43:59,699 I 660947 662093] core_worker.cc:902: Event stats:


Global stats: 875 total (8 active)
Queueing time: mean = 48.991 us, max = 489.888 us, min = 9.111 us, total = 42.867 ms
Execution time:  mean = 75.151 us, total = 65.757 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 8.856 us, total = 5.314 ms, Queueing time: mean = 52.293 us, max = 179.068 us, min = 14.218 us, total = 31.376 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 10.407 us, total = 634.826 us, Queueing time: mean = 55.054 us, max = 82.562 us, min = 13.553 us, total = 3.358 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 172.286 us, total = 10.337 ms, Queueing time: mean = 53.447 us, max = 90.292 us, min = 16.087 us, total = 3.207 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 756.355 us, total = 45.381 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 26.807 us, total = 1.608 ms, Queueing time: mean = 43.851 us, max = 223.661 us, min = 9.952 us, total = 2.631 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 30.911 us, total = 370.937 us, Queueing time: mean = 32.669 us, max = 71.526 us, min = 14.962 us, total = 392.024 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.822 us, total = 523.757 us, Queueing time: mean = 222.967 us, max = 489.888 us, min = 9.111 us, total = 1.561 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 5.666 us, total = 33.998 us, Queueing time: mean = 45.950 us, max = 78.665 us, min = 34.525 us, total = 275.703 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.152 us, total = 520.152 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 369.491 us, total = 369.491 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.744 us, total = 19.744 us, Queueing time: mean = 25.146 us, max = 25.146 us, min = 25.146 us, total = 25.146 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 107.768 us, total = 107.768 us, Queueing time: mean = 10.896 us, max = 10.896 us, min = 10.896 us, total = 10.896 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.714 us, total = 63.714 us, Queueing time: mean = 30.882 us, max = 30.882 us, min = 30.882 us, total = 30.882 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 472.090 us, total = 472.090 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 5961 total (1 active)
Queueing time: mean = 57.514 us, max = 2.066 ms, min = 3.107 us, total = 342.843 ms
Execution time:  mean = 15.000 us, total = 89.417 ms
Event stats:
	CoreWorker.CheckSignal - 5960 total (1 active), Execution time: mean = 15.001 us, total = 89.409 ms, Queueing time: mean = 57.524 us, max = 2.066 ms, min = 9.496 us, total = 342.840 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.847 us, total = 7.847 us, Queueing time: mean = 3.107 us, max = 3.107 us, min = 3.107 us, total = 3.107 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 184 total (1 active)
Queueing time: mean = 36.939 us, max = 102.977 us, min = 7.703 us, total = 6.797 ms
Execution time:  mean = 354.737 us, total = 65.272 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 61 total (1 active), Execution time: mean = 219.618 us, total = 13.397 ms, Queueing time: mean = 61.840 us, max = 102.977 us, min = 20.968 us, total = 3.772 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 61 total (0 active), Execution time: mean = 809.461 us, total = 49.377 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 61 total (0 active), Execution time: mean = 38.575 us, total = 2.353 ms, Queueing time: mean = 49.142 us, max = 81.524 us, min = 7.703 us, total = 2.998 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.637 us, total = 144.637 us, Queueing time: mean = 26.843 us, max = 26.843 us, min = 26.843 us, total = 26.843 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:44:59,700 I 660947 662093] core_worker.cc:902: Event stats:


Global stats: 1734 total (8 active)
Queueing time: mean = 48.318 us, max = 489.888 us, min = 8.176 us, total = 83.783 ms
Execution time:  mean = 76.736 us, total = 133.060 ms
Event stats:
	CoreWorker.RecoverObjects - 1200 total (1 active), Execution time: mean = 8.756 us, total = 10.508 ms, Queueing time: mean = 51.983 us, max = 214.024 us, min = 13.610 us, total = 62.380 ms
	CoreWorker.ExitIfParentRayletDies - 121 total (1 active), Execution time: mean = 11.285 us, total = 1.365 ms, Queueing time: mean = 53.423 us, max = 106.296 us, min = 10.416 us, total = 6.464 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 179.591 us, total = 21.551 ms, Queueing time: mean = 52.359 us, max = 92.295 us, min = 16.087 us, total = 6.283 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 27.981 us, total = 3.358 ms, Queueing time: mean = 44.409 us, max = 223.661 us, min = 9.952 us, total = 5.329 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 771.578 us, total = 92.589 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 37.783 us, total = 906.786 us, Queueing time: mean = 44.531 us, max = 105.086 us, min = 8.176 us, total = 1.069 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 5.435 us, total = 65.218 us, Queueing time: mean = 49.172 us, max = 109.298 us, min = 18.941 us, total = 590.067 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.822 us, total = 523.757 us, Queueing time: mean = 222.967 us, max = 489.888 us, min = 9.111 us, total = 1.561 ms
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 319.933 us, total = 639.866 us, Queueing time: mean = 20.130 us, max = 40.260 us, min = 40.260 us, total = 40.260 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.152 us, total = 520.152 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 107.768 us, total = 107.768 us, Queueing time: mean = 10.896 us, max = 10.896 us, min = 10.896 us, total = 10.896 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.714 us, total = 63.714 us, Queueing time: mean = 30.882 us, max = 30.882 us, min = 30.882 us, total = 30.882 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 472.090 us, total = 472.090 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 369.491 us, total = 369.491 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.744 us, total = 19.744 us, Queueing time: mean = 25.146 us, max = 25.146 us, min = 25.146 us, total = 25.146 us

-----------------
Task execution event stats:

Global stats: 11924 total (1 active)
Queueing time: mean = 55.181 us, max = 2.066 ms, min = -0.000 s, total = 657.980 ms
Execution time:  mean = 15.119 us, total = 180.276 ms
Event stats:
	CoreWorker.CheckSignal - 11923 total (1 active), Execution time: mean = 15.119 us, total = 180.268 ms, Queueing time: mean = 55.185 us, max = 2.066 ms, min = -0.000 s, total = 657.976 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.847 us, total = 7.847 us, Queueing time: mean = 3.107 us, max = 3.107 us, min = 3.107 us, total = 3.107 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 36.361 us, max = 102.995 us, min = 7.703 us, total = 13.126 ms
Execution time:  mean = 350.203 us, total = 126.423 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 217.141 us, total = 26.057 ms, Queueing time: mean = 63.110 us, max = 102.995 us, min = 20.968 us, total = 7.573 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 797.498 us, total = 95.700 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 37.682 us, total = 4.522 ms, Queueing time: mean = 46.054 us, max = 91.151 us, min = 7.703 us, total = 5.526 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.637 us, total = 144.637 us, Queueing time: mean = 26.843 us, max = 26.843 us, min = 26.843 us, total = 26.843 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:45:59,701 I 660947 662093] core_worker.cc:902: Event stats:


Global stats: 2593 total (8 active)
Queueing time: mean = 48.508 us, max = 745.644 us, min = 8.176 us, total = 125.782 ms
Execution time:  mean = 77.409 us, total = 200.721 ms
Event stats:
	CoreWorker.RecoverObjects - 1800 total (1 active), Execution time: mean = 8.671 us, total = 15.607 ms, Queueing time: mean = 51.896 us, max = 214.024 us, min = 13.610 us, total = 93.413 ms
	CoreWorker.ExitIfParentRayletDies - 181 total (1 active), Execution time: mean = 11.247 us, total = 2.036 ms, Queueing time: mean = 52.733 us, max = 106.296 us, min = 10.416 us, total = 9.545 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 182.004 us, total = 32.761 ms, Queueing time: mean = 53.290 us, max = 111.405 us, min = 16.087 us, total = 9.592 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 29.008 us, total = 5.221 ms, Queueing time: mean = 45.579 us, max = 223.661 us, min = 9.952 us, total = 8.204 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 778.138 us, total = 140.065 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 35.976 us, total = 1.295 ms, Queueing time: mean = 47.259 us, max = 105.086 us, min = 8.176 us, total = 1.701 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 5.657 us, total = 101.833 us, Queueing time: mean = 89.146 us, max = 745.644 us, min = 18.941 us, total = 1.605 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.822 us, total = 523.757 us, Queueing time: mean = 222.967 us, max = 489.888 us, min = 9.111 us, total = 1.561 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 519.157 us, total = 1.557 ms, Queueing time: mean = 31.470 us, max = 54.151 us, min = 40.260 us, total = 94.411 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.152 us, total = 520.152 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 107.768 us, total = 107.768 us, Queueing time: mean = 10.896 us, max = 10.896 us, min = 10.896 us, total = 10.896 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.714 us, total = 63.714 us, Queueing time: mean = 30.882 us, max = 30.882 us, min = 30.882 us, total = 30.882 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 472.090 us, total = 472.090 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 369.491 us, total = 369.491 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.744 us, total = 19.744 us, Queueing time: mean = 25.146 us, max = 25.146 us, min = 25.146 us, total = 25.146 us

-----------------
Task execution event stats:

Global stats: 17889 total (1 active)
Queueing time: mean = 53.019 us, max = 2.066 ms, min = -0.000 s, total = 948.462 ms
Execution time:  mean = 15.165 us, total = 271.283 ms
Event stats:
	CoreWorker.CheckSignal - 17888 total (1 active), Execution time: mean = 15.165 us, total = 271.275 ms, Queueing time: mean = 53.022 us, max = 2.066 ms, min = -0.000 s, total = 948.459 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.847 us, total = 7.847 us, Queueing time: mean = 3.107 us, max = 3.107 us, min = 3.107 us, total = 3.107 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 35.949 us, max = 102.995 us, min = 7.703 us, total = 19.449 ms
Execution time:  mean = 347.340 us, total = 187.911 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 213.553 us, total = 38.440 ms, Queueing time: mean = 63.920 us, max = 102.995 us, min = 20.968 us, total = 11.506 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 792.640 us, total = 142.675 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 36.954 us, total = 6.652 ms, Queueing time: mean = 43.978 us, max = 91.151 us, min = 7.703 us, total = 7.916 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.637 us, total = 144.637 us, Queueing time: mean = 26.843 us, max = 26.843 us, min = 26.843 us, total = 26.843 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:46:59,702 I 660947 662093] core_worker.cc:902: Event stats:


Global stats: 3451 total (8 active)
Queueing time: mean = 48.581 us, max = 745.644 us, min = 8.176 us, total = 167.655 ms
Execution time:  mean = 77.510 us, total = 267.487 ms
Event stats:
	CoreWorker.RecoverObjects - 2399 total (1 active), Execution time: mean = 8.600 us, total = 20.631 ms, Queueing time: mean = 52.030 us, max = 214.024 us, min = 13.610 us, total = 124.819 ms
	CoreWorker.ExitIfParentRayletDies - 241 total (1 active), Execution time: mean = 11.249 us, total = 2.711 ms, Queueing time: mean = 53.927 us, max = 106.296 us, min = 10.416 us, total = 12.996 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 182.343 us, total = 43.762 ms, Queueing time: mean = 53.213 us, max = 111.405 us, min = 16.087 us, total = 12.771 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 29.075 us, total = 6.978 ms, Queueing time: mean = 45.198 us, max = 223.661 us, min = 9.952 us, total = 10.848 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 780.089 us, total = 187.221 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 36.480 us, total = 1.751 ms, Queueing time: mean = 45.510 us, max = 105.086 us, min = 8.176 us, total = 2.184 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 5.678 us, total = 136.273 us, Queueing time: mean = 94.812 us, max = 745.644 us, min = 18.941 us, total = 2.275 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.822 us, total = 523.757 us, Queueing time: mean = 222.967 us, max = 489.888 us, min = 9.111 us, total = 1.561 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 554.688 us, total = 2.219 ms, Queueing time: mean = 33.202 us, max = 54.151 us, min = 38.397 us, total = 132.808 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.152 us, total = 520.152 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 107.768 us, total = 107.768 us, Queueing time: mean = 10.896 us, max = 10.896 us, min = 10.896 us, total = 10.896 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.714 us, total = 63.714 us, Queueing time: mean = 30.882 us, max = 30.882 us, min = 30.882 us, total = 30.882 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 472.090 us, total = 472.090 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 369.491 us, total = 369.491 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.744 us, total = 19.744 us, Queueing time: mean = 25.146 us, max = 25.146 us, min = 25.146 us, total = 25.146 us

-----------------
Task execution event stats:

Global stats: 23851 total (1 active)
Queueing time: mean = 52.892 us, max = 2.066 ms, min = -0.000 s, total = 1.262 s
Execution time:  mean = 15.201 us, total = 362.550 ms
Event stats:
	CoreWorker.CheckSignal - 23850 total (1 active), Execution time: mean = 15.201 us, total = 362.542 ms, Queueing time: mean = 52.894 us, max = 2.066 ms, min = -0.000 s, total = 1.262 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.847 us, total = 7.847 us, Queueing time: mean = 3.107 us, max = 3.107 us, min = 3.107 us, total = 3.107 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 36.042 us, max = 106.244 us, min = 7.703 us, total = 25.986 ms
Execution time:  mean = 344.736 us, total = 248.555 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 211.003 us, total = 50.641 ms, Queueing time: mean = 64.373 us, max = 102.995 us, min = 20.968 us, total = 15.449 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 787.292 us, total = 188.950 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 36.749 us, total = 8.820 ms, Queueing time: mean = 43.791 us, max = 106.244 us, min = 7.703 us, total = 10.510 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.637 us, total = 144.637 us, Queueing time: mean = 26.843 us, max = 26.843 us, min = 26.843 us, total = 26.843 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:47:59,703 I 660947 662093] core_worker.cc:902: Event stats:


Global stats: 4311 total (8 active)
Queueing time: mean = 49.056 us, max = 745.644 us, min = 8.176 us, total = 211.481 ms
Execution time:  mean = 77.422 us, total = 333.767 ms
Event stats:
	CoreWorker.RecoverObjects - 2999 total (1 active), Execution time: mean = 8.533 us, total = 25.590 ms, Queueing time: mean = 52.406 us, max = 214.024 us, min = 13.610 us, total = 157.166 ms
	CoreWorker.ExitIfParentRayletDies - 301 total (1 active), Execution time: mean = 11.214 us, total = 3.376 ms, Queueing time: mean = 55.466 us, max = 106.296 us, min = 10.416 us, total = 16.695 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 180.727 us, total = 54.218 ms, Queueing time: mean = 53.953 us, max = 111.405 us, min = 16.087 us, total = 16.186 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 29.174 us, total = 8.752 ms, Queueing time: mean = 45.319 us, max = 223.661 us, min = 9.952 us, total = 13.596 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 781.858 us, total = 234.558 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 35.590 us, total = 2.135 ms, Queueing time: mean = 48.561 us, max = 105.086 us, min = 8.176 us, total = 2.914 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 5.679 us, total = 170.368 us, Queueing time: mean = 100.767 us, max = 745.644 us, min = 18.941 us, total = 3.023 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.822 us, total = 523.757 us, Queueing time: mean = 222.967 us, max = 489.888 us, min = 9.111 us, total = 1.561 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 574.876 us, total = 2.874 ms, Queueing time: mean = 40.294 us, max = 68.660 us, min = 38.397 us, total = 201.468 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.503 us, total = 17.006 us, Queueing time: mean = 36.142 us, max = 72.283 us, min = 72.283 us, total = 72.283 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.152 us, total = 520.152 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 107.768 us, total = 107.768 us, Queueing time: mean = 10.896 us, max = 10.896 us, min = 10.896 us, total = 10.896 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.714 us, total = 63.714 us, Queueing time: mean = 30.882 us, max = 30.882 us, min = 30.882 us, total = 30.882 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 472.090 us, total = 472.090 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 369.491 us, total = 369.491 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.744 us, total = 19.744 us, Queueing time: mean = 25.146 us, max = 25.146 us, min = 25.146 us, total = 25.146 us

-----------------
Task execution event stats:

Global stats: 29811 total (1 active)
Queueing time: mean = 53.709 us, max = 2.066 ms, min = -0.000 s, total = 1.601 s
Execution time:  mean = 15.198 us, total = 453.079 ms
Event stats:
	CoreWorker.CheckSignal - 29810 total (1 active), Execution time: mean = 15.199 us, total = 453.071 ms, Queueing time: mean = 53.710 us, max = 2.066 ms, min = -0.000 s, total = 1.601 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.847 us, total = 7.847 us, Queueing time: mean = 3.107 us, max = 3.107 us, min = 3.107 us, total = 3.107 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 35.609 us, max = 106.244 us, min = 7.703 us, total = 32.083 ms
Execution time:  mean = 344.407 us, total = 310.311 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 209.819 us, total = 62.946 ms, Queueing time: mean = 63.628 us, max = 102.995 us, min = 20.968 us, total = 19.088 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 787.341 us, total = 236.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 36.727 us, total = 11.018 ms, Queueing time: mean = 43.227 us, max = 106.244 us, min = 7.703 us, total = 12.968 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.637 us, total = 144.637 us, Queueing time: mean = 26.843 us, max = 26.843 us, min = 26.843 us, total = 26.843 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:48:59,703 I 660947 662093] core_worker.cc:902: Event stats:


Global stats: 5169 total (8 active)
Queueing time: mean = 49.773 us, max = 745.644 us, min = 8.176 us, total = 257.279 ms
Execution time:  mean = 77.565 us, total = 400.934 ms
Event stats:
	CoreWorker.RecoverObjects - 3598 total (1 active), Execution time: mean = 8.621 us, total = 31.020 ms, Queueing time: mean = 53.271 us, max = 214.024 us, min = 13.610 us, total = 191.670 ms
	CoreWorker.ExitIfParentRayletDies - 361 total (1 active), Execution time: mean = 11.513 us, total = 4.156 ms, Queueing time: mean = 56.173 us, max = 106.296 us, min = 10.416 us, total = 20.279 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 181.286 us, total = 65.263 ms, Queueing time: mean = 54.810 us, max = 111.405 us, min = 15.375 us, total = 19.732 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 29.470 us, total = 10.609 ms, Queueing time: mean = 45.400 us, max = 223.661 us, min = 9.952 us, total = 16.344 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 781.871 us, total = 281.473 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 35.929 us, total = 2.587 ms, Queueing time: mean = 50.903 us, max = 105.086 us, min = 8.176 us, total = 3.665 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 5.728 us, total = 206.215 us, Queueing time: mean = 100.438 us, max = 745.644 us, min = 18.941 us, total = 3.616 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 74.822 us, total = 523.757 us, Queueing time: mean = 222.967 us, max = 489.888 us, min = 9.111 us, total = 1.561 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 587.568 us, total = 3.525 ms, Queueing time: mean = 45.596 us, max = 72.109 us, min = 38.397 us, total = 273.577 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 8.503 us, total = 17.006 us, Queueing time: mean = 36.142 us, max = 72.283 us, min = 72.283 us, total = 72.283 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 520.152 us, total = 520.152 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 107.768 us, total = 107.768 us, Queueing time: mean = 10.896 us, max = 10.896 us, min = 10.896 us, total = 10.896 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 63.714 us, total = 63.714 us, Queueing time: mean = 30.882 us, max = 30.882 us, min = 30.882 us, total = 30.882 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 472.090 us, total = 472.090 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 369.491 us, total = 369.491 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 19.744 us, total = 19.744 us, Queueing time: mean = 25.146 us, max = 25.146 us, min = 25.146 us, total = 25.146 us

-----------------
Task execution event stats:

Global stats: 35771 total (1 active)
Queueing time: mean = 54.489 us, max = 8.022 ms, min = -0.000 s, total = 1.949 s
Execution time:  mean = 15.228 us, total = 544.716 ms
Event stats:
	CoreWorker.CheckSignal - 35770 total (1 active), Execution time: mean = 15.228 us, total = 544.708 ms, Queueing time: mean = 54.490 us, max = 8.022 ms, min = -0.000 s, total = 1.949 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 7.847 us, total = 7.847 us, Queueing time: mean = 3.107 us, max = 3.107 us, min = 3.107 us, total = 3.107 us

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 36.000 us, max = 106.244 us, min = 7.703 us, total = 38.916 ms
Execution time:  mean = 344.463 us, total = 372.364 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 208.395 us, total = 75.022 ms, Queueing time: mean = 63.280 us, max = 102.995 us, min = 20.968 us, total = 22.781 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 788.724 us, total = 283.941 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 36.824 us, total = 13.257 ms, Queueing time: mean = 44.745 us, max = 106.244 us, min = 7.703 us, total = 16.108 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 144.637 us, total = 144.637 us, Queueing time: mean = 26.843 us, max = 26.843 us, min = 26.843 us, total = 26.843 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-07-05 18:49:08,784 I 660947 662093] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660947 662093] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=81655757ee105ab29fe91f737a9df717933d86949aad6a5b97fd7e2d
[2025-07-05 18:49:08,784 I 660947 662093] core_worker.cc:5107: Number of alive nodes:0
[2025-07-05 18:49:08,879 I 660947 662093] raylet_client.cc:281: Error reporting task backlog information: RpcError: RPC Error message: Cancelling all calls; RPC Error details:  rpc_code: 14
