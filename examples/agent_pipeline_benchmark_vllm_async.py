import asyncio
import time
from datetime import datetime
from typing import Any, Dict

import ray
from vllm import <PERSON>plingParams

# Assuming the script is run from the project's root directory
from Ayo.dags.node_commons import NodeOps
from Ayo.engines.llm import LLMEngine

# --- Helper Actor for Collecting Results ---
# The provided LLMEngine is designed to call a scheduler back with results.
# Since we are not running the full Ayo scheduler, we create a simple actor
# to act as a result collector.

@ray.remote
class ResultCollector:
    """An actor to collect asynchronous results from the LLMEngine."""
    def __init__(self):
        self._results: Dict[str, Any] = {}
        self._events: Dict[str, asyncio.Event] = {}

    def _get_event(self, request_id: str) -> asyncio.Event:
        """Get or create an asyncio.Event for a given request_id."""
        if request_id not in self._events:
            self._events[request_id] = asyncio.Event()
        return self._events[request_id]

    async def on_result(self, request_id: str, query_id: str, result: Any):
        """Callback method for the LLMEngine to send results to."""
        # Store result with request_id as key (query_id is for grouping but not used here)
        self._results[request_id] = result
        self._get_event(request_id).set() # Signal that the result has arrived

    async def get_result(self, request_id: str) -> Any:
        """Wait for and retrieve a result for a given request_id."""
        await self._get_event(request_id).wait()
        return self._results.pop(request_id, None)

    def clear(self):
        """Clear all stored results and events."""
        self._results.clear()
        self._events.clear()

# --- Shared Resources and Prompts ---

def setup_shared_resources():
    """Defines the prompts and configurations used across all scenarios."""
    business_question = "Our company is considering entering the emerging AI education market, but we're uncertain about how to formulate an entry strategy. Please help analyze this business decision."

    # Agent 1 (Analyst) is tasked to generate a structured analysis
    agent1_prompt = f"""You are a senior business analyst. Please analyze the following business question in depth and output your analysis in the following 4 parts:

Business Question: {business_question}

Please output your analysis in the following format:

## 1. Problem Understanding
[Deep understanding and definition of the problem]

## 2. Key Factors Analysis
[Key factors and variables affecting the problem]

## 3. Solution Suggestions
[Specific solutions and implementation recommendations]

## 4. Risk Assessment
[Potential risks and mitigation strategies]

Begin analysis:"""

    # Agent 2 (Synthesizer) has a static part of its prompt (system instructions)
    agent2_static_prompt = f"""You are a senior business consultant. Based on the analyst's detailed analysis, you need to generate a comprehensive business consulting report for the client.

Original Business Question: {business_question}

Analyst's Analysis Results:
"""

    # And a template for the dynamic part that will be filled by Agent 1's output
    agent2_dynamic_prompt_template = """
{agent1_full_output}

Based on the above analysis, please generate a structured comprehensive business consulting report including:
1. Executive Summary
2. Comprehensive Analysis
3. Recommended Action Plan
4. Implementation Roadmap

Begin generating report:"""

    # Sampling parameters for the LLMs
    agent1_sampling_params = SamplingParams(temperature=0.1, top_p=0.9, max_tokens=1024, stop=["\n\n\n"])
    agent2_sampling_params = SamplingParams(temperature=0.7, top_p=0.9, max_tokens=1536)

    return {
        "agent1_prompt": agent1_prompt,
        "agent2_static_prompt": agent2_static_prompt,
        "agent2_dynamic_prompt_template": agent2_dynamic_prompt_template,
        "agent1_sampling_params": agent1_sampling_params,
        "agent2_sampling_params": agent2_sampling_params,
    }

def log_event(message: str, start_time: float, log_file=None):
    """Helper function to log events with timestamps."""
    elapsed = time.time() - start_time
    log_msg = f"[{elapsed:7.3f}s] {message}"
    print(log_msg)
    if log_file:
        log_file.write(log_msg + "\n")
        log_file.flush()

# --- SCENARIO IMPLEMENTATIONS ---

async def run_baseline_scenario(engine: ray.actor.ActorHandle, collector: ray.actor.ActorHandle, resources: Dict, log_file=None):
    """
    Scenario 1: Baseline (Sequential Execution)
    Agent 1 must fully complete its generation before Agent 2 can start.
    This demonstrates the classic, non-optimized approach.
    """
    print("\n" + "="*80)
    print("  SCENARIO 1: BASELINE (SEQUENTIAL EXECUTION)")
    print("="*80)

    # Clear any previous results
    await collector.clear.remote()

    # Unique IDs for this run
    agent1_id = "baseline-agent1"
    agent2_id = "baseline-agent2"

    # Start timer for the entire workflow
    start_time = time.time()
    log_event("Workflow started.", start_time, log_file)

    # --- Step 1: Execute Agent 1 and wait for its full completion ---
    log_event("Submitting request for Agent 1 to generate full analysis.", start_time, log_file)
    # The 'generate' method in Ayo's LLMEngine is a combination of prefill and decode.
    # We submit a single request and wait for the final result.
    await engine.submit_request.remote(
        request_id="agent1-decode", query_id="baseline-run", llm_internal_id=agent1_id,
        prompt=resources["agent1_prompt"], llm_op_type=NodeOps.LLM_DECODING,
        sampling_params=resources["agent1_sampling_params"]
    )

    # Wait until the collector receives the result from the engine
    agent1_full_output = await collector.get_result.remote("agent1-decode")
    log_event("Agent 1 has COMPLETELY FINISHED generation.", start_time, log_file)
    print(f"    - Agent 1 Output (first 50 chars): '{agent1_full_output[:50]}...'")

    # --- Step 2: Execute Agent 2 using the completed output from Agent 1 ---
    log_event("Submitting request for Agent 2, now that Agent 1 is done.", start_time, log_file)
    agent2_full_prompt = (
        resources["agent2_static_prompt"] + 
        resources["agent2_dynamic_prompt_template"].format(agent1_full_output=agent1_full_output)
    )
    
    # Submit Agent 2's request
    await engine.submit_request.remote(
        request_id="agent2-decode", query_id="baseline-run", llm_internal_id=agent2_id,
        prompt=agent2_full_prompt, llm_op_type=NodeOps.LLM_DECODING,
        sampling_params=resources["agent2_sampling_params"]
    )

    # Wait for the final report
    final_report = await collector.get_result.remote("agent2-decode")
    log_event("Agent 2 has FINISHED. Workflow complete.", start_time, log_file)
    print(f"    - Final Report (first 50 chars): '{final_report[:50]}...'")

    total_time = time.time() - start_time
    print(f"\n--- Baseline Scenario Total Time: {total_time:.3f} seconds ---")
    return total_time


async def run_ayo_partial_prefill_scenario(engine: ray.actor.ActorHandle, collector: ray.actor.ActorHandle, resources: Dict, log_file=None):
    """
    Scenario 2: Ayo's 2-Phase Partial-to-Full Prefill
    Agent 1 starts. In parallel, Agent 2's static system prompt is partially prefilled.
    Once Agent 1 is fully done, its output is used for a full prefill on Agent 2.
    """
    print("\n" + "="*80)
    print("  SCENARIO 2: AYO'S 2-PHASE PARTIAL-TO-FULL PREFILL")
    print("="*80)

    await collector.clear.remote()
    agent1_id = "ayo-2phase-agent1"
    agent2_id = "ayo-2phase-agent2"

    start_time = time.time()
    log_event("Workflow started.", start_time, log_file)

    # --- Step 1: Start Agent 1 and Agent 2's PARTIAL prefill in parallel ---
    log_event("Submitting Agent 1 (full task) and Agent 2 (partial prefill) IN PARALLEL.", start_time, log_file)
    
    # Submit Agent 1's full generation task. We don't wait for it yet.
    await engine.submit_request.remote(
        request_id="agent1-decode", query_id="ayo-2phase-run", llm_internal_id=agent1_id,
        prompt=resources["agent1_prompt"], llm_op_type=NodeOps.LLM_DECODING,
        sampling_params=resources["agent1_sampling_params"]
    )

    # Simultaneously, submit Agent 2's partial prefill request with only static content.
    await engine.submit_request.remote(
        request_id="agent2-partial-prefill", query_id="ayo-2phase-run", llm_internal_id=agent2_id,
        prompt=resources["agent2_static_prompt"], llm_op_type=NodeOps.LLM_PARTIAL_PREFILLING,
        sampling_params=resources["agent2_sampling_params"]
    )

    # Wait for the partial prefill to be accepted by the engine
    await collector.get_result.remote("agent2-partial-prefill")
    log_event("Agent 2 PARTIAL prefill for static prompt is complete. GPU now holds its KV cache.", start_time, log_file)

    # Now, wait for Agent 1 to fully complete its generation.
    log_event("Waiting for Agent 1 to finish its generation...", start_time, log_file)
    agent1_full_output = await collector.get_result.remote("agent1-decode")
    log_event("Agent 1 has COMPLETELY FINISHED generation.", start_time, log_file)

    # --- Step 2: Use Agent 1's output for Agent 2's FULL prefill ---
    log_event("Submitting Agent 1's output to Agent 2 for FULL prefilling.", start_time, log_file)
    agent2_dynamic_prompt = resources["agent2_dynamic_prompt_template"].format(agent1_full_output=agent1_full_output)
    
    # This request adds the dynamic prompt to the existing context of `agent2_id`.
    await engine.submit_request.remote(
        request_id="agent2-full-prefill", query_id="ayo-2phase-run", llm_internal_id=agent2_id,
        prompt=agent2_dynamic_prompt, llm_op_type=NodeOps.LLM_FULL_PREFILLING,
        sampling_params=resources["agent2_sampling_params"]
    )
    await collector.get_result.remote("agent2-full-prefill")
    log_event("Agent 2 FULL prefill is complete.", start_time, log_file)

    # --- Step 3: Final decoding for Agent 2 ---
    log_event("Submitting final DECODING request for Agent 2.", start_time, log_file)
    await engine.submit_request.remote(
        request_id="agent2-decode", query_id="ayo-2phase-run", llm_internal_id=agent2_id,
        prompt="", llm_op_type=NodeOps.LLM_DECODING,
        sampling_params=resources["agent2_sampling_params"]
    )

    final_report = await collector.get_result.remote("agent2-decode")
    log_event("Agent 2 has FINISHED. Workflow complete.", start_time, log_file)
    print(f"    - Final Report (first 50 chars): '{str(final_report)[:50]}...'")
    if log_file:
        log_file.write(f"    - Final Report (first 50 chars): '{str(final_report)[:50]}...'\n")

    total_time = time.time() - start_time
    print(f"\n--- Ayo 2-Phase Scenario Total Time: {total_time:.3f} seconds ---")
    return total_time


async def run_streaming_pipeline_scenario(engine: ray.actor.ActorHandle, collector: ray.actor.ActorHandle, resources: Dict, log_file=None):
    """
    Scenario 3: Full Streaming Pipeline
    Agent 1 streams its output chunk by chunk. As each chunk becomes available,
    it is immediately sent to Agent 2 for incremental prefilling.
    This demonstrates the most advanced level of pipelining.
    """
    print("\n" + "="*80)
    print("  SCENARIO 3: FULL STREAMING PIPELINE")
    print("="*80)

    await collector.clear.remote()
    agent1_id = "streaming-agent1"
    agent2_id = "streaming-agent2"

    start_time = time.time()
    log_event("Workflow started.", start_time, log_file)

    # --- Step 1: Start Agent 1 and Agent 2's PARTIAL prefill in parallel ---
    log_event("Submitting Agent 1 (streaming task) and Agent 2 (partial prefill) IN PARALLEL.", start_time, log_file)
    
    # Submit Agent 1's prefill request. This prepares it for streaming decoding.
    await engine.submit_request.remote(
        request_id="agent1-prefill", query_id="streaming-run", llm_internal_id=agent1_id,
        prompt=resources["agent1_prompt"], llm_op_type=NodeOps.LLM_PREFILLING,
        sampling_params=resources["agent1_sampling_params"]
    )
    await collector.get_result.remote("agent1-prefill")
    
    # Submit Agent 2's partial prefill request with static content.
    await engine.submit_request.remote(
        request_id="agent2-partial-prefill", query_id="streaming-run", llm_internal_id=agent2_id,
        prompt=resources["agent2_static_prompt"], llm_op_type=NodeOps.LLM_PARTIAL_PREFILLING,
        sampling_params=resources["agent2_sampling_params"]
    )
    await collector.get_result.remote("agent2-partial-prefill")
    log_event("Agent 1 and Agent 2 PARTIAL prefill complete. Pipeline is ready.", start_time, log_file)

    # --- Step 2: Simulate the streaming pipeline ---
    # In a real Ayo system, the Graph Scheduler would get callbacks. Here we simulate it.

    # This task will drive Agent 1's generation and stream out chunks
    agent1_generator_task = asyncio.create_task(
        _drive_agent1_streaming(engine, collector, agent1_id, resources)
    )

    # This task will listen for chunks and feed them to Agent 2
    agent2_consumer_task = asyncio.create_task(
        _consume_for_agent2_prefill(engine, collector, agent2_id, resources, start_time, log_file)
    )

    # Wait for the full pipeline to complete
    await asyncio.gather(agent1_generator_task, agent2_consumer_task)

    # --- Step 3: Final decoding for Agent 2 ---
    log_event("Submitting final DECODING request for Agent 2.", start_time, log_file)
    await engine.submit_request.remote(
        request_id="agent2-decode", query_id="streaming-run", llm_internal_id=agent2_id,
        prompt="", llm_op_type=NodeOps.LLM_DECODING,
        sampling_params=resources["agent2_sampling_params"]
    )
    final_report = await collector.get_result.remote("agent2-decode")
    log_event("Agent 2 has FINISHED. Workflow complete.", start_time, log_file)
    print(f"    - Final Report (first 50 chars): '{str(final_report)[:50]}...'")
    if log_file:
        log_file.write(f"    - Final Report (first 50 chars): '{str(final_report)[:50]}...'\n")

    total_time = time.time() - start_time
    print(f"\n--- Streaming Pipeline Scenario Total Time: {total_time:.3f} seconds ---")
    return total_time

async def _drive_agent1_streaming(engine, collector, agent1_id, resources):
    """Helper coroutine to simulate Agent 1 generating and 'yielding' chunks."""
    # This corresponds to the "leader" PARTIAL_DECODING request.
    await engine.submit_request.remote(
        request_id="agent1-stream-driver", query_id="streaming-run", llm_internal_id=agent1_id,
        prompt="", llm_op_type=NodeOps.LLM_PARTIAL_DECODING,
        sampling_params=resources["agent1_sampling_params"]
        # Note: partial_output_pattern is not supported in current LLMEngine implementation
    )
    # In the real engine, this request would now be actively generating.
    # The actual result is collected by the consumer task.
    # We just need to wait for it to signal completion.
    await collector.get_result.remote("agent1-stream-driver")


async def _consume_for_agent2_prefill(engine, collector, agent2_id, resources, start_time, log_file=None):
    """Helper coroutine to simulate scheduler receiving chunks and feeding Agent 2."""
    num_chunks = 4
    for i in range(num_chunks):
        # In a real system, this would be a callback. Here, we poll the collector.
        # The 'agent1-stream-driver' will put results here with keys like 'chunk_0', 'chunk_1', etc.
        # For this simulation, we'll use a simplified version where the driver signals one final completion.
        # To show the pipeline, we'll manually create the chunks and send them.

        # This is a mock of receiving a chunk.
        # In a real system, this chunk would be extracted from the vLLM stream.
        mock_chunk = f"\n\n## {i+1}. This is the analysis for part {i+1}."
        log_event(f"SCHEDULER: Received chunk {i+1} from Agent 1's stream.", start_time, log_file)

        is_last_chunk = (i == num_chunks - 1)
        prompt_chunk = mock_chunk
        if is_last_chunk:
            # Append the final instructions to the last chunk.
            prompt_chunk += resources["agent2_dynamic_prompt_template"].format(agent1_full_output="")
        
        # Submit the received chunk for incremental prefilling on Agent 2
        await engine.submit_request.remote(
            request_id=f"agent2-chunk-prefill-{i}", query_id="streaming-run", llm_internal_id=agent2_id,
            prompt=prompt_chunk, llm_op_type=NodeOps.LLM_FULL_PREFILLING,
            sampling_params=resources["agent2_sampling_params"]
        )
        await collector.get_result.remote(f"agent2-chunk-prefill-{i}")
        log_event(f"SCHEDULER: Sent chunk {i+1} to Agent 2 for incremental prefilling.", start_time, log_file)

        # Simulate time taken for Agent 1 to generate the next chunk
        if not is_last_chunk:
            await asyncio.sleep(2.0)

    log_event("SCHEDULER: All chunks from Agent 1 consumed and prefilled by Agent 2.", start_time, log_file)


# --- Main Execution Block ---

async def main():
    """Main function to run all scenarios and compare results."""
    # Create log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"benchmark_log_{timestamp}.txt"

    with open(log_filename, 'w') as log_file:
        log_file.write(f"Agent Pipeline Benchmark Log - {datetime.now()}\n")
        log_file.write("="*80 + "\n\n")

        ray.init(
            ignore_reinit_error=True,
            _temp_dir="/home/<USER>/von/Ayo/cache/ray",
            # _temp_dir="/data/ray_temp",
        )

        # Instantiate the result collector first
        collector = ResultCollector.remote()
        
        # num_gpus_to_use = 4
        # # Instantiate the LLM Engine actor
        # # Note: Ensure you have enough GPU memory for the model.
        # llm_engine = LLMEngine.options(num_gpus=num_gpus_to_use).remote(
        #     model_name="meta-llama/Llama-2-7b-chat-hf", 
        #     trust_remote_code=True, 
        #     scheduler_ref=collector,
        #     tensor_parallel_size=num_gpus_to_use
        #     ) # meta-llama/Llama-2-7b-chat-hf
  
        # Instantiate the LLM Engine actor
        # Note: Ensure you have enough GPU memory for the model.
        llm_engine = LLMEngine.remote(
            model_name="meta-llama/Llama-2-7b-chat-hf", 
            trust_remote_code=True, 
            scheduler_ref=collector
            ) # meta-llama/Llama-2-7b-chat-hf

        # Wait for engine to be ready
        print("Waiting for LLM Engine to complete its blocking initialization...")
        time.sleep(400) 
        print("LLM Engine should now be ready.")

        # Get shared prompts and configs
        resources = setup_shared_resources()

        # Run scenarios
        baseline_time = await run_baseline_scenario(llm_engine, collector, resources, log_file)
        time.sleep(5) # Cooldown period

        ayo_2phase_time = await run_ayo_partial_prefill_scenario(llm_engine, collector, resources, log_file)
        time.sleep(5) # Cooldown period

        # The streaming scenario is more complex to simulate perfectly without the actual scheduler
        # but this gives a conceptual demonstration.
        streaming_time = await run_streaming_pipeline_scenario(llm_engine, collector, resources, log_file)

        # --- Final Summary ---
        summary = [
            "\n\n" + "#"*80,
            "  PERFORMANCE SUMMARY",
            "#"*80,
            f"Scenario 1 (Baseline) Total Time: {baseline_time:>6.3f}s",
            f"Scenario 2 (Ayo 2-Phase) Total Time:  {ayo_2phase_time:>6.3f}s",
            f"Scenario 3 (Streaming) Total Time:    {streaming_time:>6.3f}s",
            "-"*80,
            f"Ayo 2-Phase Speedup vs Baseline:   {baseline_time / ayo_2phase_time:.2f}x",
            f"Streaming Speedup vs Baseline:   {baseline_time / streaming_time:.2f}x"
        ]

        for line in summary:
            print(line)
            log_file.write(line + "\n")

        log_file.write(f"\nBenchmark completed at: {datetime.now()}\n")

        # Shutdown
        await llm_engine.shutdown.remote()
        ray.shutdown()

    print(f"\nLog file saved as: {log_filename}")


if __name__ == "__main__":
    asyncio.run(main())