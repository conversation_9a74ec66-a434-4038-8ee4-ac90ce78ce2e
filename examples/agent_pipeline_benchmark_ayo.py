"""
AYO Example: Agent1 Partial Output Processing for Multi-Agent Collaboration

This example demonstrates how AYO handles the following scenario:
- Agent1 (Analyst LLM) analyzes a business problem and outputs 4 meaningful parts:
    1. Problem Understanding
    2. Key Factors Analysis
    3. Solution Suggestions
    4. Risk Assessment
- Agent2 (Synthesizer LLM) receives these 4 parts + system prompt to generate final comprehensive report

Key AYO Processing Mechanisms Demonstrated:
1. Partial Output Detection: Uses regex patterns to detect structured output sections
2. LLM Decoding Pipelining: Automatically decomposes decoding nodes into parallel sub-nodes
3. Node Dependency Management: Ensures proper data flow between agents
4. Performance Optimization: Reduces total latency through streaming and parallelization

vLLM Integration Points:
- Partial output detection during token generation using _check_partial_output()
- Parallel decoding coordination through shared llm_internal_id
- Streaming results to downstream nodes as soon as parts are available
"""

import asyncio
import time
import traceback
import uuid
from copy import deepcopy
from typing import List, Dict, Any

from Ayo.app import APP
from Ayo.configs.config import EngineConfig
from Ayo.dags.dag import DAG
from Ayo.dags.node import Node, NodeAnnotation, NodeIOSchema, NodeOps, NodeType
from Ayo.engines.engine_types import ENGINE_REGISTRY, EngineType
from Ayo.opt_pass.decoding_pipeling import LLMDecodingPipeliningPass
from Ayo.opt_pass.prefilling_split import PrefillingSpiltPass
from Ayo.opt_pass.pruning_dependency import PruningDependencyPass
from Ayo.opt_pass.stage_decomposition import StageDecompositionPass
from Ayo.queries.query import Query
from Ayo.utils import print_key_info


# Create LLM engine configuration
# This configuration will be used by AYO's LLMEngine (Ayo/engines/llm.py)
# which wraps vLLM's AsyncLLMEngine for distributed processing
default_llm_config = ENGINE_REGISTRY.get_default_config(EngineType.LLM)
default_llm_config.update({
    "model_name": "meta-llama/Llama-2-7b-chat-hf",
    "max_batch_size": 4,
})

llm_config = EngineConfig(
    name="llm_service",
    engine_type=EngineType.LLM,
    resources={},
    num_gpus=1,
    num_cpus=4,
    instances=1,
    model_config={**default_llm_config, "device": "cuda"},
    latency_profile={"timeout": 300, "batch_wait": 0.1},
)


def create_agent_partial_output_dag():
    """
    Create DAG where Agent1 outputs multiple parts to Agent2
    This function demonstrates AYO's core mechanisms:
    1. Node definition with partial output configuration
    2. Input/output schema mapping between agents
    3. Dependency relationship establishment
    4. Optimization-ready DAG structure
    """
    dag = DAG(dag_id="agent_partial_output_workflow")
    # Generate unique IDs for LLM coordination
    # These IDs will be used by vLLM's partial decoding mechanism
    agent1_llm_id = str(uuid.uuid4())
    agent2_llm_id = str(uuid.uuid4())
    
    # Agent1: Analyst LLM - Analyzes problem and outputs 4 parts
    # Configure Agent1 to use partial output for streaming meaningful sections
    # This leverages vLLM's _check_partial_output() mechanism in llm.py
    agent1_prefilling_node = Node(
        name="Agent1_Analyst_Prefilling",
        node_type=NodeType.COMPUTE,
        engine_type=EngineType.LLM,
        io_schema=NodeIOSchema(
            input_format={"business_question": str},
            output_format={"agent1_prefill_state": bool}
        ),
        op_type=NodeOps.LLM_PREFILLING,
        anno=NodeAnnotation.BATCHABLE,
        config={
            "prompt_template": """You are a senior business analyst. Please analyze the following business question in depth and output your analysis in the following 4 parts:

            Business Question: {business_question}

            Please output your analysis in the following format:

            ## 1. Problem Understanding
            [Deep understanding and definition of the problem]

            ## 2. Key Factors Analysis
            [Key factors and variables affecting the problem]

            ## 3. Solution Suggestions
            [Specific solutions and implementation recommendations]

            ## 4. Risk Assessment
            [Potential risks and mitigation strategies]

            Begin analysis:""",
            "parse_json": False,
            "partial_output": True,  # Key: Enable partial output detection
            "partial_prefilling": False,
            "llm_internal_id": agent1_llm_id,
            "max_tokens": 1024,
            # Configure partial output splitting pattern
            # This pattern will be used by vLLM's regex-based detection
            "partial_output_pattern": "## [0-9]+\\.",  # Matches "## 1.", "## 2." etc.
        },
    )
    
    agent1_decoding_node = Node(
        name="Agent1_Analyst_Decoding",
        node_type=NodeType.COMPUTE,
        engine_type=EngineType.LLM,
        io_schema=NodeIOSchema(
            input_format={"agent1_prefill_state": bool},
            output_format={
                "problem_understanding": str,    # Part 1: Problem Understanding
                "key_factors": str,             # Part 2: Key Factors Analysis
                "solution_suggestions": str,     # Part 3: Solution Suggestions
                "risk_assessment": str          # Part 4: Risk Assessment
            }
        ),
        op_type=NodeOps.LLM_DECODING,
        anno=NodeAnnotation.BATCHABLE,
        config={
            "prompt_template": """You are a senior business analyst. Please analyze the following business question in depth and output your analysis in the following 4 parts:

            Business Question: {business_question}

            Please output your analysis in the following format:

            ## 1. Problem Understanding
            [Deep understanding and definition of the problem]

            ## 2. Key Factors Analysis
            [Key factors and variables affecting the problem]

            ## 3. Solution Suggestions
            [Specific solutions and implementation recommendations]

            ## 4. Risk Assessment
            [Potential risks and mitigation strategies]

            Begin analysis:""",
            "parse_json": False,
            "partial_output": True,  # Key: Enable partial output for streaming
            "partial_prefilling": False,
            "llm_internal_id": agent1_llm_id,
            "max_tokens": 1024,
            # Configure how partial outputs map to different output fields
            # This will be processed by LLMDecodingPipeliningPass optimization
            "partial_output_mapping": {
                0: "problem_understanding",
                1: "key_factors",
                2: "solution_suggestions",
                3: "risk_assessment"
            }
        },
    )

    # Node 2A: Partial Prefilling for Agent2's static system prompt.
    # This node can run as soon as the workflow starts, in parallel with Agent1.
    agent2_partial_prefilling_node = Node(
        name="Agent2_Synthesizer_Partial_Prefilling",
        node_type=NodeType.COMPUTE,
        engine_type=EngineType.LLM,
        io_schema=NodeIOSchema(
            input_format={"business_question": str},  # Only needs the initial query input
            output_format={"agent2_partial_prefill_state": bool} # Outputs a state handle
        ),
        op_type=NodeOps.LLM_PARTIAL_PREFILLING, # Use the specific op_type
        anno=NodeAnnotation.BATCHABLE,
        config={
            # Prompt contains ONLY the static part (system prompt, instructions)
            "prompt_template": """You are a senior business consultant. Based on the analyst's detailed analysis, you need to generate a comprehensive business consulting report for the client.

            Original Business Question: {business_question}

            Analyst's Analysis Results:
            """,
            "parse_json": False,
            "partial_output": False,
            "partial_prefilling": True, # Key: Enable partial prefilling
            "llm_internal_id": agent2_llm_id,
            "max_tokens": 1536,
        },
    )

    # Node 2B: Full Prefilling for the dynamic parts received from Agent1.
    # This node appends the analysis from Agent1 to the already prefilled context.
    agent2_full_prefilling_node = Node(
        name="Agent2_Synthesizer_Full_Prefilling",
        node_type=NodeType.COMPUTE,
        engine_type=EngineType.LLM,
        io_schema=NodeIOSchema(
            input_format={
                # Depends on Agent1's outputs AND Agent2's partial prefill state
                "agent2_partial_prefill_state": bool,
                "problem_understanding": str,
                "key_factors": str,
                "solution_suggestions": str,
                "risk_assessment": str
            },
            output_format={"agent2_prefill_state": bool}
        ),
        op_type=NodeOps.LLM_FULL_PREFILLING, # Use the specific op_type
        anno=NodeAnnotation.BATCHABLE,
        config={
            # Prompt now contains ONLY the dynamic parts
            "prompt_template": """
            Problem Understanding:
            {problem_understanding}

            Key Factors Analysis:
            {key_factors}

            Solution Suggestions:
            {solution_suggestions}

            Risk Assessment:
            {risk_assessment}

            Based on the above analysis, please generate a structured comprehensive business consulting report including:
            1. Executive Summary
            2. Comprehensive Analysis
            3. Recommended Action Plan
            4. Implementation Roadmap

            Begin generating report:""",
            "parse_json": False,
            "partial_output": False,
            "partial_prefilling": True, # Key: Mark as part of a partial prefilling sequence
            "llm_internal_id": agent2_llm_id,
            "max_tokens": 1536,
        },
    )
    
    # The original agent2_decoding_node remains the same, as it triggers after all prefilling is done.
    agent2_decoding_node = Node(
        # ... (agent2_decoding_node code is exactly the same as you provided) ...
        name="Agent2_Synthesizer_Decoding",
        op_type=NodeOps.LLM_DECODING,
        io_schema=NodeIOSchema(input_format={"agent2_prefill_state": bool}, output_format={"final_report": str}),
        config={"llm_internal_id": agent2_llm_id, "max_tokens": 1536, "prompt_template": "..."}
    )

    # Connect nodes: Agent1 -> Agent2
    agent1_prefilling_node >> agent1_decoding_node

    # NEW DEPENDENCIES:
    # Agent2's partial prefill node has no dependency on Agent1 and can run in parallel.
    
    # Agent2's full prefill depends on BOTH Agent1's decoding AND Agent2's partial prefill.
    agent1_decoding_node >> agent2_full_prefilling_node
    agent2_partial_prefilling_node >> agent2_full_prefilling_node

    # The final decoding depends on the full prefilling being complete.
    agent2_full_prefilling_node >> agent2_decoding_node

    # Register all nodes to the DAG
    dag.register_nodes(
        agent1_prefilling_node,
        agent1_decoding_node,
        agent2_partial_prefilling_node, # Register new node
        agent2_full_prefilling_node,    # Register new node
        agent2_decoding_node,
    )

    # Set query inputs
    dag.set_query_inputs({
        "business_question": "Our company is considering entering the emerging AI education market, but we're uncertain about how to formulate an entry strategy. Please help analyze this business decision."
    })
    
    return dag


async def process_query(app, query_input: str, dag, query_id: str):
    """Process query through the multi-agent workflow"""
    try:
        query = Query(
            uuid=f"agent-partial-output-{query_id}",
            query_id=f"agent-partial-output-{query_id}",
            query_inputs={"business_question": query_input},
            DAG=deepcopy(dag),
        )
        
        future = await app.submit_query(query=query, timeout=300)
        result = await asyncio.wait_for(future, timeout=300)
        return result
    
    except Exception as e:
        print(f"Query {query_id} processing failed:\n{traceback.format_exc()}")
        raise Exception(f"Query {query_id} processing failed: {str(e)}")


async def run_app(dag, query):
    """
    Run the application demonstrating AYO's multi-agent partial output processing

    This function shows the complete workflow:
    1. App initialization and engine registration
    2. Query submission and processing
    3. Results collection and display
    """
    try:
        # Initialize application
        app = APP.init()
        app.register_engine(llm_config)

        # Start application - this initializes the LLMEngine with vLLM
        await app.start()
        await asyncio.sleep(5)

        result = await process_query(app, query, dag, "1")

        print("\n=== Agent Partial Output Workflow Results ===")
        print("Agent1 (Analyst) Partial Outputs:")
        for key, value in result.items():
            if key.startswith("agent1_"):
                print(f"{key}:\n{value}\n")

        print("Agent2 (Synthesizer) Final Report:")
        print(result["final_report"])

    except Exception as e:
        print(f"Main program error:\n{traceback.format_exc()}")
        raise
    finally:
        try:
            await cleanup(app)
        except Exception as e:
            print(f"Cleanup error: {e}")


async def cleanup(app):
    """Clean up application resources"""
    try:
        await app.stop()
        app.shutdown()
    except Exception as e:
        print(f"Cleanup failed: {str(e)}")


if __name__ == "__main__":
    import os
    from Ayo.vis.vis_graph import visualize_dag_with_node_types

    # Create DAG
    dag = create_agent_partial_output_dag()

    print("=== AYO Agent Partial Output Example ===")
    print("Original DAG Node Information:")
    print(dag.get_full_dag_nodes_info())

    # Visualize original DAG
    visualize_dag_with_node_types(dag, "before_optimize_agent_partial_output.png")
    if os.path.exists("before_optimize_agent_partial_output.png"):
        os.remove("before_optimize_agent_partial_output.png")

    # Optimize DAG - This is where AYO's magic happens
    print("\n=== Starting DAG Optimization ===")
    begin_time = time.time()
    dag.optimize([
        PruningDependencyPass(),           # Remove unnecessary dependencies
        StageDecompositionPass(),          # Decompose stages for parallelization
        PrefillingSpiltPass(),             # Split prefilling into partial/full phases
        LLMDecodingPipeliningPass(),       # Key optimization: Enable partial output pipelining
    ])
    end_time = time.time()

    print_key_info(f"Optimization time: {end_time - begin_time} seconds")
    print("Optimized DAG Node Information:")
    print(dag.get_full_dag_nodes_info())

    # Visualize optimized DAG
    visualize_dag_with_node_types(dag, "optimized_agent_partial_output.png")
    if os.path.exists("optimized_agent_partial_output.png"):
        os.remove("optimized_agent_partial_output.png")

    # Run application
    print("\n=== Starting Workflow Execution ===")
    # Test query
    test_query = "Our company is considering entering the emerging AI education market, but we're uncertain about how to formulate an entry strategy. Please help analyze this business decision."
    asyncio.run(run_app(dag, test_query))
